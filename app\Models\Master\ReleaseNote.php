<?php

namespace App\Models\Master;

use App\Models\Common\Attachment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\ReleaseNote
 *
 * @property int $id
 * @property string $version
 * @property string $title
 * @property string $description
 * @property string $post_date
 * @property array|null $tags
 * @property string|null $contributors
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property array|null $roles
 * @property string|null $app_name
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Attachment> $attachment
 * @property-read int|null $attachment_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Master\ReleaseNoteReceiver> $receiver
 * @property-read int|null $receiver_count
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote query()
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereAppName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereContributors($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote wherePostDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereRoles($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereTags($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNote whereVersion($value)
 * @mixin \Eloquent
 */
class ReleaseNote extends Model
{
    use HasFactory;

    protected $casts = [
        'roles' => 'array',
        'tags' => 'array',
    ];

    /**
     * The attributes that are mass assignable.
     */
    protected $guarded = [];

    public function receiver()
    {
        return $this->hasMany(ReleaseNoteReceiver::class, 'release_noted_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function attachment()
    {
        return $this->hasMany(Attachment::class, 'source_id', 'id')
            ->where("type", "ReleaseNote");
    }
}
