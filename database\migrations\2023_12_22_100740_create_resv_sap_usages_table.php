<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateResvSapUsagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('resv_sap_usages', function (Blueprint $table) {
            $table->id();
            $table->string('division', 150);
            $table->string('work_location', 150);
            $table->string('movement_type', 150);
            $table->string('description');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('resv_sap_usages');
    }
}
