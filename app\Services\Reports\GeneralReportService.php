<?php

namespace App\Services\Reports;

use App\Models\Resv\ResvHeader;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class GeneralReportService
{
    public function show($request, $date_from, $date_to, $item_type, $request_type, $created_by)
    {
        $rows = ResvHeader::select(
            "resv_headers.DocNum",
            "resv_headers.DocDate",
            "resv_headers.RequiredDate",
            "resv_headers.WhsCode",
            DB::raw('resv_headers."RequestType" as "HeaderRequestType"'),
            "resv_headers.ItemType",
            "resv_details.ItemCode",
            "resv_details.ItemName",
            "resv_details.AssetCode",
            "resv_details.AssetName",
            "resv_details.UoMCode",
            "resv_details.ReqQty",
            "resv_details.ReqNotes",
            "resv_details.LineNum",
            "resv_details.RequestType",
            "resv_headers.RequesterName",
            "resv_headers.WorkLocation",
            "resv_headers.DocStatus",
            DB::raw('
                CASE
                    WHEN resv_headers."DocStatus" = \'D\' THEN \'Draft\'
                    WHEN resv_headers."DocStatus" = \'C\' THEN \'Cancel\'
                    WHEN resv_headers."RequestType" = \'Restock\' AND resv_headers."DocStatus" = \'O\' THEN \'Open\'
                    WHEN resv_headers."DocStatus" = \'O\' THEN \'Open\'
                END AS "DocumentStatus"
            '),
            DB::raw('
                CASE
                    WHEN resv_headers."ApprovalStatus" = \'W\' THEN \'Waiting\'
                    WHEN resv_headers."ApprovalStatus" = \'P\' THEN \'Pending\'
                    WHEN resv_headers."ApprovalStatus" = \'N\' THEN \'Rejected\'
                    WHEN resv_headers."ApprovalStatus" = \'Y\' THEN \'Approved\'
                    WHEN resv_headers."ApprovalStatus" = \'-\' THEN \'-\'
                END AS "AppStatus"
            ')
        )
            ->leftJoin("resv_details", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            ->whereRaw("resv_headers.\"DocDate\" BETWEEN '${date_from}' AND '${date_to}' ")
            ->whereRaw("resv_headers.\"ItemType\" LIKE '%${item_type}%'")
            ->whereRaw("resv_details.\"RequestType\" LIKE '%${request_type}%' ")
            ->where("resv_headers.ApprovalStatus", "=", "Y")
            ->when($request, function ($query) use ($request) {
                if (!$request->user()->hasAnyRole(['Superuser'])) {

                    $query->where("resv_headers.WorkLocation", "=", $request->user()->location);
                } else {
                    $query->where("resv_headers.WorkLocation", "=", $request->user()->location);
                }
            });

        if (
            Str::contains($request->user()->department, ['WAREHOUSE', 'SAP'])
            || $request->user()->hasAnyRole(['Admin Warehouse', 'Superuser'])
        ) {
            // if ($request->user()->hasAnyRole('Superuser')) {
            //     $rows = $rows
            //         ->whereIn("resv_headers.WhsCode", ["MW-IT", "MW-ZZ"])
            //         // ->orWhereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
            //     ->orderBy("resv_headers.DocNum")
            //     ->get();
            // } else {

            //     $rows = $rows->orderBy("resv_headers.DocNum")
            //     ->get();
            // }

            $rows = $rows->orderBy("resv_headers.DocNum")
                ->get();
        } else {
            if ($request->user()->hasAnyRole('View Data IT')) {
                $rows = $rows
                    ->whereIn("resv_headers.WhsCode", ["MW-IT", "MW-ZZ", 'IG03'])
                    // ->orWhereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
                    ->orderBy("resv_headers.DocNum")
                    ->get();
            } else {
                $rows = $rows->whereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
                    ->orderBy("resv_headers.DocNum")
                    ->get();
            }
        }

        $service = new ReportService();
        return $service->getS4Docs($rows);
    }

    public function header()
    {
        return [
            'DocNum',
            'Doc Date',
            'Req Date',
            'WH',
            'Request Type',
            'Item type',
            'Item Code',
            'Item Name',
            'Asset Code',
            'Asset Name',
            'UoM',
            'Request Qty',
            'Notes',
            'Type',
            'Requester',
            'GIR NO',
            'PR NO',
            'PO NO',
            'GRPO NO',
            'GI NO',
            'Transfer No',
            'Doc Status',
            'App Status'
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "DocNum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "RequiredDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "WhsCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "HeaderRequestType",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemType",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],

            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "AssetCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "AssetName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "UoMCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ReqQty",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ReqNotes",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "RequestType",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "RequesterName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_GIRNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_PRNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "PONum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "GRPONum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_GINo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_TrfNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocumentStatus",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "AppStatus",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value->DocNum,
                $value->DocDate,
                $value->RequiredDate,
                $value->WhsCode,
                $value->HeaderRequestType,
                $value->ItemType,
                $value->ItemCode,
                $value->ItemName,
                $value->AssetCode,
                $value->AssetName,
                $value->UoMCode,
                $value->ReqQty,
                $value->ReqNotes,
                $value->RequestType,
                $value->RequesterName,
                $value->SAP_GIRNo,
                $value->SAP_PRNo,
                $value->PONum,
                $value->GRPONum,
                $value->SAP_GINo,
                $value->SAP_TrfNo,
                $value->DocumentStatus,
                $value->AppStatus,
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
