<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGoodsIssuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $array = ['goods_issues', 'goods_receipts'];
        foreach ($array as $item) {
            Schema::create($item, function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('doc_number');
                $table->unsignedBigInteger('created_by');
                $table->unsignedBigInteger('updated_by')->nullable();
                $table->dateTime('post_date');
                $table->string('notes');
                $table->string('status', 10)->default('DRAFT');
                $table->string('whs_code', 50);
                $table->timestamps();

                $table->index(['created_by', 'updated_by']);
                $table->index(['post_date']);
                $table->index(['whs_code']);
            });
        }

        $array = ['goods_issue_details', 'goods_receipt_details'];
        foreach ($array as $item) {
            Schema::create($item, function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('master_item_id');
                $table->unsignedBigInteger('header_id');
                $table->string('item_code', 50);
                $table->string('item_name');
                $table->string('uom');
                $table->double('qty', 12, 2);
                $table->string('notes');
                $table->string('whs_code', 50);
                $table->unsignedBigInteger('created_by');
                $table->unsignedBigInteger('updated_by')->nullable();
                $table->timestamps();

                $table->index(['created_by', 'updated_by']);
                $table->index(['master_item_id', 'header_id']);
                $table->index(['item_code', 'item_name']);
                $table->index(['whs_code']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('goods_issues');
        Schema::dropIfExists('goods_receipts');
        Schema::dropIfExists('goods_issue_details');
        Schema::dropIfExists('goods_receipt_details');
    }
}
