<?php

namespace App\Models\Resv;

use App\Models\Common\Attachment;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\ReqItem
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem query()
 * @property int $U_DocEntry
 * @property string|null $U_Description
 * @property string|null $U_UoM
 * @property string|null $U_Status
 * @property string|null $U_Remarks
 * @property string|null $U_Supporting
 * @property string|null $U_CreatedBy
 * @property string|null $U_Comments
 * @property string|null $U_ItemType
 * @property string|null $U_CreatedAt
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property int $DocNum
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Attachment> $attachment
 * @property-read int|null $attachment_count
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereDocNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUDocEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUItemType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereURemarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUSupporting($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUUoM($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReqItem whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ReqItem extends Model
{
    public $timestamps = false;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $primaryKey = 'U_DocEntry';
    protected $connection = 'sqlsrv';
    protected $guarded = [];
    protected $table = 'resv_req_items';

    public function attachment()
    {
        return $this->hasMany(Attachment::class, 'source_id', 'DocNum')
            ->where("type", "=", "item");
    }
}
