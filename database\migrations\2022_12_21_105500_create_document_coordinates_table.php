<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDocumentCoordinatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('document_coordinates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('document_id');
            $table->string('materai_page', 3)->nullable();
            $table->string('meterai_coordinate', 100)->nullable();
            $table->string('meterai_id', 100)->nullable();
            $table->string('meterai_stamp_type', 100)->nullable();
            $table->string('meterai_page_index', 100)->nullable();
            $table->string('meterai_top', 100)->nullable();
            $table->string('meterai_left', 100)->nullable();
            $table->string('meterai_width', 100)->nullable();
            $table->string('meterai_height', 100)->nullable();
            $table->string('vis_llx', 100)->nullable();
            $table->string('vis_lly', 100)->nullable();
            $table->string('vis_urx', 100)->nullable();
            $table->string('vis_ury', 100)->nullable();
            $table->string('digisign_coordinate', 100)->nullable();
            $table->string('digisign_id', 100)->nullable();
            $table->string('digisign_stamp_type', 100)->nullable();
            $table->string('digisign_page_index', 100)->nullable();
            $table->string('digisign_top', 100)->nullable();
            $table->string('digisign_left', 100)->nullable();
            $table->string('digisign_width', 100)->nullable();
            $table->string('digisign_height', 100)->nullable();
            $table->string('sign_page', 3)->nullable();
            $table->string('vis_digisign_llx', 100)->nullable();
            $table->string('vis_digisign_lly', 100)->nullable();
            $table->string('vis_digisign_urx', 100)->nullable();
            $table->string('vis_digisign_ury', 100)->nullable();
            $table->timestamps();
        });

        Schema::table('documents', function (Blueprint $table) {
            $table->string('approver', 100)->nullable();
            $table->string('sign_payment', 2)->default('2');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('document_coordinates');
    }
}
