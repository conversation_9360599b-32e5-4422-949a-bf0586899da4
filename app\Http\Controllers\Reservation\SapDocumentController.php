<?php

namespace App\Http\Controllers\Reservation;

use App\Http\Controllers\Controller;
use App\Services\SapS4Service;
use Illuminate\Http\Request;

class SapDocumentController extends Controller
{
    public function index(Request $request)
    {
        $docNum = $request->docNum;
        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();
        $flow = $service->getFlow(
            $docNum
        );
        $dataFlow = [];
        if ($flow) {
            if (array_key_exists('DATA', $flow) && $flow['DATA'] != 'NULL') {
                foreach ($flow['DATA'] as $index => $item) {
                    $dataFlow[] = [
                        "DocNum" => (array_key_exists('ERESERVASI', $flow['DATA'][$index])) ? $flow['DATA'][$index]['ERESERVASI'] : null,
                        "SAP_GIRNo" => (array_key_exists('RESERVASI', $flow['DATA'][$index])) ? $flow['DATA'][$index]['RESERVASI'] : null,
                        "SAP_PRNo" => (array_key_exists("PR", $flow['DATA'][$index])) ? $flow['DATA'][$index]['PR'] : null,
                        "PONum" => (array_key_exists("PO", $flow['DATA'][$index])) ? $flow['DATA'][$index]['PO'] : null,
                        "GRPONum" => (array_key_exists("GR", $flow['DATA'][$index])) ? $flow['DATA'][$index]['GR'] : null,
                        "SAP_GINo" => (array_key_exists("GI", $flow['DATA'][$index])) ? $flow['DATA'][$index]['GI'] : null
                    ];
                }
            }
        }

        return $this->success($dataFlow);
    }
}
