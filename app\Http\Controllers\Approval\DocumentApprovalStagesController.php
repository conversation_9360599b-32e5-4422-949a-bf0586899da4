<?php

namespace App\Http\Controllers\Approval;

use App\Http\Controllers\Controller;
use App\Models\Approval\ApprovalStage;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DocumentApprovalStagesController extends Controller
{
    /**
     * Retrieves approval stages based on the provided user and status.
     *
     * @param Request $request The request object containing the user's ID and status.
     * @return JsonResponse The JSON response containing the retrieved approval stages.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $approval = ApprovalStage::with(['document.userCreate', 'document.attachment', 'document.coordinate', 'user', 'approval'])
            ->whereHas('approval.approver', function ($query) use ($request) {
                if (!$request->user()->hasAnyRole('Superuser')) {
                    $query->whereIn('user_id', [$request->user()->id]);
                }
            })
            ->distinct()
            ->where('status', $request->status_approval)
            ->when($user, function($query) use ($user) {
                if (!$user->hasAnyRole(['Superuser'])) {
                    $query->where("user_id", $user->id);
                }
            })
            // ->where("user_id", $request->user()->id)
            ->get();

        return $this->success([
            'rows' => $approval,
            'total' => count($approval),
            'document_status' => ['pending', 'approved', 'rejected'],
            'search_item' => ['Document Number', 'Document Type'],
        ]);
    }
}
