<?php

namespace App\Models\Common;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Common\LogDocOwnerChange
 *
 * @property int $id
 * @property string $old_owner
 * @property string $new_owner
 * @property string $efective_date
 * @property string $notes
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange query()
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange whereEfectiveDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange whereNewOwner($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange whereOldOwner($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocOwnerChange whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class LogDocOwnerChange extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
