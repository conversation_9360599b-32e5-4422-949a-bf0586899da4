<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Master\ReleaseNote;
use App\Models\Master\ReleaseNoteReceiver;
use App\Models\Paper\Role;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReleaseNoteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $rows = ($request->itemsPerPage) ? $request->itemsPerPage : 20;
        $sort = isset($request->sortDesc[0]) ? $request->sortBy[0]['key'] : 'post_date';
        $order = isset($request->sortDesc[0]) ? $request->sortBy[0]['order'] : 'desc';
        $app_name = (isset($request->app_name)) ? $request->app_name : '';

        $result = [];

        $query = ReleaseNote::with(['user', 'receiver'])
            ->where('app_name', 'like', '%' . $app_name . '%')
            ->with(['attachment', 'user'])
            ->orderBY($sort, $order);
        $result['total'] = $query->count();
        $data = $query->paginate($rows)
            ->items();

        // $data = collect($data);
        // $data->transform(function ($item) {
        //     $item->roles = array($item->roles);
        //     return $item;
        // });

        $result = array_merge($result, [
            'rows' => $data,
        ]);
        return $this->success($result);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return JsonResponse
     */
    public function store(Request $request)
    {
        DB::beginTransaction();
        $data = new ReleaseNote();
        try {
            $data->title = $request->form['title'];
            $data->description = $request->form['description'];
            $data->version = $request->form['version'];
            $data->app_name = $request->form['app_name'];
            $data->roles = $request->form['roles'];
            $data->tags = $request->form['tags'];
            $data->post_date = date('Y-m-d H:i:s', strtotime($request->form['post_date'] . ':' . date('s')));
            $data->created_by = $request->user()->id;
            $data->updated_at = Carbon::now();

            $update = $data->save();

            $this->storeReleaseNoteReceiver($request->form['roles'], $data);

            DB::commit();
            $this->success([
                'data' => $data
            ], 'Data Updated');
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \App\Exceptions\CustomException($e->getMessage());
        }
    }

    public function storeReleaseNoteReceiver($role, $releaseNote)
    {
        if ($role) {
            ReleaseNoteReceiver::where('release_noted_id', $releaseNote->id)
                ->whereNull('read_at')
                ->delete();

            foreach ($role as $key => $value) {
                $id = array_key_exists('id', (array) $value) ? $value['id'] : $value;
                $role_id = Role::where('id', '=', $id)->first();

                $users = User::whereHas('roles', function ($query) use ($role_id) {
                    $query->where('name', $role_id->name);
                })
                    ->where("active", "Y")
                    // ->select("username", "name")
                    ->get();

                // throw new \Exception(json_encode($users));
                foreach ($users as $user) {
                    ReleaseNoteReceiver::create([
                        'release_noted_id' => $releaseNote->id,
                        'user_id' => $user->id
                    ]);
                }
            }
        }
    }


    /**
     * Display the specified resource.
     *
     * @param int $id
     */
    public function show(Request $request, $id): JsonResponse
    {
        $user = $request->user();
        $data = ReleaseNote::orderBy('id', 'desc')
            ->whereHas('receiver', function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->whereNull('read_at');
            })
            ->where('app_name', $request->app_name)
            ->with(['attachment', 'user'])
            ->first();

        return $this->success($data);
    }

    public function markAsRead(Request $request)
    {
        $releaseNoteId = $request->releaseNoteId;
        $userId = $request->user()->id;

        ReleaseNoteReceiver::where("release_noted_id", $releaseNoteId)
            ->where("user_id", $userId)
            ->update([
                'read_at' => now()
            ]);

        return $this->success([], 'Data updated');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     *
     * @return JsonResponse
     */
    public function update(Request $request, $id)
    {
        $data = ReleaseNote::where('id', '=', $id)->first();
        DB::beginTransaction();
        try {
            $data->title = $request->form['title'];
            $data->version = $request->form['version'];
            $data->description = $request->form['description'];
            $data->app_name = $request->form['app_name'];
            $data->roles = $request->form['roles'];
            $data->tags = $request->form['tags'];
            $data->post_date = date('Y-m-d H:i:s', strtotime($request->form['post_date']));
            $data->created_by = $request->user()->id;
            $data->updated_at = Carbon::now();

            $update = $data->save();

            $this->storeReleaseNoteReceiver($request->form['roles'], $data);

            DB::commit();
            return $this->success([
                'data' => $data
            ], 'Data Updated');
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \App\Exceptions\CustomException($e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     */
    public function destroy($id)
    {
        try {
            $check = ReleaseNote::findOrFail($id);
            $check->delete();

            return $this->success([], 'Data deleted');
        } catch (\Exception $e) {
            throw new \App\Exceptions\CustomException($e->getMessage(), $e->getCode(), $e);
        }
    }
}
