<?php

namespace App\Http\Controllers;

use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use App\Traits\ConnectHana;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
    use ApiResponse;
    use ConnectHana;
    use AppConfig;
}
