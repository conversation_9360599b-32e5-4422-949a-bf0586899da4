<?php

namespace App\Http\Controllers\Common;

use App\Models\Common\SafetyData;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SafetyDataController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = SafetyData::select(
            "U_DocEntry",
            "date_out",
            "id_card",
            "employee_name",
            "item_code",
            "item_name",
            "qty",
            "uom",
            "company",
            "department",
            "notes"
        )
            ->orderBy('date_out', 'desc')
            ->get();

        if (count($data) < 1) {
            $data = [
                [
                    "U_DocEntry" => null,
                    "date_out" => null,
                    "id_card" => null,
                    "employee_name" => null,
                    "item_code" => null,
                    "item_name" => null,
                    "qty" => null,
                    "uom" => null,
                    "company" => null,
                    "department" => null,
                    "notes" => null
                ]
            ];
        }

        return $this->success([
            'rows' => $data,
            'columns' => [
                [
                    'data' => 'U_DocEntry',
                    'width' => 50,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'date_out',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'id_card',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'employee_name',
                    'width' => 60,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'item_code',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'item_name',
                    'width' => 90,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'qty',
                    'width' => 20,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'uom',
                    'width' => 20,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'company',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'department',
                    'width' => 40,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'notes',
                    'width' => 100,
                    'wordWrap' => false,
                ],
            ],
            'header' => ['Id', 'DATE OUT', 'ID CARD', 'EMPLOYEE', 'ITEM CODE', 'ITEM NAME', 'QTY', 'UOM', 'COMPANY', 'DEPARTMENT', 'NOTES'],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        try {
            foreach ($details as $detail) {
                $data = [
                    'date_out' => date('Y-m-d', strtotime($detail['date_out'])),
                    'id_card' => $detail['id_card'],
                    'employee_name' => $detail['employee_name'],
                    'item_code' => $detail['item_code'],
                    'item_name' => $detail['item_name'],
                    'qty' => (isset($detail['qty'])) ? $detail['qty'] : 1,
                    'uom' => $detail['uom'],
                    'company' => $detail['company'],
                    'department' => $detail['department'],
                    'notes' => $detail['notes'],
                    'created_at' => date('Y-m-d'),
                    'updated_at' => date('Y-m-d'),
                ];

                $brand = SafetyData::where('U_DocEntry', '=', $detail['U_DocEntry'])->first();

                if (!$brand) {
                    DB::connection('sqlsrv')
                        ->table('safety_data')
                        ->insert($data);
                } else {
                    DB::connection('sqlsrv')
                        ->table('safety_data')
                        ->where('U_DocEntry', '=', $detail['U_DocEntry'])
                        ->update($data);
                }
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /* Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = SafetyData::where('category', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param Config $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Config $productBrand)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            SafetyData::whereIn('U_DocEntry', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
