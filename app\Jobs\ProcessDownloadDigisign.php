<?php

namespace App\Jobs;

use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use App\Traits\DocumentHelper;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class ProcessDownloadDigisign implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use AppConfig;
    use ApiResponse;
    use DocumentHelper;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;
    protected $document;
    protected $fileName;
    protected $userId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($document, $fileName, $userId)
    {
        $this->document = $document;
        $this->fileName = $fileName;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $document = $this->document;
        $file_name = $document->attachment->file_name;

        if ($document->digisign_coordinate == 'Y') {
            if (date('Y-m-d', strtotime($document->created_at)) < '2022-06-27') {
                $doc_id = Str::slug(strtoupper($document->document_number));
            } else {
                $doc_id = Str::slug(strtoupper($document->external_document_number));
            }

            // there are no digisign error message, next step process the download file
            $esign_user_id = $this->getConfigByName('UserIdEsign', 'ESIGN', $document->company);
            $token = $this->getConfigByName('TokenEsign', 'ESIGN', $document->company);

            $url = $this->getConfigByName('DownloadBase64', 'ESIGN');
            $options = [
                'jsonfield' => json_encode(
                    [
                        'JSONFile' => [
                            // "userid" => $user_id,
                            'userid' => $esign_user_id,
                            'document_id' => $doc_id,
                            // "document_id" => "SNI7",
                        ],
                    ]
                ),
            ];

            $headers[] = "Authorization: Bearer ${token}";
            $curl = curl_init();
            curl_setopt($curl, CURLOPT_HEADER, false);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            // curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($curl, CURLOPT_VERBOSE, 1);
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_TIMEOUT, 95);
            // timeout in seconds
            curl_setopt($curl, CURLOPT_POSTFIELDS, $options);

            $response = curl_exec($curl);
            curl_close($curl);
            // dd($options);
            // dd(config('app.sign_url_download'));
            $response_text = json_decode($response);
            // return $this->error('', 422, $response);
            // dd($response);
            $collect_response = collect($response_text);

            // $contents = base64_decode($response->collect()['JSONFile']['file']);
            $contents = base64_decode($collect_response['JSONFile']->file);
            // $file_name = $doc_id . '_DOWNLOAD.pdf';
            $path_download = public_path('documents/' . $file_name);
            // file_put_contents($path_download, $contents);

            custom_disk_put('documents/' . $file_name, $contents);
            // Remove Attachment
            // RemoveAttachment::dispatch($path_download)->delay(now()->addMinutes(30));

            $this->createLog(
                $document->id,
                'document',
                'Digisign - Download document ID ' . $doc_id . ', userid : ' . $esign_user_id,
                $this->userId
            );
        }
    }
}
