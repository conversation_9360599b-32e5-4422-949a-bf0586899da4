<?php

namespace App\Http\Middleware;

use App\Models\Settings\PostingPeriod;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;

class PostingPeriodMiddleware
{
    use ApiResponse;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
     */
    public function handle(Request $request, Closure $next)
    {
        if ($request->post_date) {
            $month = date('m', strtotime($request->post_date));
            $year = date('Y', strtotime($request->post_date));
            $postingPeriod = PostingPeriod::whereMonth('start_period', $month)
                ->whereYear('start_period', $year)
                ->first();

            if (!$postingPeriod) {
                return $this->error('No posting period for ' . Carbon::parse($request->post_date)->format('Y-m-d'));
            } elseif ($postingPeriod->is_locked == 'Yes') {
                return $this->error('Transaction for posting period ' . Carbon::parse($request->post_date)->format('Y-m-d') . ' is locked!');
            } else {
                return $next($request);
            }
        } else {
            return $this->error('Posting date is required!');
        }
    }
}
