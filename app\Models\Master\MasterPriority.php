<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\MasterPriority
 *
 * @property int $id
 * @property string $title
 * @property string|null $slug
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPriority newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPriority newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPriority query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPriority whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPriority whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPriority whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPriority whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPriority whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterPriority extends Model
{
    use HasFactory;

    protected $table = 'master_periority';

    protected $guarded = [];

    protected $connection = 'sqlsrv';
}
