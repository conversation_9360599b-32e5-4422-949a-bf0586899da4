<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSignLoggingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sign_loggings', function (Blueprint $table) {
            $table->id();
            $table->string('id_document')->nullable();
            $table->string('serial_number')->nullable();
            $table->text('file_path')->nullable();
            $table->text('file_name')->nullable();
            $table->text('payload_request')->nullable();
            $table->text('payload_response')->nullable();
            $table->string('response_code', 3)->nullable();
            $table->string('status')->nullable(); //STAMP, NOT_STAMP, FAILED, USED
            $table->timestamp('request_time')->nullable();
            $table->timestamp('response_time')->nullable();
            $table->string('batch_id')->nullable();
            $table->unsignedInteger('request_sn')->nullable();
            $table->unsignedInteger('sequence')->nullable();
            $table->string('log_type')->default('request'); // request, callback
            $table->string('request_type')->default('SN'); // SN, STAMP
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sign_loggings');
    }
}
