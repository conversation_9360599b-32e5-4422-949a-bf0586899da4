<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('resv_headers', function (Blueprint $table) {
            $table->index(["CreateDate", "U_DocEntry"]);
        });
        Schema::table('resv_cost_centers', function (Blueprint $table) {
            $table->index(["division", "work_location", "cc_code", "created_by"]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('resv_headers', function (Blueprint $table) {
            //
        });
    }
};
