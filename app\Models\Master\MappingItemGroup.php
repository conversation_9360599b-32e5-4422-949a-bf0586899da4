<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\MappingItemGroup
 *
 * @property int $id
 * @property string $item_group_b1
 * @property string $item_group_s4
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|MappingItemGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingItemGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingItemGroup query()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingItemGroup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingItemGroup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingItemGroup whereItemGroupB1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingItemGroup whereItemGroupS4($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingItemGroup whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MappingItemGroup extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';

    protected $guarded = [];
}
