<?php

namespace App\Models\Resv;

use App\Models\Inventory\InventoryDetail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * App\Models\Resv\ResvDetail
 *
 * @property int $LineEntry
 * @property int $U_DocEntry
 * @property int|null $LineNum
 * @property string|null $ItemCode
 * @property string|null $ItemName
 * @property string|null $WhsCode
 * @property string|null $UoMCode
 * @property string|null $UoMName
 * @property float|null $ReqQty
 * @property string|null $ReqDate
 * @property string|null $ReqNotes
 * @property int|null $OtherResvNo
 * @property string|null $RequestType
 * @property float|null $QtyReadyIssue
 * @property string|null $LineStatus
 * @property int|null $SAP_GIRNo
 * @property int|null $SAP_TrfNo
 * @property int|null $SAP_PRNo
 * @property string|null $ItemCategory
 * @property int|null $OIGRDocNum
 * @property string|null $InvntItem
 * @property string|null $U_ATTACH
 * @property string|null $AssetCode
 * @property string|null $AssetName
 * @property string|null $ItemGroup
 * @property string|null $SubGroup
 * @property float|null $AvailableQty
 * @property string|null $AvailableQtyDate
 * @property string|null $U_Department
 * @property string|null $U_Period
 * @property string|null $U_Category
 * @property string|null $U_AppResBy
 * @property int|null $SAP_SONo
 * @property string|null $VehicleNo
 * @property string|null $Mileage
 * @property string|null $EmployeeName
 * @property string|null $EmployeeId
 * @property string|null $OrderId
 * @property float|null $OnHand
 * @property string|null $U_ItemType
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail query()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereAssetCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereAssetName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereAvailableQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereAvailableQtyDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereEmployeeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereEmployeeName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereInvntItem($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereItemCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereItemCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereItemGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereItemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereLineEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereLineNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereLineStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereMileage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereOIGRDocNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereOnHand($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereOtherResvNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereQtyReadyIssue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereReqDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereReqNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereReqQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereRequestType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereSAPGIRNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereSAPPRNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereSAPSONo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereSAPTrfNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereSubGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUATTACH($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUAppResBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUDocEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUItemType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUoMCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUoMName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereVehicleNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvDetail whereWhsCode($value)
 * @property-read \App\Models\Resv\ResvHeader|null $header
 * @property-read InventoryDetail|null $issueReceipt
 * @mixin \Eloquent
 */
class ResvDetail extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    use HasFactory;
    protected $primaryKey = 'LineEntry';
    protected $connection = 'sqlsrv';
    protected $table = 'resv_details';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    public function header()
    {
        return $this->belongsTo(ResvHeader::class, 'U_DocEntry', 'U_DocEntry');
    }

    public function issueReceipt()
    {
        return $this->belongsTo(InventoryDetail::class, 'LineEntry', 'resv_detail_id');
    }
}
