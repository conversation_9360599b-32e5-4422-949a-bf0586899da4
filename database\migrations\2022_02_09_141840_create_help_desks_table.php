<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHelpDesksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('master_sub_category', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->nullable();
            $table->string('category');
            $table->timestamps();
        });


        Schema::create('master_priority', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->nullable();
            $table->timestamps();
        });

        Schema::create('task_section', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->nullable();
            $table->smallInteger('order_line')->default(0);
            $table->unsignedBigInteger('user_id');
            $table->timestamps();
        });

        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('department');
            $table->dateTime('due_date')->nullable();
            $table->smallInteger('order_line')->default(0);
            $table->unsignedBigInteger('section_id');
            $table->unsignedBigInteger('sub_category_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('periority_id')->nullable();
            $table->timestamps();
        });

        Schema::create('task_sub_category', function (Blueprint $table) {
            $table->unsignedBigInteger('sub_category_id');
            $table->unsignedBigInteger('task_id');
        });

        Schema::create('task_comments', function (Blueprint $table) {
            $table->id();
            $table->text('content')->nullable();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('task_id');
            $table->timestamps();
        });

        Schema::create('task_tag_people', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('task_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('master_category');
        Schema::dropIfExists('master_sub_category');
        Schema::dropIfExists('master_priority');
        Schema::dropIfExists('task_section');
        Schema::dropIfExists('tasks');
        Schema::dropIfExists('task_sub_category');
        Schema::dropIfExists('task_comments');
        Schema::dropIfExists('task_tag_people');
    }
}
