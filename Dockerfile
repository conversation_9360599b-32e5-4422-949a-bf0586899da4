# Used for prod build.
FROM php:8.2-fpm AS php

ENV APP_HOME /opt/laravel

# Install dependencies.
RUN apt-get update && apt-get install -y \
    unzip \
    libpq-dev \
    libcurl4-gnutls-dev \
    nginx \
    cron \
    libonig-dev \
    supervisor \
    zlib1g-dev \
    libpng-dev \
    libzip-dev \
    zip \
    python3 \
    python3-pip \
    libsodium-dev \
    imagemagick \
    libmagickwand-dev \
    gnupg \
    curl \
    gettext-base \
    --no-install-recommends && rm -rf /var/lib/apt/lists/*

# Install Node.js and npm
# RUN curl -sL https://deb.nodesource.com/setup_20.x | bash - && \
#     apt-get install -y nodejs

# Install chokidar
# RUN npm install -g  chokidar

# Install PHP extensions.
RUN docker-php-ext-install mysqli \
    pdo \
    pdo_mysql \
    bcmath \
    curl \
    opcache \
    sodium \
    mbstring \
    gd \
    zip \
    ftp \
    pcntl

# Install and enable Imagick extension.
RUN pecl install imagick \
    && docker-php-ext-enable imagick

# Install Open Swoole
# RUN pecl install openswoole \
#     && docker-php-ext-enable openswoole

# Output the content of the PHP extensions directory for debugging.
RUN ls -la /usr/local/lib/php/extensions/no-debug-non-zts-*

# Install Microsoft Drivers for PHP for SQL Server.
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update \
    && ACCEPT_EULA=Y apt-get install -y msodbcsql17 mssql-tools unixodbc-dev \
    && pecl install sqlsrv pdo_sqlsrv \
    && docker-php-ext-enable sqlsrv pdo_sqlsrv \
    && rm -rf /var/lib/apt/lists/*

# Clean up build dependencies and cache.
RUN apt-get purge -y build-essential \
    && apt-get autoremove -y \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set working directory.
WORKDIR /opt/laravel

# Install composer.
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Copy Nginx configuration template.
COPY conf.d/nginx/default.conf /etc/nginx/nginx.conf.template

# Copy PHP configuration.
COPY conf.d/php-fpm/php-fpm.conf /usr/local/etc/php-fpm.d/www.conf
COPY conf.d/php/php.ini /usr/local/etc/php/conf.d/php.ini
COPY conf.d/php/opcache.ini /usr/local/etc/php/conf.d/opcache.ini

# Copy Supervisor configuration.
COPY conf.d/supervisor/supervisord.conf /etc/supervisord.conf

# Copy Laravel application files.
COPY . /opt/laravel

# Explicitly create necessary directories before setting permissions.
RUN mkdir -p /opt/laravel/storage \
    && mkdir -p /opt/laravel/bootstrap/cache

# Set up permissions for Laravel directories.
RUN chown -R www-data:www-data /opt/laravel \
    && chmod -R 775 /opt/laravel/storage \
    && chmod -R 755 /opt/laravel/bootstrap/cache \
    && chmod -R 775 /opt/laravel/public

# Fix files ownership.
RUN chown -R www-data:www-data $APP_HOME/storage
RUN chown -R www-data:www-data $APP_HOME/public
RUN chown -R www-data:www-data $APP_HOME/storage/framework
RUN chown -R www-data:www-data $APP_HOME/storage/framework/sessions

# Set correct permissions.
RUN chmod -R 775 $APP_HOME/storage
RUN chmod -R 775 $APP_HOME/storage/logs
RUN chmod -R 775 $APP_HOME/storage/framework
RUN chmod -R 775 $APP_HOME/storage/framework/sessions
RUN chmod -R 775 $APP_HOME/bootstrap
RUN chmod -R 775 $APP_HOME/public

# Ensure permissions for the whole /opt/laravel directory.
RUN chown -R www-data:www-data /opt/laravel \
    && chmod -R 775 /opt/laravel

# Set up permissions for Laravel directories (simplified).
RUN chown -R www-data:www-data /opt/laravel \
    && chmod -R 775 /opt/laravel/storage \
    && chmod -R 775 /opt/laravel/bootstrap/cache \
    && chmod -R 775 /opt/laravel/public

# Create a log file.
RUN touch /var/log/cron.log

# Expose ports.
EXPOSE 80

# Add entrypoint script.
ADD entrypoint.sh /root/entrypoint.sh
RUN chmod +x /root/entrypoint.sh

ENTRYPOINT ["/root/entrypoint.sh"]
