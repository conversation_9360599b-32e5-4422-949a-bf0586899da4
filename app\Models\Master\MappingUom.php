<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\MappingUom
 *
 * @property int $id
 * @property string $uom_code_b1
 * @property string $uom_code_s4
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|MappingUom newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingUom newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingUom query()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingUom whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingUom whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingUom whereUomCodeB1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingUom whereUomCodeS4($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingUom whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MappingUom extends Model
{
    use HasFactory;
}
