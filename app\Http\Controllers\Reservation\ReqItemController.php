<?php

namespace App\Http\Controllers\Reservation;

use App\Http\Controllers\Controller;
use App\Models\Common\Attachment;
use App\Models\Resv\ReqItem;
use App\Traits\ConnectHana;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Services\SapS4Service;
use Illuminate\Support\Facades\Log;
use PDO;

class ReqItemController extends Controller
{
    use ConnectHana;

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $options = json_decode($request->options);
        $year_local = date('Y');
        $pages = isset($request->page) ? (int) $request->page : 1;
        $filter = isset($request->filter) ? (string) $request->filter : $year_local;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "U_Description";
        $order = isset($request->sortBy[0]) ? (string) $options->sortDesc[0] : "desc";
        $search_status = isset($request->searchStatus) ? (string) $request->searchStatus : "";
        $search_item = isset($request->searchItem) ? (string) $request->searchItem : "";
        $search = isset($request->search) ? (string) $request->search : "";
        $offset = $pages;

        if ($search_status == 'All') {
            $search_status = '';
        }

        $createdName = $request->user();

        $result = array();
        $sap_db = (config('app.env') == 'local') ? 'IMIP_TEST' : 'IMIP_LIVE';
        $db_name = $sap_db;
        // $connect = $this->connectHana();

        $own_db = (config('app.env') == 'local') ? 'IMIP_ERESV_TEST' : 'IMIP_ERESV_LIVE';
        $own_db_name = $own_db;

        $query = ReqItem::withCount('attachment')
            // ->where("U_Status", "LIKE", "%" . $search_status . "%")
            ->when($search_item, function ($query) use ($search, $search_item) {
                switch ($search_item) {
                    case 'Specification':
                        $query->where("U_Description", "LIKE", "%" . $search . '%');
                        break;

                    // case 'Item Code':
                    //     $query->where("ItemCode",  "LIKE", "%" . $search . '%');
                    //     break;

                    // case 'Item Name':
                    //     $query->where("U_Description", "LIKE", "%" . $search . '%');
                    //     break;

                    case 'Created By':
                        $query->where("U_CreatedBy", "LIKE", "%" . $search . '%');
                        break;
                }
            })->when($createdName, function ($query) use ($createdName) {
                if (!$createdName->hasAnyRole(['Superuser', 'Admin Warehouse', 'User Can Show Req Item'])) {
                    $query->where("U_CreatedBy", $createdName->name);
                }
            });

        // $result["total"] = $query->count();

        $all_data = $query->orderBY('DocNum', 'desc')
            ->paginate($row_data)
            ->items();

        $arr = [];
        // $statement = $connection2->query($sql);
        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();

        $reqNo = collect($all_data)->pluck('DocNum')->toArray();

        $status = $service->getStatusRequestItem($reqNo);
        $datastatus = [];
        if ($status) {
            // throw new \Exception(json_encode($status));
            if (array_key_exists('DATA', $status)) {
                if ($status['DATA'] != 'NULL') {
                    foreach ($status['DATA'] as $index => $item) {
                        $datastatus[] = [
                            "DocNum" => (array_key_exists('MATREQNUM', $status['DATA'][$index])) ? (int) $status['DATA'][$index]['MATREQNUM'] : null,
                            "ItemName" => (array_key_exists('MATDESC1', $status['DATA'][$index])) ? $status['DATA'][$index]['MATDESC1'] : null,
                            "ItemCode" => (array_key_exists("MATNR", $status['DATA'][$index])) ? $status['DATA'][$index]['MATNR'] : null,
                            "U_DocStatus" => (array_key_exists("MATNR", $status['DATA'][$index])) ? 'Approved' : 'Pending',
                        ];
                    }
                }
            }
        }

        $collectionFix = collect($datastatus);

        foreach ($all_data as $key => $value) {
            $arr[] = [
                "U_Description" => $value->U_Description,
                "U_UoM" => $value->U_UoM,
                "U_Status" => $value->U_Status,
                "U_Remarks" => $value->U_Remarks,
                "U_Supporting" => $value->U_Supporting,
                "U_CreatedBy" => $value->U_CreatedBy,
                "DocNum" => $value->DocNum,
                "U_Comments" => $value->U_Comments,
                "U_CreatedAt" => $value->U_CreatedAt,
                "U_ItemType" => $value->U_ItemType,
                // "ItemCode" => $rowStatusItemCode,
                // "ItemName" => $rowStatusItemName,
                "U_DocStatus" => 'Pending',
                "count_attachment" => Attachment::where('source_id', '=', $value->DocNum)
                    ->where('type', '=', 'item')
                    ->count()
            ];
        }

        $mergedArray = [];

        foreach ($arr as $item1) {
            $docEntry = $item1['DocNum'];

            // Find corresponding item in array2 based on DocNum
            $matchingItem = collect($datastatus)->where('DocNum', $docEntry)->first();

            if ($matchingItem) {
                // Merge the two items
                $mergedItem = array_merge($item1, $matchingItem);
                $mergedArray[] = $mergedItem;
            } else {
                $mergedArray[] = $item1;
            }
        }

        $collection = collect($mergedArray);

        switch ($search_item) {
            case 'Item Code':
                $collection = ($search) ? $collection->where("ItemCode", $search) : $collection;
                break;

            case 'Item Name':
                $collection = ($search) ? $collection->where("ItemName", $search) : $collection;
                break;
        }

        $result["total"] = $query->count();


        $result = array_merge($result, [
            "rows" => $collection->values(),
            "merge" => $collection->count(),
            'documentStatus' => [
                'All',
                'Pending',
                'Approved',
                'Cancel'
            ],
            'filter' => [
                'Item Name',
                'Item Code',
                'Specification',
                'UoM',
                'Created By'
            ]
        ]);
        return response()->json($result);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        if ($this->validation($request)) {
            return response()->json([
                "errors" => true,
                "validHeader" => true,
                "message" => $this->validation($request)
            ], 422);
        }

        $form = $request->form;
        // return response()->json($this->generateDocNum(date('Y-m-d H:i:s')), 422);
        try {
            $data = new ReqItem();
            $data->U_Description = $form['U_Description'];
            $data->U_ItemType = $form['U_ItemType'];
            $data->U_UoM = array_key_exists('U_UoM', $form) ? $form['U_UoM'] : '';
            $data->U_Status = array_key_exists('U_Status', $form) ? ((!empty($form['U_Status'])) ? $form['U_Status'] : 'Pending') : 'Pending';
            $data->U_Remarks = $form['U_Remarks'];
            $data->U_Supporting = $form['U_Supporting'];
            $data->U_CreatedBy = $request->user()->name;
            $data->U_CreatedAt = now();
            $data->DocNum = $this->generateDocNum(date('Y-m-d H:i:s'));
            $data->save();

            return $this->success([
                "errors" => false,
            ], "Data inserted!");
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $sysDate
     * @return string
     */
    protected function generateDocNum($sysDate)
    {
        $data_date = strtotime($sysDate);
        $year_val = date('y', $data_date);
        $full_year = date('Y', $data_date);
        $month = date('m', $data_date);
        $day_val = date('j', $data_date);
        $end_date = date('t', $data_date);

        if ($day_val == 1) {
            $docnum = (int) $year_val . $month . sprintf("%04s", "1");
            $check_docnum = ReqItem::where('DocNum', '=', $docnum)->first();
            if (!$docnum) {
                return (int) $year_val . $month . sprintf("%04s", "1");
            } else {
                $first_date = "$full_year-$month-01";
                $second_date = "$full_year-$month-$end_date";
                $doc_num = ReqItem::selectRaw('ISNULL(DocNum, 0) as DocNum')
                    ->whereBetween(DB::raw('CONVERT(varchar,U_CreatedAt,23)'), [$first_date, $second_date])
                    ->orderBy("U_CreatedAt", "DESC")
                    ->first();
                $number = (empty($doc_num)) ? '00000000' : $doc_num->DocNum;
                $clear_doc_num = (int) substr($number, 4, 7);
                $number = $clear_doc_num + 1;
                return (int) $year_val . $month . sprintf("%04s", $number);
            }
        } else {
            $first_date = "$full_year-$month-01";
            $second_date = "$full_year-$month-$end_date";
            $doc_num = ReqItem::selectRaw('ISNULL(DocNum, 0) as DocNum')
                ->whereBetween(DB::raw('CONVERT(varchar,U_CreatedAt,23)'), [$first_date, $second_date])
                ->orderBy("DocNum", "DESC")
                ->first();
            $number = (empty($doc_num)) ? '00000000' : $doc_num->DocNum;
            $clear_doc_num = (int) substr($number, 4, 7);
            $number = $clear_doc_num + 1;
            return (int) $year_val . $month . sprintf("%04s", $number);
        }
    }

    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $messages = [
            'form.U_Description' => 'Name is required!',
            // 'form.U_UoM' => 'Description Status is required!',
            'form.U_ItemType' => 'Item Type is required!',
        ];

        $validator = Validator::make($request->all(), [
            'form.U_Description' => 'required',
            // 'form.U_UoM' => 'required',
            'form.U_ItemType' => 'required',
        ], $messages);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): \Illuminate\Http\JsonResponse
    {
        $data = ReqItem::where("DocNum", "=", $id)->get();
        return response()->json([
            'rows' => $data
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->validation($request)) {
            return response()->json([
                "errors" => true,
                "validHeader" => true,
                "message" => $this->validation($request)
            ], 422);
        }

        $form = $request->form;
        try {
            $query = DB::connection('sqlsrv')
                ->table('resv_req_items')
                ->where("DocNum", "=", $form['DocNum']);

            $count = $query->count();

            if ($count > 0) {
                $query->update([
                    'U_ItemType' => $form['U_ItemType'],
                    'U_Description' => $form['U_Description'],
                    'U_UoM' => array_key_exists('U_UoM', $form) ? $form['U_UoM'] : '',
                    'U_Status' => array_key_exists('U_Status', $form) ? $form['U_Status'] : 'Pending',
                    'U_Remarks' => $form['U_Remarks'],
                    'U_Supporting' => $form['U_Supporting'],
                ]);

                return $this->success([
                    "errors" => false,
                ], "Data updated!");
            } else {
                return $this->error('Document not found!', '404');
            }
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): \Illuminate\Http\JsonResponse
    {
        $details = ReqItem::where("DocNum", "=", $id)->first();
        if ($details) {
            ReqItem::where("DocNum", "=", $id)->delete();
            return response()->json([
                'message' => 'Row deleted'
            ]);
        }
        return response()->json([
            'message' => 'Row not found'
        ]);
    }
}
