<?php

namespace App\Http\Controllers\Approval;

use App\Http\Controllers\Controller;
use App\Models\Approval\BatchApproval;
use App\Models\Common\Attachment;
use App\Models\Document\Document;
use App\Services\ApprovalActionService;
use App\Services\ChangeSignService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Throwable;

class BatchApprovalController extends Controller
{
    /**
     * @param Request $request
     * @param $document_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchStatus(Request $request, $document_id)
    {
        $batch = BatchApproval::where('document_id', $document_id)
            ->select('document_id', 'name', 'status', 'callback_message', 'callback_trace')
            ->distinct()
            ->get();
        return $this->success([
            'rows' => $batch
        ]);
    }

    /**
     * @param Request $request
     * @param $doc_id
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     */
    public function reProcessBatch(Request $request, $doc_id): JsonResponse
    {
        try {
            $document = Document::find($doc_id);

            if (isset($request->type)) {
                if ($request->type == 'revision') {
                    $externalDocumentNumber = $document->external_document_number;
                    $referenceNumber = $document->reference_number;
                    if (!Str::contains($externalDocumentNumber, '-REV')) {
                        $document->external_document_number = $externalDocumentNumber . '-REV1';
                        if ($referenceNumber) {
                            $document->reference_number = $referenceNumber . 'REV1';
                            $document->document_token = null;
                        }
                        $document->save();
                    } else {
                        $document->external_document_number = ++$externalDocumentNumber;
                        if ($referenceNumber) {
                            $document->reference_number = ++$referenceNumber;
                            $document->document_token = null;
                        }
                        $document->save();
                    }
                }
            }

            $document = Document::find($doc_id);

            $service = new ApprovalActionService();

            // if ($document->internal_document == 'Y') {

            if ($request->user()->hasAnyRole(['Admin E-Meterai Internal'])) {
                $fileName = Attachment::where('source_id', $document->id)
                    ->where('type', 'peruri')
                    ->first();

                Document::where('id', $doc_id)
                    ->update([
                        'status' => 'approved - on process'
                    ]);

                $service->processInternalDocument($document, $fileName, $doc_id);
            } else {
                $fileName = Attachment::where('source_id', $document->id)
                    ->where('type', 'peruri')
                    ->first();

                Document::where('id', $doc_id)
                    ->update([
                        'status' => 'approved - on process',
                        'document_token' => null,
                        'privy_status' => ''
                    ]);


                $batch_approval = BatchApproval::where('document_id', $doc_id)->pluck('callback_message');
                BatchApproval::where('document_id', $doc_id)->delete();

                $checkUseDigisign = $this->getConfigByName('UseDigisign', 'GENERAL');

                $userId = $request->user()->id;
                // change approval sign provider
                $changeSign = new ChangeSignService();
                $changeSign->changeSign($document, $fileName, $userId, $doc_id, $batch_approval, $checkUseDigisign);
            }

            return $this->success("document's processed, please wait!");
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }
}
