<?php

namespace App\Traits;

use App\Jobs\RemoveAttachment;
use App\Models\Document\Document;
use App\Services\QrCodeService;
use setasign\Fpdi\Fpdi;

trait ManualSignHelpder
{
    public function process($document, $fileName, $batch)
    {
        $document = Document::find($document->id);
        create_file_delete_job('/Attachment/docs/' . $fileName->file_name);
        $pdf_file = public_path('/Attachment/docs/' . $fileName->file_name);
        $fpdi = new Fpdi('P', 'pt');
        $qrCodeService = new QrCodeService();

        $dataCheck = [
            'document_id' => $document->id,
            'name' => 'Submit Digital Sign',
        ];

        try {
            $qrCodeService->generatePath(public_path() . '/images/qrcode/');
            $count = $fpdi->setSourceFile($pdf_file);
            for ($i = 1; $i <= $count; $i++) {
                $template = $fpdi->importPage($i);
                $size = $fpdi->getTemplateSize($template);
                $fpdi->AddPage($size['orientation'], array($size['width'], $size['height']));
                $fpdi->useTemplate($template);

                foreach ($document->coordinate as $key => $value) {
                    if ($value->sign_page == $i) {
                        $file_export_name = $document->str_url;
                        $qr_file = config('app.front_url') . '/verification?str=' . $file_export_name;
                        $qrCodePath = 'images/qrcode/' . $file_export_name . '.png';
                        $qrCodeService->generateQrCode($qr_file, $file_export_name, 300);


                        $x = $value->digisign_left;
                        $y = $value->digisign_top;
                        $width = $value->vis_digisign_urx - $value->vis_digisign_llx;
                        $height = $value->vis_digisign_ury - $value->vis_digisign_lly;

                        $qrimage = public_path($qrCodePath);
                        $speciment = public_path('images/approval/' . $document->approver->id . '.png');
                        $newname = public_path('images/speciment/' . $document->approver->id . '.png');

                        $qrCodeService->mergeImage($qrimage, $speciment, $newname, 65, 65, 65, 65, $file_export_name);

                        $fpdi->Image(public_path('images/speciment/' . $document->approver->id . '.png'), $x, $y, 65, 65);
                        // $fpdi->Image(public_path($qrCodePath), $x, $y, 65, 65);

                        RemoveAttachment::dispatch($qrimage)->delay(now()->addMinutes(15));
                    }
                }
            }
            $qrCodeService->generatePath(public_path() . '/documents/');
            $outputFilePath = public_path('/documents/' . $fileName->file_name);
            $fpdi->Output($outputFilePath, 'F');

            RemoveAttachment::dispatch($outputFilePath)->delay(now()->addMinutes(15));

            $dataUpdate = [
                'batch_id' => $batch->id,
                'status' => 'Processing ' . $batch->progress() . '%',
                'callback_message' => 'Success',
                'callback_trace' => 'Success Submit Manual Sign'
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        } catch (\Exception $exception) {
            $dataUpdate = [
                'batch_id' => $batch->id,
                'status' => 'Processing ' . $batch->progress() . '%',
                'callback_message' => 'Failed manual sign: ' . $exception->getMessage(),
                'callback_trace' => ''
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            $this->fail($exception);
            throw new \Exception('MANUAL SIGN: ' . $exception->getMessage(), 1);
        }
    }
}
