<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Jobs\RemoveAttachment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use <PERSON>nes<PERSON>\Madzipper\Madzipper as Zipper;
use RuntimeException;
use Illuminate\Support\Facades\Log;
use App\Services\CheckStatusPrivyService;

class DocumentDownloadEkbController extends Controller
{
    public function index(Request $request)
    {
        try {
            $document_id = $request->document_id;
            $bc_type = $request->bc_type;
            $batch = DB::connection('sqlsrv4')
                ->table('batch_approvals')
                ->where('document_id', $document_id)
                ->where('bc_type', $bc_type)
                ->get();


            $all_pdf = [];
            foreach ($batch as $key => $row) {
                $form_draft = json_decode(json_decode($row->value));
                $file_name = $form_draft->file_names;
                create_file_delete_job('documents/' . $file_name);
                $all_pdf[] = public_path('documents/' . $file_name);
            }

            $zipper = new Zipper();
            $zip_path = public_path() . '/documents/';
            if (!file_exists($zip_path)) {
                if (!mkdir($zip_path, 0777, true) && !is_dir($zip_path)) {
                    throw new RuntimeException(sprintf(
                        'Directory "%s" was not created',
                        $zip_path
                    ));
                }
            }
            $file_name = 'EKB-BATCH' . date('YmdHis') . '.zip';
            $data_file = public_path('documents/' . $file_name);
            if (file_exists($data_file)) {
                unlink($data_file);
            }
            $zipper->make($data_file)->add($all_pdf);
            $zipper->close();

            RemoveAttachment::dispatch([$data_file])->delay(now()->addMinutes(30));

            $base64 = chunk_split(base64_encode(file_get_contents(
                $data_file
            )));

            return response()->json([
                'error' => false,
                'base64' => $base64
            ], 422);
        } catch (\Exception $e) {
            // Log::info('Error download file ekb from privy', [
            //     'message' => $e->getMessage()
            // ]);
            return response()->json([
                'error' => true,
                'message' => $e->getMessage()
            ], 422);
        }
    }

    public function show(Request $request)
    {
        $id = $request->id;
        $batchApprovalId = $request->batchApprovalId;
        $documentType = (isset($request->documentType)) ?  $request->documentType : 'import';
        if (str($documentType)->contains(["DynamicSign", "TemporaryExport", "TemporaryImport"])) {
            $file_name = $request->referenceNumber . '.pdf';
            $id = $batchApprovalId;
        } else {
            $row = DB::connection('sqlsrv4')
                ->table('batch_approvals')
                ->where('id', $id)
                ->first();

            $form_draft = json_decode(json_decode($row->value));
            $file_name = $form_draft->file_names;
        }
        info("file name ekb ". $file_name);
        // create_file_delete_job('documents/' . $file_name);
        $all_pdf = public_path('documents/' . $file_name);


        // Log::info('download attachment ekb', [
        //     'file_exists' => file_exists($all_pdf),
        //     'pdf' => $all_pdf
        // ]);

        if (!file_exists($all_pdf)) {
            info("File not exisit ". $file_name);
            $service = new CheckStatusPrivyService();
            $contents = $service->index($id);

            $file_name = $request->referenceNumber . '.pdf';

            custom_disk_put('documents/' . $file_name, $contents);
        }

        create_file_delete_job('documents/' . $file_name);
        $all_pdf = public_path('documents/' . $file_name);



        // RemoveAttachment::dispatch([$all_pdf])->delay(now()->addMinutes(30));

        $base64 = chunk_split(base64_encode(file_get_contents(
            $all_pdf
        )));

        return response()->json([
            'error' => false,
            'base64' => $base64
        ], 200);
    }
}
