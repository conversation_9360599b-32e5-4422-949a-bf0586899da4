<?php

namespace App\Http\Controllers\Callback;

use App\Http\Controllers\Controller;
use App\Models\Document\Document;
use App\Services\ApprovalActionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CallbackSignController extends Controller
{
    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            $documentNumber = $request->DocumentReferenceID;
            $status = $request->StatusId;

            if ($documentNumber) {
                $document = Document::where('document_number', $documentNumber)->first();

                $service = new ApprovalActionService();
                if ($status == 'Rejected') {
                    $service->updateDocumentStatus($document->id, 'rejected');
                } else {
                    $service->updateDocumentStatus($document->id, 'approved - on process');

                    $document = Document::where('document_number', $documentNumber)->first();
                    $service->processSignDocument($document, 0, 0);
                }
                DB::commit();
                return response()->json([
                    'error' => false,
                    'message' => 'Document approved!'
                ]);
            } else {
                return response()->json([
                    'error' => true,
                    'message' => 'No Document Found!'
                ], 422);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            // Log::info('callback internal sign', [
            //     'message' => $exception->getMessage(),
            //     'trace' => $exception->getTrace()
            // ]);
            return response()->json([
                'error' => true,
                'message' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
                // 'trace' => $exception->getTrace()
            ], 422);
        }
    }
}
