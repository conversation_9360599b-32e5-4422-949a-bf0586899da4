<?php

namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Approval\TransactionApprovalDetails
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionApprovalDetails newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionApprovalDetails newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionApprovalDetails query()
 * @mixin \Eloquent
 */
class TransactionApprovalDetails extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $connection = 'sqlsrv2';
    protected $table = 'U_WDD1';
    protected $primaryKey = 'U_DocEntry';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
