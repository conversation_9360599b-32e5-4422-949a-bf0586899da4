<?php

namespace App\Services\Reports;

class IssueAtkSummaryService
{
    public function header()
    {
        return ['ItemCode', 'ItemName', 'WHS', 'ReqQty', 'UoMCode'];
    }

    public function columns()
    {
        return [
            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Dscription",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "WhsCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Quantity",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "unitMsr",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value['ItemCode'],
                $value['Dscription'],
                $value['WhsCode'],
                $value['Quantity'],
                $value['unitMsr'],
                // $value['Department'],
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
