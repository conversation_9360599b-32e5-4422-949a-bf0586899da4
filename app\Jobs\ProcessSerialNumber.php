<?php

namespace App\Jobs;

use App\Models\Common\Attachment;
use App\Models\Common\SerialNumber;
use App\Models\Document\DocumentCoordinate;
use App\Services\DocumentService;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use App\Traits\DocumentHelper;
use App\Traits\SerialNumberHelper;
use Carbon\Carbon;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class ProcessSerialNumber implements ShouldQueue
{
    // public $timeout = 0;

    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use AppConfig;
    use ApiResponse;
    use DocumentHelper;
    use Ser<PERSON>NumberHelper;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;
    protected $document;
    protected $fileName;
    protected $userId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($document, $fileName, $userId)
    {
        $this->document = $document;
        $this->fileName = $fileName;
        $this->userId = $userId;
        $this->onQueue('processDocument');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        sleep(5);
        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');

        $document = $this->document;
        // $fileName = $this->fileName;

        $service = new DocumentService();
        $service->peruriLogin();

        $fileName = Attachment::where('source_id', $document->id)
            ->where('type', 'peruri')
            ->first();

        $dataCheck = [
            'document_id' => $document->id,
            'name' => 'Generate Serial Number',
        ];

        $dataLog = [
            'created_by' => $document->created_by,
            'id_document' => $document->external_document_number,
            'request_time' => Carbon::now(),
            'file_name' => $fileName->file_name
        ];

        $params = [
            'idfile' => $fileName->id,
            'isUpload' => false,
            'metadata' => "",
            'namadoc' => $document->type_no,
            'namafile' => $fileName->file_name,
            'nilaidoc' => $document->value,
            'namejidentitas' => $document->identity_type,
            'noidentitas' => $document->identity_number,
            'namedipungut' => $document->identity_name,
            'snOnly' => false,
            'nodoc' => $document->document_number,
            'tgldoc' => $document->document_date,
        ];

        $url = $this->getConfigByName('GenerateSerialNumber');
        $token = $this->getConfigByName('Token');

        $dataSerial = SerialNumber::orderBy('tgl')
            ->where('status', 'NOTSTAMP')
            ->where('is_used', 'no')
            ->first();
        if ($document->sign_payment == '2') {
            $this->process($document, $dataSerial, $fileName, $dataLog, $dataCheck, $params, $document, 'ref_token');
            // if (!$document->ref_token) {
            // }
        } else if ($document->sign_payment == '3') {
            $coordinates = $document->coordinate;
            foreach ($coordinates as $item) {
                $coordinate = DocumentCoordinate::find($item->id);
                $dataSerial = SerialNumber::orderBy('tgl')
                    ->where('status', 'NOTSTAMP')
                    ->where('is_used', 'no')
                    ->first();
                Log::info('from process serial number', [
                    'serial' => $coordinate->serial_number,
                ]);
                $this->process($document, $dataSerial, $fileName, $dataLog, $dataCheck, $params, $coordinate, 'serial_number');
                // if (!$coordinate->serial_number) {
                // // if (!$coordinate->serial_number) {
                // }
            }
        }
    }
    /**
     * Summary of process
     * @param mixed $document
     * @param mixed $dataSerial
     * @param mixed $fileName
     * @param mixed $dataLog
     * @param mixed $dataCheck
     * @param mixed $params
     * @param mixed $serialTable
     * @param mixed $snColumn
     * @throws \Exception
     * @return void
     */
    public function process($document, $dataSerial, $fileName, $dataLog, $dataCheck, $params, $serialTable, $snColumn)
    {
        $url = $this->getConfigByName('GenerateSerialNumber');
        $token = $this->getConfigByName('Token');
        if ($serialTable->meterai_coordinate == 'Y') {
            try {
                if ($dataSerial) {
                    $serialTable->$snColumn = $dataSerial->serial_number;
                    $serialTable->save();

                    $dataSerial->is_used = 'yes';
                    $dataSerial->save();

                    Log::info('from process serial number, update stamp', [
                        'serial' => $dataSerial->serial_number,
                    ]);

                    $this->updateDataSerial($dataSerial, $fileName, $document, $token, $dataLog, $dataCheck, $snColumn, $this->batch(), $serialTable);
                } else if (empty($document->$snColumn)) {
                    Log::info('from process serial number, serial', [
                        'serial' => $serialTable->$snColumn,
                    ]);
                    // process generate serial number with laravel http client
                    $this->submitSerialNumber($token, $url, $params, $dataLog, $dataCheck, $document, $snColumn, $fileName, $this->batch(), $serialTable);
                } else {
                    Log::info('from process serial number, qr image', [
                        'serial' => $serialTable->$snColumn,
                    ]);
                    $this->generateQrImage($token, $document, $dataLog, $dataCheck, $fileName, $this->batch(), $serialTable, $snColumn);
                }
            } catch (\Exception $exception) {
                $dataUpdate = [
                    'batch_id' => $this->batch()->id,
                    'status' => 'Processing ' . $this->batch()->progress() . '%',
                    'callback_message' => 'Failed process serial number: ' . $exception->getMessage(),
                    'callback_trace' => ''
                ];
                $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                throw new \Exception('E-METERAI: ' . $exception->getMessage(), 1);
            }
        }
    }
}
