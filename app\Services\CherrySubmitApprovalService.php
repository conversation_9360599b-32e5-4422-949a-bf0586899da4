<?php

namespace App\Services;

use App\Models\Common\Vehicle;
use App\Models\Master\ResvSapUsage;
use App\Models\Resv\ResvHeader;
use App\Models\User;
use App\Models\View\ViewApprovalStage;
use App\Models\View\ViewEmployee;
use App\Services\ReservationDataService;
use App\Services\ReservationValidateDataService;
use App\Traits\ApiResponse;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CherrySubmitApprovalService
{
    use ApiResponse;

    private function validateItemCounts($details, $header)
    {
        $counts = [
            'it' => 0,
            'it_bdm' => 0,
            'sub_group_network' => 0,
            'sub_group_other' => 0,
            'ga' => 0,
            'npb' => 0,
            'spb' => 0,
            'safety' => 0,
            'seragam' => 0,
            'ga_in_safety' => 0,
            'bdm' => 0
        ];

        $groups = [
            'it' => [],
            'it_bdm' => [],
            'ga' => [],
            'safety' => [],
            'ga_in_safety' => [],
            'bdm' => []
        ];

        $validateService = new ReservationValidateDataService();

        foreach ($details as $item) {
            // Count IT items
            if ($header->RequestType != 'Restock') {
                if ($item['ItemGroup'] == 'ZITS') {
                    $counts['it']++;
                }
                if (Str::contains($item['SubGroup'], ['89112'])) {
                    $counts['sub_group_other']++;
                }
                if ($item['SubGroup'] == '66103') {
                    $counts['sub_group_network']++;
                } else {
                    $counts['sub_group_other']++;
                }
                $groups['it'][] = $item['ItemGroup'];
            }

            // Count BDM IT items
            if (Str::contains($header->WorkLocation, ['BDM MOROWALI']) && $header->WhsCode == 'BG02') {
                if ($item['ItemGroup'] == 'ZITS') {
                    $counts['it_bdm']++;
                }
                $groups['it_bdm'][] = $item['ItemGroup'];
            }

            // Count GA items 
            if ($item['SubGroup'] == '66201' || ($item['SubGroup'] == '66202' && $item['ItemCategory'] == 'RS')) {
                $counts['ga']++;
            }
            $groups['ga'][] = $item['ItemGroup'];

            // Count GA in Safety items
            if ($item['U_AppResBy'] == 'GA') {
                $counts['ga_in_safety']++;
            }
            $groups['ga_in_safety'][] = $item['U_AppResBy'];

            // Count safety items
            if ($item['U_AppResBy'] == 'HSE' || $header->CategoryType == 'APD') {
                $counts['safety']++;
            }
            $groups['safety'][] = $item['U_AppResBy'];

            // Count seragam items
            if ($item['SubGroup'] == '67104' && $item['ItemCategory'] == 'RS') {
                $counts['seragam']++;
            }

            // Check NPB/SPB
            if ($item['NPB'] == 'Y')
                $counts['npb']++;
            if ($item['SPB'] == 'Y')
                $counts['spb']++;

            // Count BDM items
            $groups['bdm'][] = $item['ItemGroup'];
            if (array_key_exists('SubGroup', $item)) {
                if (
                    str($item['SubGroup'])->contains($validateService->itemGroupSapBDM() &&
                        str($header->Company)->contains(['BDM', 'BDW']))
                ) {
                    $counts['bdm']++;
                }
            }
        }

        $flags = [
            'is_request_it' => $counts['it'] > 0 && $counts['it'] == count($groups['it']),
            'is_request_it_bdm' => $counts['it_bdm'] > 0 && $counts['it_bdm'] == count($groups['it_bdm']),
            'is_request_ga' => $counts['ga'] > 0 && $counts['ga'] == count($groups['ga']),
            'is_request_ga_seragam' => $counts['seragam'] > 0 && $counts['seragam'] == count($groups['ga']),
            'is_request_ga_in_safety' => $counts['ga_in_safety'] > 0 && $counts['ga_in_safety'] == count($groups['ga_in_safety']),
            'is_request_safety' => $counts['safety'] > 0 && $header->CategoryType == 'APD',
            'is_request_item_bdm' => $counts['bdm'] > 0 && $counts['bdm'] == count($groups['bdm'])
        ];

        return array_merge($counts, $flags);
    }

    public function submitApproval($header, $details, $request)
    {
        // Initial validation
        $checkExisting = ViewApprovalStage::where('DocumentReferenceID', $header->DocNum)->first();
        if ($checkExisting) {
            ResvHeader::where("DocNum", $header->DocNum)->update(['ApprovalStatus' => 'W']);
            throw new \RuntimeException("Document already submitted to cherry");
        }

        // Get auth data
        $cherry_token = Auth::user()->cherry_token;
        $authUser = Auth::user();
        $username = ($authUser->hasAnyRole(['Superuser'])) ? $header->CreatedBy : $authUser->username;
        $createdUser = User::where('username', $username)->first();
        $cherry_token = ($authUser->hasAnyRole(['Superuser'])) ? $createdUser->cherry_token : $cherry_token;

        // Get document list from API
        $list_code = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'ExternalDocuments',
                'UserName' => $username,
                'Token' => $cherry_token,
                'ParameterData' => []
            ]);

        if ($list_code->failed() || $list_code->collect()['MessageType'] == 'error') {
            throw new \Exception($list_code->body() ?: $list_code->collect()['Message'], 1);
        }

        // Validate item counts
        $itemCounts = $this->validateItemCounts($details, $header);

        // Get vehicle data
        $is_carpool = false;
        $vehicle = Vehicle::where('vehicle_no', $header->VehicleNo)->first();
        if ($vehicle && $vehicle->is_carpool == 'Yes') {
            $is_carpool = true;
        }

        // Get reservation code based on conditions 
        $reservation_code = $this->getReservationCode($list_code->collect()['Data'], $header, $itemCounts, $vehicle);

        if (!$reservation_code) {
            throw new \Exception('External Document Code cannot empty!', 1);
        }

        // Get employee data
        $employee = ViewEmployee::where('Nik', $header->Requester)->first();
        $employee_code = $employee->EmployeeCode ?: Auth::user()->employee_code;
        $company_code = $employee->CompanyCode;

        // Get movement type
        $movementType = ResvSapUsage::where('movement_type', $header->Usage)->first();

        // Generate document content
        $document_content = view('email.approval_resv', [
            'details' => $details,
            'movementType' => $movementType,
            'header' => $header
        ])->render();

        // Submit to cherry API
        $response = $this->submitToCherry(
            $username,
            $cherry_token,
            $reservation_code,
            $company_code,
            $employee_code,
            $header,
            $document_content
        );

        if ($response['MessageType'] == 'error') {
            $this->handleCherryError($response, $header);
        }

        // Update approval status
        ResvHeader::where('U_DocEntry', $header->U_DocEntry)
            ->update(['ApprovalStatus' => 'W']);

        return $this->success([
            'U_DocEntry' => $header->U_DocEntry
        ], ($header->U_DocEntry != 'null' ? 'Data updated!' : 'Data inserted!'));
    }

    // Add other helper methods...
}