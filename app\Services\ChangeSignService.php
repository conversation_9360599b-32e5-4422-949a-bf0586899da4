<?php

namespace App\Services;

use App\Jobs\ProcessMeteraiStamp;
use App\Jobs\ProcessSerialNumber;
use App\Jobs\ProcessSubmitDigitalSign;
use App\Jobs\ProcessSubmitPrivy;
use App\Models\Approval\BatchApproval;
use App\Models\Document\Document;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;

class ChangeSignService
{
    public function changeSign($document, $fileName, $userId, $doc_id, $batch_approval, $checkUseDigisign)
    {
        // if (Str::of($document->company)->contains(['PT BDM', 'PT MMM', 'PT IMIP'])) {
        //     $this->usePrivy($document, $fileName, $userId, $doc_id, $batch_approval);
        // } else

        if ($checkUseDigisign == '1') {
            $this->useDigisign($document, $fileName, $userId, $doc_id, $batch_approval);
        } else {
            $this->usePrivy($document, $fileName, $userId, $doc_id, $batch_approval);
        }
    }
    public function useDigisign($document, $fileName, $userId, $doc_id, $batch_approval)
    {
        // Log::info('Process digisign and peruri document= ' . $document->document_number);
        $batch = Bus::batch([
            [new ProcessSerialNumber($document, $fileName, $userId)],
            [new ProcessMeteraiStamp($document, $fileName, $userId)],
            [new ProcessSubmitDigitalSign($document, $fileName, $userId)],
            // [new ProcessPrivyCheckDocument($document)]
        ])->catch(function (Batch $batch, Throwable $e) use ($doc_id) {
            $document = Document::find($doc_id);
            $document->status = 'approved - failed';
            $document->save();

            // event(new DocumentEvent($document->created_by));
        })->finally(function (Batch $batch) use ($batch_approval, $doc_id) {
            $batch_approval = BatchApproval::where('document_id', $doc_id)->pluck('callback_message');
            $document = Document::find($doc_id);
            $count_success = 0;
            foreach ($batch_approval as $key => $value) {
                if (Str::contains($value, ['Success'])) {
                    ++$count_success;
                }
            }
            if (count($batch_approval) == $count_success && $count_success > 0) {
                $document->status = 'approved - finish';
            } else {
                $document->status = 'approved - failed';
            }
            $document->save();

            // event(new DocumentEvent($document->created_by));
        })
            ->name('Process Submit To Privy and Digisign')
            ->onQueue('digitalSign')
            ->allowFailures()
            ->dispatch();

        $document = Document::find($document->id);
        $document->batch_id = $batch->id;
        $document->save();
    }

    public function usePrivy($document, $fileName, $userId, $doc_id, $batch_approval)
    {
        // Log::info('Process privy document= ' . $document->document_number);
        $batch = Bus::batch([
            // [new ProcessSerialNumber($document, $fileName, $request->user()->id)],
            // [new ProcessMeteraiStamp($document, $fileName, $request->user()->id)],
            [new ProcessSubmitPrivy($document, $fileName, $userId)],
            // [new ProcessPrivyCheckDocument($document)]
        ])->catch(function (Batch $batch, Throwable $e) use ($doc_id) {
            $document = Document::find($doc_id);
            $document->status = 'approved - failed';
            $document->save();

            // event(new DocumentEvent($document->created_by));
        })->finally(function (Batch $batch) use ($batch_approval, $doc_id) {
            $batch_approval = BatchApproval::where('document_id', $doc_id)->pluck('callback_message');
            $document = Document::find($doc_id);
            $count_success = 0;
            foreach ($batch_approval as $key => $value) {
                if (Str::contains($value, ['Success'])) {
                    ++$count_success;
                }
            }
            if (count($batch_approval) == $count_success && $count_success > 0) {
                if ($document->privy_status == 'completed') {
                    $document->status = 'approved - finish';
                } else {
                    $document->status = 'approved - ' . $document->privy_status;
                }
            } else {
                $document->status = 'approved - failed';
            }
            $document->save();

            // event(new DocumentEvent($document->created_by));
        })
            ->name('Process Submit To Privy')
            ->onQueue('processDocumentPrivy')
            ->allowFailures()
            ->dispatch();

        $document = Document::find($document->id);
        $document->batch_id = $batch->id;
        $document->save();
    }

    public function usePrivyBackup($document, $fileName, $userId, $doc_id, $batch_approval)
    {
        // Log::info('Process privy document= ' . $document->document_number);
        $batch = Bus::batch([
            [new ProcessSerialNumber($document, $fileName, $userId)],
            [new ProcessMeteraiStamp($document, $fileName, $userId)],
            [new ProcessSubmitPrivy($document, $fileName, $userId)],
            // [new ProcessPrivyCheckDocument($document)]
        ])->catch(function (Batch $batch, Throwable $e) use ($doc_id) {
            $document = Document::find($doc_id);
            $document->status = 'approved - failed';
            $document->save();

            // event(new DocumentEvent($document->created_by));
        })->finally(function (Batch $batch) use ($batch_approval, $doc_id) {
            $batch_approval = BatchApproval::where('document_id', $doc_id)->pluck('callback_message');
            $document = Document::find($doc_id);
            $count_success = 0;
            foreach ($batch_approval as $key => $value) {
                if (Str::contains($value, ['Success'])) {
                    ++$count_success;
                }
            }
            if (count($batch_approval) == $count_success && $count_success > 0) {
                if ($document->privy_status == 'completed') {
                    $document->status = 'approved - finish';
                } else {
                    $document->status = 'approved - ' . $document->privy_status;
                }
            } else {
                $document->status = 'approved - failed';
            }
            $document->save();

            // event(new DocumentEvent($document->created_by));
        })
            ->name('Process Submit To Privy')
            ->onQueue('processDocumentPrivy')
            ->allowFailures()
            ->dispatch();

        $document = Document::find($document->id);
        $document->batch_id = $batch->id;
        $document->save();
    }
}
