<?php

namespace App\Http\Controllers\Notification;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\Common\NotificationMarkAsRead;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    /**
     * Summary of index
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = User::find($request->user()->id);

        return $this->success([
            'data' => $user->notifications,
            'total' => count($user->notifications)
        ]);
    }

    public function unRead(Request $request)
    {
        $user = User::find($request->user()->id);

        return $this->success([
            'data' => $user->unreadNotifications,
            'total' => count($user->unreadNotifications)
        ]);
    }

    /**
     * Summary of countData
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function countData(Request $request)
    {
        $user = User::find($request->user()->id);

        $count = $user->unreadNotifications()->count();

        return $this->success([
            'total' => $count
        ], $count);
    }
    /**
     * Summary of markAsReadSingleRow
     * @param Request $request
     * @param mixed $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsReadSingleRow(Request $request, $id)
    {
        try {
            $notifications = $request->notifications;
            foreach ($notifications as $item) {
                auth()->user()
                    ->unreadNotifications
                    ->when($item['id'], function ($query) use ($item) {
                        return $query->where('id', $item['id']);
                    })
                    ->markAsRead();
            }

            $request->user()->notify(new NotificationMarkAsRead());

            return $this->success([], 'notification updated');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), $exception->getCode(), [
                'trace' => $exception->getTrace()
            ]);
        }
    }
}
