{"name": "abram/laravel-odbc", "description": "ODBC integration for Laravel framework ", "type": "library", "homepage": "https://github.com/andreossido/laravel-odbc", "authors": [{"name": "<PERSON>", "email": "andre<PERSON><PERSON>@gmail.com", "homepage": "https://github.com/andreossido", "role": "Developer"}], "require": {"php": ">=8.0", "illuminate/database": "^9.0 | ^10.0", "illuminate/support": "^9.0 | ^10.0"}, "autoload": {"classmap": ["src"]}, "extra": {"laravel": {"providers": ["Abram\\Odbc\\ODBCServiceProvider"]}}}