<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Jobs\RemoveAttachment;
use App\Models\Common\Attachment;
use App\Models\Document\Document;
use App\Models\Document\DocumentSubType;
use App\Models\Document\Invoice;
use App\Services\ConvertDocxToPdfService;
use App\Services\DocumentService;
use App\Services\InvoiceService;
use App\Services\SapS4Service;
use App\Traits\AppConfig;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use PhpOffice\PhpWord\TemplateProcessor;

class InvoiceController extends Controller
{
    use AppConfig;

    public $service;

    public function __construct(InvoiceService $service)
    {
        $this->service = $service;
    }
    /**
     * Display a listing of the resource.
     * @return JsonResponse
     *
     */
    public function index(Request $request): JsonResponse
    {
        $service = new SapS4Service();
        $service->login();

        $customer = [];
        $customers = $service->getCustomer();
        foreach ($customers['DATA'] as $key => $value) {
            $customer[] = [
                "CardName" => $value['NAME1'],
                "CardCode" => $value['KUNNR'],
                "Address" => $value['KUNNR'],
            ];
        }

        $pages = isset($request->page) ? (int) $request->page : 1;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;

        $sorts = isset($request->sortBy[0]) ? json_decode($request->sortBy[0])->key : 'document_number';
        $order = isset($request->sortBy[0]) ? json_decode($request->sortBy[0])->order : 'desc';

        $search_item = isset($request->searchItem) ? (string) $request->searchItem : '';
        $search = isset($request->search) ? (string) $request->search : '';
        $type = isset($request->type) ? (string) $request->type : '';
        $offset = $pages;

        $query = Invoice::with(['lineItems', 'document'])
            //->whereIn('status', $status)
            ->orderBY($sorts, $order);

        if (!$request->user()->hasAnyRole(['Superuser'])) {
            $query = $query->where('created_by', $request->user()->id);
        }

        if ($search_item == 'Document Number') {
            $query = $query->where('document_number', 'LIKE', '%' . $search . '%');
        } elseif ($search_item == 'Document Type') {
            $query = $query->where('type', 'LIKE', '%' . $search . '%');
        } elseif ($search_item == 'Created By') {
            $query = $query->where('created_name', 'LIKE', '%' . $search . '%');
        }

        $total = $query->count();

        $data = $query->offset($offset)
            ->limit($row_data)
            ->get();

        return $this->success([
            'rows' => $data,
            'total' => $total,
            'form' => $this->service->getForm(),
            'document_status' => [
                ucfirst('all'),
                ucfirst('open'),
                ucfirst('meterai'),
            ],
            'invoice_type' => ['Invoice', 'Proforma Invoice'],
            'customer' => $customer,
            'search_item' => ['Document Number', 'Document Type'],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        $this->service->validateDetails($request->line_items);

        DB::beginTransaction();
        try {
            $line_items = $request->line_items;
            // return $this->error('', 422, $this->service->formData($request, 'store'));
            $document = Invoice::create($this->service->formData($request, 'store'));

            $this->service->processDetails($document, $line_items);

            DB::commit();

            return $this->success('Document saved!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'customer' => 'required',
            'document_number' => 'required',
            'document_date' => 'required',
            'due_date' => 'required',
            'amount' => 'required',
        ]);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     */
    public function show(Request $request, $id): JsonResponse
    {
        $category = $request->category;
        $brand = Invoice::where('category', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param \App\Models\Document\Document $productBrand
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        $this->service->validateDetails($request->line_items);

        DB::beginTransaction();
        try {
            $line_items = $request->line_items;
            Invoice::where('id', $id)
                ->update($this->service->formData($request, 'update'));

            $document = Invoice::find($id);

            $this->service->processDetails($document, $line_items);

            DB::commit();
            return $this->success('Document saved!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            Invoice::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    public function generateDocument(Request $request)
    {
        $id = $request->id;
        $invoices = Invoice::whereIn('id', $id)->get();

        foreach ($invoices as $key => $invoice) {

            DB::beginTransaction();
            try {
                $data_letter = [];
                $file_path_name = public_path('/Attachment/docs/') . strtoupper(Str::slug($invoice->document_number)) . '.docx';

                $letter_template = new TemplateProcessor(public_path('template/INVOICE.docx'));
                $companyName = $invoice->company;
                $letter_template->setImageValue('LOGO', [
                    'path' => public_path($this->getConfigByName('LOGO', 'COMMON', $companyName)),
                    'width' => 281,
                    'height' => 57
                ]);

                $letter_template->setValue('COMPANYNAME', $this->getConfigByName('COMPANYNAME', 'COMMON', $companyName));
                $letter_template->setValue('COMPANYNAMECAPITAL', strtoupper($this->getConfigByName('COMPANYNAME', 'COMMON', $companyName)));
                $letter_template->setValue('ENBANKNAME', $this->getConfigByName('ENBANKNAME', 'COMMON', $companyName));
                $letter_template->setValue('ENBANKACCOUNT', $this->getConfigByName('ENBANKACCOUNT', 'COMMON', $companyName));
                $letter_template->setValue('ENBANKNO', $this->getConfigByName('ENBANKACCOUNT', 'COMMON', $companyName));
                $letter_template->setValue('ENBANKBRANCH', $this->getConfigByName('ENBANKBRANCH', 'COMMON', $companyName));
                $letter_template->setValue('ENBANKSWIFTCODE', $this->getConfigByName('ENBANKSWIFTCODE', 'COMMON', $companyName));
                $letter_template->setValue('IDNCOMPANYNAME', $this->getConfigByName('IDNCOMPANYNAME', 'COMMON', $companyName));
                $letter_template->setValue('IDNBANKNAME', $this->getConfigByName('IDNBANKNAME', 'COMMON', $companyName));
                $letter_template->setValue('IDNBANKACCOUNT', $this->getConfigByName('IDNBANKACCOUNT', 'COMMON', $companyName));
                $letter_template->setValue('IDNBANKBRANCH', $this->getConfigByName('IDNBANKBRANCH', 'COMMON', $companyName));
                $letter_template->setValue('ADDRESS1', $this->getConfigByName('ADDRESS1', 'COMMON', $companyName));
                $letter_template->setValue('ADDRESS2', $this->getConfigByName('ADDRESS2', 'COMMON', $companyName));
                $letter_template->setValue('APPROVALNAME', $this->getConfigByName('APPROVALNAME', 'COMMON', $companyName));
                $letter_template->setValue('CURR', 'IDR');
                $letter_template->setValue('BILL_TO', str_replace('&', '&amp;', $invoice->customer));
                $letter_template->setValue('NO_NOTA', $invoice->document_number);
                $letter_template->setValue('DATE', date('d F Y', strtotime($invoice->document_date)));
                $letter_template->setValue('DUE_DATE', $invoice->due_date);
                $letter_template->setValue('ADDRESS', $invoice->address);
                $letter_template->setValue('NOTES', $invoice->notes);
                $letter_template->setValue('TITLE', strtoupper($invoice->title));

                $line_items = $invoice->lineItems;

                foreach ($line_items as $index => $line_item) {
                    // throw new \Exception(str_replace("\n", "</w:t><w:br/><w:t>", $line_item->description ), 1);
                    $data_letter[] = [
                        'NO' => ($index + 1),
                        'DESCRIPTION' => str_replace("\n", "</w:t><w:br/><w:t>", $line_item->description),
                        'AMOUNT' => number_format($line_item->amount, 2),
                        'CURR' => $line_item->currency,
                    ];
                }


                $letter_template->setValue('SUB_TOTAL', number_format($invoice->sub_total, 2));
                $letter_template->setValue('VAT', number_format($invoice->vat, 2));
                $letter_template->setValue('TOTAL', number_format($invoice->amount, 2));

                $letter_template->cloneRowAndSetValues('NO', $data_letter);

                $letter_template->saveAs($file_path_name);

                RemoveAttachment::dispatch([$file_path_name])->delay(now()->addMinutes(5));

                try {
                    $word_file = new \COM("word.application");
                } catch (\Exception $ex) {
                    return response()->json([
                        'message' => $ex->getMessage()
                    ], 422);
                }

                $pdf_path = public_path('/Attachment/docs/') . strtoupper(Str::slug($invoice->document_number)) . '.pdf';

                $serviceConvert = new ConvertDocxToPdfService();
                $pathToSavingDirectory = public_path('/Attachment/docs/');
                $pdfFileName = strtoupper(Str::slug($invoice->document_number)) . ".pdf";
                $serviceConvert->convert($file_path_name, $pathToSavingDirectory, $pdfFileName);
                $pdf_file = $pathToSavingDirectory . strtoupper(Str::slug($invoice->document_number)) . ".pdf";

                create_file_delete_job('/Attachment/docs/' . $pdfFileName);

                $this->createDocument($invoice, $pdf_path);

                RemoveAttachment::dispatch($pdf_path)->delay(now()->addMinutes(10));

                DB::commit();
            } catch (\Exception $exception) {
                DB::rollBack();
                return $this->error($exception->getMessage(), '422', [
                    'trace' => $exception->getTrace()
                ]);
            }
        }

        return $this->success('Document saved!');
    }


    public function createDocument($invoice, $file)
    {
        $file = pathinfo($file);

        $sub_name = ($invoice->title == 'Invoice') ? 'AR Invoice' : 'Proforma Invoice';

        $sub_type = DocumentSubType::where('name', $sub_name)->first();

        $origin_name = $file['filename'];

        $external_document_number = $invoice->external_document_number;
        if (empty($invoice->external_document_number)) {
            $external_document_number = pathinfo($origin_name, PATHINFO_FILENAME);
        }

        $check_document = Document::where('external_document_number', $external_document_number)
            ->whereNotIn('status', ['canceled', 'rejected'])
            ->count();

        if ($check_document > 0) {
            return $this->error('Nama file sudah pernah dipakai!');
        }

        // $filtered = $filtered->merge(['external_document_number' => $external_document_number]);
        $service = new DocumentService();

        $form = [
            "document_number" => $service->generateDocNum(date('Y-m-d H:i:s'), 'test'),
            "type" => "Dokumen Transaksi",
            "type_no" => 2,
            "value" => 10000,
            "document_date" => $invoice->document_date,
            "location" => "JAKARTA",
            "password" => null,
            "filename" => $file['filename'],
            "reason" => $invoice->notes,
            "profile_name" => "emeteraicertificateSigner",
            "identity_type" => "NPWP",
            "identity_number" => $this->getConfigByName('NPWP', 'COMMON', $invoice->company),
            "identity_name" => strtoupper($this->getConfigByName('COMPANYNAME', 'COMMON', $invoice->company)),
            "external_document_number" => $invoice->document_number,
            "company" => $invoice->company,
            "document_sub_type_id" => $sub_type->id,
            "customer_name" => $invoice->customer,
            "invoice_id" => $invoice->id,
            "created_by" => auth()->user()->id
        ];
        $document = Document::create($form);

        invoice::where('id', $invoice->id)
            ->update([
                'document_id' => $document->id
            ]);

        // process attachment
        $extension = $file['extension'];

        // $destination_path = public_path('/Attachment/docs');

        // check if destination path exist, if not create one
        // if (!file_exists($destination_path)) {
        //     if (!mkdir($destination_path, 0777, true) && !is_dir($destination_path)) {
        //         throw new \RuntimeException(
        //             sprintf(
        //                 'Directory "%s" was not created',
        //                 $destination_path
        //             )
        //         );
        //     }
        // }

        $name_no_ext = strtoupper(Str::slug(pathinfo($origin_name, PATHINFO_FILENAME)));
        $file_name = $name_no_ext . '.' . $extension;
        // $file->move($destination_path, $file_name);

        // data attachment
        $data = [
            'file_name' => $file_name,
            'file_path' => config('app.url') . '/Attachment/docs/' . $file_name,
            'source_id' => (int) $document->id,
            'str_url' => $document->id,
            'created_by' => auth()->user()->id,
            'type' => 'peruri'
        ];

        // save attachment
        $attach = Attachment::create($data);
    }
}
