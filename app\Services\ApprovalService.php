<?php

namespace App\Services;

use App\Models\Approval\Approval;
use App\Models\Approval\ApprovalApprover;
use App\Models\Approval\ApprovalRule;
use App\Models\Approval\ApprovalStage;
use App\Models\Common\Attachment;
use App\Models\Document\Document;
use App\Models\User;
use App\Models\View\ViewEmployee;
use App\Notifications\ApprovalMeteraiRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ApprovalService
{
    /**
     * @param $request
     * @param $type
     * @return array
     */
    public function index($request, $type): array
    {
        $all_data = [];
        $options = $request->options;
        $pages = isset($request->page) ? (int) $request->page : 1;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 10;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "name";
        $order = isset($request->sortBy[0]) ? (string) $options->sortDesc[0] : "asc";
        $type = $request->contactType;
        $offset = $pages;

        $result = array();
        $query = Approval::selectRaw("
            approvals.*,
            'actions' as ACTIONS ")
            ->with(['rules', 'approver']);

        $result["total"] = $query->count();

        $query->orderBy($sorts, $order);
        if ($row_data != 0) {
            $query->paginate($request->itemsPerPage)
                ->items();
        }
        $all_data = $query->get();

        $all_data = array_merge($result, [
            "rows" => $all_data,
        ]);

        return $all_data;
    }

    /**
     * @param $form
     * @return array
     */
    public function formData($request): array
    {
        $request->request->remove('rules');
        $request->request->remove('ACTIONS');
        $request->request->remove('approver');
        $request->request->remove('id');
        $request->request->remove('created_at');
        $request->request->remove('updated_at');
        $request->request->remove('deleted_at');
        $request->request->remove('/api/master/approval/2');
        $request->merge(['final_status' => 'approve']);
        $data = $request->all();
        Arr::forget($data, '/api/master/approval/2');

        return $data;
    }

    /**
     * @param $approves
     * @param $approval_id
     * @return void
     */
    public function storeApprover($approves, $approval_id)
    {
        foreach ($approves as $approve) {
            $approve = (object) $approve;
            if ($approve->user_id) {
                ApprovalApprover::updateOrCreate([
                    'approval_id' => $approval_id,
                    'sequence' => $approve->sequence,
                    'user_id' => $approve->user_id,
                ], [
                    'user_id' => $approve->user_id,
                    'is_specimen' => $approve->is_specimen,
                    'types' => $approve->types,
                ]);
            }
        }
    }

    /**
     * @param $rules
     * @param $approval_id
     * @return void
     */
    public function storeApprovalRules($rules, $approval_id)
    {
        foreach ($rules as $item) {
            $item = (object) $item;
            if ($item->name) {
                ApprovalRule::updateOrCreate([
                    'approval_id' => $approval_id,
                    'name' => $item->name,
                ], [
                    'operator' => $item->operator,
                    'value' => $item->value,
                ]);
            }
        }
    }

    /**
     * Retrieves all the approvers for a given approval.
     *
     * @param Approval $approval the approval object to retrieve approvers for.
     * @throws \Exception if the retrieval fails.
     * @return \Illuminate\Database\Eloquent\Collection a collection of approval approver objects.
     */
    protected function getApprover(Approval $approval)
    {
        return ApprovalApprover::leftJoin('users', 'users.id', 'approval_approver.user_id')
            ->where('approval_id', $approval->id)
            ->orderBy('approval_approver.sequence')
            ->get();
    }

    /**
     * Sends a notification to the given approver about the given document.
     *
     * @param mixed $approver the recipient of the notification
     * @param Document $document the document being approved
     * @throws \Exception if there is a description of exception
     * @return void
     */
    protected function sendNotificationToApprover($approver, Document $document)
    {
        $requester = ViewEmployee::where('Nik', $document->userCreate->username)->first();
        $project = [
            'greeting' => 'Dear ' . $approver->name . ',',
            'body' => $document->external_document_number . ' waiting for approval',
            'subject' => "[E-SIGN] " . $document->external_document_number . ' waiting for approval',
            'to' => '<EMAIL>',
            'inv' => [],
            'cc' => $requester->OfficeEmailAddress
        ];

        $user = User::find($approver->user_id);
        $user->notify(new ApprovalMeteraiRequest($project));
    }

    /**
     * Processes the approval for the given type and document.
     *
     * @param Approval $approval The approval to process.
     * @param string $type The type of approval.
     * @param Document $document The document to approve.
     * @throws \Exception If the approval fails.
     * @return void
     */
    protected function processApproval(Approval $approval, $type, Document $document)
    {
        $approvers = $this->getApprover($approval);

        foreach ($approvers as $index => $approver) {
            if ($type == 'single') {
                $this->sendNotificationToApprover($approver, $document);
            }

            $this->createApprovalStage($approval, $approver, $document);
        }
    }

    /**
     * Creates a new approval stage for a given approval, approver, and document.
     *
     * @param Approval $approval The approval object to create the stage for.
     * @param mixed $approver The approver object to create the stage for.
     * @param Document $document The document object to create the stage for.
     * @throws \Exception if there is an error creating the approval stage.
     */
    protected function createApprovalStage(Approval $approval, $approver, Document $document)
    {
        ApprovalStage::create([
            'approval_id' => $approval->id,
            'user_id' => $approver->user_id,
            'status' => 'pending',
            'notes' => null,
            'response_date' => null,
            'document_id' => $document->id,
        ]);
    }

    /**
     * Submit the document for approval.
     *
     * @param string $parameter_type The type of parameter being submitted.
     * @param Document $document The document being submitted.
     * @param string $type The type of approval being submitted.
     * @throws \Exception Description of exception that can be thrown.
     */
    public function submitApproval(string $parameter_type, Document $document, string $type, Request $request)
    {
        // TODO : check schema approval
        // if ($document->document_type == 'internal') {
        // if ($request->user()->hasAnyRole(['E-Sign Cherry Approval'])) {
        if ($document->userCreate->hasAnyRole(['E-Sign Cherry Approval'])) {
            $this->submitCherryApproval($document);
        } else {
            $rules = ApprovalRule::all();

            $userCreate = $document->userCreate;
            // get approval
            $approval = Approval::when($document, function ($query) use ($document) {
                if ($document->internal_document == 'Y') {
                    $query->whereJsonContains('document_type', ['Internal Sign', 'Invoice Other']);
                }

                if ($document->signer) {
                    $query->where("has_signer", $document->signer);
                } else {
                    $query->whereNull("has_signer");
                }
            })
                ->when($userCreate, function ($query) use ($userCreate) {
                    $query->whereJsonContains('requester', $userCreate->name)
                        ->whereJsonContains('department', $userCreate->department);
                })
                ->where('company', $document->company)
                ->orderBy('priority', 'desc')
                ->first();

            // Log::info('approval check ', [
            //     'approval' => $approval
            // ]);

            if ($approval) {
                $this->processApproval($approval, $type, $document);
            } else {
                throw new \Exception("Approval not found");
            }
        }
    }

    /**
     * @param $form
     * @param $request
     *
     * @throws \Exception
     */
    public function submitCherryApproval($form)
    {
        $cherry_token = Auth::user()->cherry_token;
        $list_code = Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'GetList',
            'ModelCode' => 'ExternalDocuments',
            'UserName' => Auth::user()->username,
            'Token' => $cherry_token,
            'ParameterData' => [],
        ]);

        $approvalCode = null;
        if ($list_code->collect()['MessageType'] == 'error') {
            throw new \Exception($list_code->collect()['Message'], 1);
        }
        //throw new \Exception(json_encode($list_code->collect()['Data']), 1);
        foreach ($list_code->collect()['Data'] as $datum) {
            if ($datum['Name'] == 'Internal Sign' && $form->document_type == 'internal') {
                $approvalCode = $datum['Code'];
            } else if ($datum['Name'] == 'Invoice Sign' && $form->document_type != 'internal') {
                $approvalCode = $datum['Code'];
            }
        }
        // throw new \Exception(json_encode($approvalCode), 1);
        if (!$approvalCode) {
            throw new \Exception("Skema tidak boleh kosong", 1);
        }

        $username = Auth::user()->username;

        $employee = ViewEmployee::where('Nik', '=', Auth::user()->username)->first();

        $employee_code = $employee->EmployeeCode;
        $companyCode = $employee->CompanyCode;

        $attachment = Attachment::where("source_id", "=", $form->id)
            ->where("type", "=", "peruri")
            ->first();

        // throw new \Exception($approvalCode, 1);

        $document_content = view('email.approval_meterai', [
            'form' => $form,
            'paper' => $form,
            'attachment' => $attachment,
        ])->render();

        //return response()->json($document_content);
        //return response()->json($approvalCode);

        $response = Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'Submit',
            'ModelCode' => 'GADocuments',
            'UserName' => $username,
            'Token' => $cherry_token,
            'ParameterData' => [],
            'ModelData' => [
                'TypeCode' => $approvalCode,
                'CompanyCode' => $companyCode,
                'Date' => date('m/d/Y'),
                'EmployeeCode' => $employee_code,
                'DocumentReferenceID' => $form->document_number,
                'CallBackAccessToken' => config('app.access_token_1'),
                'DocumentContent' => $document_content,
                'Notes' => $form->type
            ]
        ]);

        // throw new \Exception(config('app.cherry_service_req'), 1);
        // $response = Http::attach('file0', public_path('Attachment/docs/' . $attachment->file_name), $attachment->file_name . '.pdf')
        //     ->post(config('app.cherry_service_req_attach'), [
        //         [
        //             'name' => 'model',
        //             'contents' => json_encode([
        //                 'CommandName' => 'Submit',
        //                 'ModelCode' => 'GADocuments',
        //                 'UserName' => $username,
        //                 'Token' => $cherry_token,
        //                 'ParameterData' => [],
        //                 'ModelData' => [
        //                     'TypeCode' => $approvalCode,
        //                     'CompanyCode' => $companyCode,
        //                     'Date' => date('m/d/Y'),
        //                     'EmployeeCode' => $employee_code,
        //                     'DocumentReferenceID' => $form->document_number,
        //                     'CallBackAccessToken' => config('app.access_token_1'),
        //                     'DocumentContent' => $document_content,
        //                     'Notes' => $form->type
        //                 ]
        //             ])
        //         ]
        //         // 'model' => [
        //         // ]
        //     ])
        //     ->throw(function ($response, $e) {
        //         throw new \Exception(json_encode($e->getMessage()), 1);
        //         // throw new \Exception(json_encode($e->getTrace()), 1);
        //     });
        // throw new \Exception(json_encode($response->throw()), 1);

        if ($response['MessageType'] == 'error') {
            return [
                'error' => true,
                'message' => $response->collect()['Message']
            ];
        }

        //return response()->json($response->collect());

        Document::where('id', '=', $form->id)
            ->update([
                'status' => 'pending'
            ]);
    }

    /**
     * @param $name
     * @return mixed
     */
    protected function approverIdByName($name)
    {
        $user = User::where('name', $name)->first();
        return $user->id;
    }
}
