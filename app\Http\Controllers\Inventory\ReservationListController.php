<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Resv\ResvHeader;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReservationListController extends Controller
{
    public function index(Request $request)
    {
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        $searchType = isset($request->searchType) ? $request->searchType : null;
        $search = isset($request->search) ? $request->search : "";
        $authUser = $request->user();

        $data = ResvHeader::leftJoin('resv_details', 'resv_headers.U_DocEntry', '=', 'resv_details.U_DocEntry')
            ->when($searchType, function ($query) use ($searchType, $search) {
                switch ($searchType) {
                    case 'DocNum':
                        $query->where('resv_headers.DocNum', 'like', '%' . $search . '%');
                        break;

                    case 'Item Code':
                        $query->where('resv_details.ItemCode', 'like', '%' . $search . '%');
                        break;

                    case 'Item Name':
                        $query->where('resv_details.ItemName', 'like', '%' . $search . '%');
                        break;

                    case 'Whs':
                        $query->where('resv_headers.WhsCode', 'like', '%' . $search . '%');
                        break;

                    case 'Employee Name':
                        $query->where('resv_details.EmployeeName', 'like', '%' . $search . '%');
                        break;

                    case 'Employee NIK':
                        $query->where('resv_details.EmployeeId', 'like', '%' . $search . '%');
                        break;

                    case 'No GIR':
                        $query->where('resv_headers.SAP_GIRNo', 'like', '%' . $search . '%');
                        break;
                }
            })
            ->when($authUser, function ($query) use ($authUser) {
                if ($authUser->hasAnyRole(['Superuser', 'Admin E-RESEVATION SAFETY INVENTORY'])) {
                    $query->whereIn("resv_headers.WhsCode", ['MW-HSE', 'IG04'])
                        ->whereRaw("resv_details.LineEntry not in (
                        SELECT ISNULL(B.resv_detail_id, 0)
                        from inventories as A
                        left join inventory_details as B on A.id = B.header_id AND A.status in ('O', 'C', 'D')
                    )")
                        // ->whereNotNull("resv_details.EmployeeId")
                        ->select(
                            "resv_headers.DocNum",
                            "resv_headers.DocDate",
                            "resv_headers.WhsCode",
                            "resv_headers.Department",
                            "resv_headers.SAP_GIRNo",
                            "resv_details.LineEntry",
                            "resv_details.ItemCode",
                            "resv_details.ItemName",
                            "resv_details.ReqNotes",
                            "resv_details.ReqQty",
                            "resv_details.UomCode",
                            "resv_details.EmployeeId",
                            "resv_details.EmployeeName",
                            DB::raw("
                            CONVERT(VARCHAR,
                                CASE
                                    WHEN resv_headers.ExpiredDate is not null THEN resv_headers.ExpiredDate
                                    ELSE DATEADD(day, 7, resv_headers.updated_at)
                                END,
                                23) as ExpiredDate
                            ")
                        );
                }
            })
            ->whereNotNull("resv_headers.SAP_GIRNo")
            ->whereNotIn("resv_headers.DocStatus", ['C'])
            ->orderBy("resv_headers.DocNum", "desc")
            ->paginate($row_data);

        return $this->success([
            'rows' => $data->items(),
            'total' => $data->total(),
        ]);
    }
}
