<?php

namespace App\Http\Controllers\Approval;

use App\Http\Controllers\Controller;
use App\Models\Approval\ApprovalStage;
use App\Models\Document\Document;
use App\Services\ApprovalStagesService;
use App\Traits\AppConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ApprovalStagesController extends Controller
{
    use AppConfig;

    protected $service;

    public function __construct(ApprovalStagesService $service)
    {
        $this->service = $service;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stages(Request $request)
    {
        $doc_id = $request->doc_id;
        $document = Document::find($doc_id);
        // TODO : check schema approval
        // if ($document->document_type == 'internal') {
        if ($document->userCreate->hasAnyRole(['E-Sign Cherry Approval'])) {
            return $this->cherryApprovalStages($request, $document);
        } else {
            $data = ApprovalStage::where('document_id', $doc_id)
                ->whereNotNull('document_id')
                ->with(['approval', 'user'])
                ->get();

            return $this->success([
                'rows' => $data
            ]);
        }
    }

    protected function cherryApprovalStages($request, $document)
    {
        $cherry_token = $request->user()->cherry_token;

        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ];

        $documents = Http::withHeaders($headers)
            ->post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'GADocuments',
                'UserName' => $request->user()->username,
                'Token' => $cherry_token,
                'OrderBy' => 'InsertStamp',
                'OrderDirection' => 'Desc',
                'ParameterData' => [
                    [
                        'ParamKey' => 'DocumentReferenceID',
                        'ParamValue' => (isset($document->document_number)) ? $document->document_number : $document->document_number,
                        'Operator' => 'eq'
                    ]
                ]
            ]);

        // return response()->json($documents->collect(), 422);

        $collect = $documents->collect();

        if ($collect['MessageType'] == 'error') {
            throw new \Exception($collect['Message'], 1);
        }
        if ($collect['Data']) {
            // return response()->json($collect, 422);
            $list_code = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'ApprovalRequests',
                'UserName' => $request->user()->username,
                'Token' => $cherry_token,
                'ParameterData' => [
                    [
                        'ParamKey' => 'ModelEntityCode',
                        'ParamValue' => $collect['Data'][0]['Code'],
                        'Operator' => 'eq'
                    ]
                ]
            ]);

            //return response()->json($collect);

            $arr_result = [];
            foreach ($list_code->collect()['Data'] as $datum) {
                $arr_result[] = [
                    'Keys' => $datum['Code'],
                    'ApproverEmployeeName' => $datum['ApproverEmployeeName'],
                    'StatusId' => $datum['StatusId'],
                    'ResponseDates' => $datum['ResponseDate'],
                    'ResponseDate' => ($datum['ResponseDate']) ?
                        date('Y-m-d H:i:s', (int)substr($datum['ResponseDate'], 6, 10)) : '',
                    'Notes' => $datum['ApprovalNotes'],
                    'ApprovalSchemaName' => $datum['ApprovalSchemaName'],
                ];
            }

            return $this->success([
                'rows' => $arr_result,
                'total' => count($list_code->collect()['Data'])
            ]);
        } else {
            return $this->success([
                'rows' => [],
                'total' => 0
            ]);
        }
    }

    public function index(Request $request)
    {
        $status = $request->status;
        $user = $request->user;
        Log::error('user dari sap approval', [
            'error' => $user,
        ]);
        if ($user == 'manager') {
            $user = '';
        }
        $rows = [];
        $stages = ApprovalStage::whereNotNull('document_id')
            ->leftJoin('users', 'users.id', 'approval_stages.user_id')
            ->select('approval_stages.document_id')
            ->where('approval_stages.status', $status)
            ->where('users.username', 'LIKE',  '%' . $user . '%')
            ->distinct()
            ->pluck('document_id');

        // return $user;

        $query = $this->queryDocument($stages);

        $other = $this->queryDocument($stages)->where('documents.document_type', 'other');
        $invoice = $this->queryDocument($stages)->where('documents.document_type', 'invoice');

        $totalOther = $other->count();
        $dataOther = $other->get();

        $totalInvoice = $invoice->count();
        $dataInvoice = $invoice->get();


        return $this->success([
            'rowsInvoice' => $dataInvoice,
            'rowsOther' => $dataOther,
            'totalInvoice' => $totalInvoice,
            'totalOther' => $totalOther,
            'document_status' => ['pending', 'approved', 'rejected'],
            'search_item' => ['Document Number', 'Document Type'],
        ]);
    }

    public function queryDocument($stages)
    {
        return Document::select(
            'documents.*',
            'document_number as paper_no',
            'document_sub_types.name as document_sub_type_name',
            'users.name as user_name'
        )
            ->leftJoin('customers', 'customers.id', 'documents.customer_id')
            ->leftJoin('users', 'users.id', 'documents.created_by')
            ->leftJoin('document_sub_types', 'document_sub_types.id', 'documents.document_sub_type_id')
            ->with(['attachment', 'userCreate', 'approver', 'approver.user'])
            ->whereIn('documents.id', $stages)
            ->orderBy('documents.document_number', 'desc');
    }



    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelDocument(Request $request)
    {
        return null;
        //        try {
        //            $rows = json_decode(json_encode($request->rows), false);
        //            $formApproval = json_decode(json_encode($request->formApproval), false);
        //            ApprovalStage::where('document_id', $doc_id)
        //                ->update([
        //                    'status' => 'canceled',
        //                    'cancel_by' => $request->user()->id,
        //                    'cancel_date' => Carbon::now()
        //                ]);
        //
        //            Document::where('id', $doc_id)
        //                ->update([
        //                    'status' => 'canceled'
        //                ]);
        //
        //
        //            return $this->success('document canceled');
        //        } catch (\Exception $exception) {
        //            return $this->error($exception->getMessage(), 422, [
        //                'trace' => $exception->getTrace()
        //            ]);
        //        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|null
     */
    public function approveDocument(Request $request)
    {
        return null;
        //        try {
        //            $rows = json_decode(json_encode($request->rows), false);
        //            $formApproval = json_decode(json_encode($request->formApproval), false);
        //            ApprovalStage::where('document_id', $doc_id)
        //                ->update([
        //                    'status' => 'approved',
        //                    'notes' => $notes,
        //                    'response_date' => Carbon::now()
        //                ]);
        //
        //            Document::where('id', $doc_id)
        //                ->update([
        //                    'status' => 'approved'
        //                ]);
        //
        //            return $this->success('document updated');
        //        } catch (\Exception $exception) {
        //            return $this->error($exception->getMessage(), 422, [
        //                'trace' => $exception->getTrace()
        //            ]);
        //        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function rejectDocument(Request $request)
    {
        return null;
        //        try {
        //            $rows = json_decode(json_encode($request->rows), false);
        //            $formApproval = json_decode(json_encode($request->formApproval), false);
        //            ApprovalStage::where('document_id', $doc_id)
        //                ->update([
        //                    'status' => 'rejected',
        //                    'notes' => $notes,
        //                    'response_date' => Carbon::now()
        //                ]);
        //
        //            Document::where('id', $doc_id)
        //                ->update([
        //                    'status' => 'rejected'
        //                ]);
        //
        //            return $this->success('document updated');
        //        } catch (\Exception $exception) {
        //            return $this->error($exception->getMessage(), 422, [
        //                'trace' => $exception->getTrace()
        //            ]);
        //        }
    }
}
