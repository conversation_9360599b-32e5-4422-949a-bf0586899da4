<?php

namespace App\Services\Reports;

class IssueApdDetailService
{
    public function header()
    {
        return [
            'Resv No',
            'GIR_NO',
            'Internal GI NO',
            'Internal GI Date',
            'Department',
            'DocDate',
            'Memo',
            'RequesterName',
            'ItemCode',
            'ItemName',
            'ReqQty',
            'UoMCode',
            'EmployeeId',
            'EmployeeName',
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "DocNum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "GIR_NO",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "InternalGiNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "updated_at",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Department",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Memo",
                'width' => 200,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "RequesterName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemName",
                'width' => 300,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ReqQty",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "UoMCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "EmployeeId",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "EmployeeName",
                'width' => 200,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value['DocNum'],
                $value['GIR_NO'],
                $value['InternalGiNo'],
                $value['updated_at'],
                $value['Department'],
                $value['DocDate'],
                $value['Memo'],
                $value['RequesterName'],
                $value['ItemCode'],
                $value['ItemName'],
                $value['ReqQty'],
                $value['UoMCode'],
                $value['EmployeeId'],
                $value['EmployeeName'],
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
