<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreGoodsIssueRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'form.post_date' => 'required|date',
            'form.whs_code' => 'required|string',
            'form.notes' => 'required|string',
            'line_items.*.item_code' => 'required',
            'line_items.*.qty' => 'required',
            'line_items.*.whs_code' => 'required',
        ];
    }

    public function goodIssuePayload()
    {
        return collect($this->except(['line_items', 'form.created_user', 'form.updated_user']))
            ->merge([
                'created_by' => $this->isMethod('POST') ? $this->user()->id : $this->form['created_by'],
                'updated_by' => $this->isMethod('PUT') ? $this->user()->id : null,
            ]);
    }
}
