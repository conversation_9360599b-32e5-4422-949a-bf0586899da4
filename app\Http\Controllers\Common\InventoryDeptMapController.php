<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Http\Requests\Inventory\StoreDeptMappingRequest;
use App\Models\Common\InventoryDeptMap;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InventoryDeptMapController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = InventoryDeptMap::select(
            "id",
            "department",
            "prefix",
        )
            ->orderBy('prefix', 'asc')
            ->get();

        if (count($data) < 1) {
            $data = [
                [
                    "id" => null,
                    "department" => null,
                    "prefix" => null,
                ]
            ];
        }

        return $this->success([
            'rows' => $data,
            'columns' => [
                [
                    'data' => 'id',
                    'width' => 50,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'department',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'prefix',
                    'width' => 15,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'id',
                    'width' => 3,
                    'wordWrap' => false,
                    'renderer' => 'DeleteRenderer'
                ],
            ],
            'header' => ['Id', 'DEPARTMENT', 'PREFIX', ''],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(StoreDeptMappingRequest $request)
    {
        $details = collect($request->details);
        try {
            foreach ($details as $detail) {
                $data = [
                    'department' => $detail['department'],
                    'prefix' => $detail['prefix'],
                    'created_at' => date('Y-m-d'),
                    'updated_at' => date('Y-m-d'),
                ];

                $brand = InventoryDeptMap::where('id', '=', $detail['id'])->first();

                if (!$brand) {
                    DB::connection('sqlsrv')
                        ->table('inventory_dept_maps')
                        ->insert($data);
                } else {
                    DB::connection('sqlsrv')
                        ->table('inventory_dept_maps')
                        ->where('id', '=', $detail['id'])
                        ->update($data);
                }
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /* Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = InventoryDeptMap::where('depertment', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $type = $request->type;
            if ($type == 'button') {
                $data = json_decode($request->data);
                InventoryDeptMap::where('id', $data->id)->first();
            } else {
                $listId = $request->id;
                InventoryDeptMap::whereIn('id', $listId)->delete();
            }
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
