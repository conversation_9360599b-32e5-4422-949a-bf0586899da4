<?php

namespace App\Services;

use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ApprovalStagesService
{
    use AppConfig;
    use ApiResponse;

    /**
     * @param $document
     * @param $fileName
     * @return string|void
     */
    public function processSerialNumber($document, $fileName)
    {
        try {
            $url = $this->getConfigByName('GenerateSerialNumber');
            $token = $this->getConfigByName('Token');

            // process generate serial number with laravel http client
            if (empty($document->ref_token)) {
                $dataCheck = [
                    'document_id' => $document->id,
                    'batch_id' => $document->batch_id,
                    'name' => 'Generate Serial Number',
                ];
                // TODO jangan lupa uncommend
                $response = Http::withoutVerifying()
                    // ->withOptions(["verify" => false])
                    ->withToken($token)
                    ->post($url, [
                        'idfile' => $fileName->id,
                        'isUpload' => false,
                        'metadata' => "",
                        'namadoc' => $document->type_no,
                        'namafile' => $fileName->file_name,
                        'nilaidoc' => $document->value,
                        'namejidentitas' => $document->identity_type,
                        'noidentitas' => $document->identity_number,
                        'namedipungut' => $document->identity_name,
                        'snOnly' => false,
                        'nodoc' => $document->document_number,
                        'tgldoc' => $document->document_date,
                    ]);

                if ($response->failed()) {
                    $dataUpdate = [
                        'status' => 'Processing %',
                        'callback_message' => 'E-METERAI: ',
                        'callback_trace' => $response
                    ];
                    $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                }

                $response = $response->collect();

                if ($response['message'] == 'success') {
                    $this->createLog(
                        $document->id,
                        'document',
                        'E-meterai - Generate Serial Number: ' . $response['result']['sn'],
                        auth()->user()->id
                    );

                    // $document = Document::find($document->id);
                    $document->ref_token = $response['result']['sn'];
                    $document->save();


                    $serial = $document->ref_token;
                    $image = $response['result']['image'];
                    $image = str_replace('data:image/png;base64,', '', $image);
                    $image = str_replace(' ', '+', $image);
                    $imageName = $serial . '.' . 'png';
                    // put stamp file to local server
                    $stampImg = '/images/stamp/' . $imageName;
                    custom_disk_put($stampImg, base64_decode($image));
                    // File::put(public_path($stampImg), base64_decode($image));

                    // get pdf file
                    $pdf_file_path = '/Attachment/docs/' . $fileName->file_name;
                    create_file_delete_job($pdf_file_path);

                    // store stamp and unsigned file to shared server
                    $stamp = '/STAMP/' . $imageName;
                    custom_disk_put($stamp, custom_disk_get($stampImg), 'ftp');
                    // Storage::disk('ftp')->put($stamp, File::get(public_path($stampImg)));
                    // pdf file
                    $doc = '/UNSIGNED/' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';
                    custom_disk_put($doc, custom_disk_get($pdf_file_path, 'app_documents'), 'ftp');
                    // Storage::disk('ftp')->put($doc, File::get($pdf_file));

                    $dataUpdate = [
                        'status' => 'Processing %',
                        'callback_message' => 'Success ',
                        'callback_trace' => 'Success Generate Serial Number!'
                    ];
                    $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                    return 'Serial generated';
                } else {
                    $dataUpdate = [
                        'status' => 'Processing %',
                        'callback_message' => 'E-METERAI: ' . $response['result']['err'],
                        'callback_trace' => $response
                    ];
                    $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                    throw new \RuntimeException('E-METERAI: ' . $response['result']['err']);
                }
            }
        } catch (\Exception $exception) {
            // Log::critical('Failed Generate Serial Number', [
            //     'error' => $exception->getMessage(),
            //     'trace' => $exception->getTrace(),
            // ]);
            return $exception->getMessage();
        }
    }

    /**
     * @param $document
     * @param $fileName
     * @return string|void
     */
    public function processStampDocument($document, $fileName)
    {
        try {
            $token = $this->getConfigByName('Token');
            $url = $this->getConfigByName('Stamping');
            $serial = $document->ref_token;
            $imageName = $serial . '.' . 'png';
            $stamp = '/STAMP/' . $imageName;
            $doc = '/UNSIGNED/' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';
            $doc_final = '/SIGNED/final_' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';

            $dataCheck = [
                'document_id' => $document->id,
                'batch_id' => $document->batch_id,
                'name' => 'Stamp Meterai',
            ];

            if ($document->meterai_coordinate == 'Y') {
                if (!Storage::disk('ftp')->exists($doc_final)) {
                    // code...
                    $responseStamp = Http::withoutVerifying()
                        ->withToken($token)
                        ->post($url, [
                            'certificatelevel' => 'NOT_CERTIFIED',
                            'dest' => '/sharefolder' . $doc_final,
                            'docpass' => (!empty($document->password)) ? $document->password : '',
                            'jwToken' => $token,
                            'location' => $document->location,
                            'profileName' => $document->profile_name,
                            'reason' => $document->type,
                            'refToken' => $serial,
                            'spesimenPath' => '/sharefolder' . $stamp,
                            'src' => '/sharefolder' . $doc,
                            // 'retryFlag' => '1',
                            'visLLX' => doubleval($document->vis_llx),
                            'visLLY' => doubleval($document->vis_lly),
                            'visURX' => doubleval($document->vis_urx),
                            'visURY' => doubleval($document->vis_ury),
                            'visSignaturePage' => (int)$document->materai_page,
                        ]);


                    $responseStamp->onError(function ($callback) use ($dataCheck, $document) {
                        $dataUpdate = [
                            'status' => 'Processing %',
                            'callback_message' => 'Error',
                            'callback_trace' => $callback
                        ];
                        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                        throw new \RuntimeException($callback);
                    });
                    $responseStamp = $responseStamp->collect();

                    if ($responseStamp['errorCode'] == '00') {
                        $this->createLog(
                            $document->id,
                            'document',
                            'E-meterai - Stamp Document Serial Number: ' . $serial,
                            auth()->user()->id
                        );

                        $doc = Storage::disk('ftp')->get($doc_final);
                        Storage::disk('app_public')->put('/documents/' . $fileName->file_name, $doc);

                        $dataUpdate = [
                            'status' => 'Processing %',
                            'callback_message' => 'Success',
                            'callback_trace' => 'Success Stamp Meterai'
                        ];
                        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                    } else {
                        $dataUpdate = [
                            'status' => 'Processing %',
                            'callback_message' => 'Error stamp ' . $document->external_document_number,
                            'callback_trace' => $responseStamp['errorCode'] . ' ' . $responseStamp['errorMessage']
                        ];
                        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                        throw new \RuntimeException($responseStamp['errorCode'] . ' ' . $responseStamp['errorMessage']);
                    }
                } else {
                    $doc = Storage::disk('ftp')->get($doc_final);
                    Storage::disk('app_public')->put('/documents/' . $fileName->file_name, $doc);
                }
            }
        } catch (\Exception $exception) {
            // Log::critical('Failed Stamp Document', [
            //     'error' => $exception->getMessage(),
            //     'trace' => $exception->getTrace(),
            // ]);
            return $exception->getMessage();
        }
    }

    /**
     * @param $document
     * @param $fileName
     * @return bool|string
     */
    public function processSubmitDigitalSign($document, $fileName)
    {
        try {
            if ($document->meterai_coordinate == 'Y') {
                create_file_delete_job('/report/documents/' . $fileName->file_name);
                $pdf_file = public_path('/report/documents/' . $fileName->file_name);
            } else {
                create_file_delete_job('/Attachment/docs/' . $fileName->file_name);
                $pdf_file = public_path('/Attachment/docs/' . $fileName->file_name);
            }

            $dataCheck = [
                'document_id' => $document->id,
                'batch_id' => $document->batch_id,
                'name' => 'Submit Digital Sign',
            ];

            if ($document->digisign_coordinate == 'Y') {
                $url = $this->getConfigByName('SendDocument', 'ESIGN');
                $esign_user_id = $this->getConfigByName('UserIdEsign', 'ESIGN', $document->company);
                $esign_user_name = $this->getConfigByName('UserNameEsign', 'ESIGN', $document->company);
                $esign_user_email = $this->getConfigByName('UserEmailEsign', 'ESIGN', $document->company);
                $esign_key_user = $this->getConfigByName('KeyUserEsign', 'ESIGN', $document->company);
                $token = $this->getConfigByName('TokenEsign', 'ESIGN', $document->company);

                if (date('Y-m-d', strtotime($document->created_at)) < '2022-06-27') {
                    $doc_id = Str::slug(strtoupper($document->document_number));
                } else {
                    $doc_id = Str::slug(strtoupper($document->external_document_number));
                }

                $responseSubmitDigisign = Http::withoutVerifying()
                    ->retry(3, 100)
                    ->withOptions(["verify" => false])
                    ->withToken($token)
                    ->attach(
                        'file',
                        file_get_contents($pdf_file),
                        $fileName->file_name
                    )
                    ->post($url, [
                        'jsonfield' => json_encode([
                            'JSONFile' => [
                                'userid' => $esign_user_id,
                                'document_id' => $doc_id,
                                'payment' => '2',
                                'redirect' => true,
                                'send-to' => [
                                    [
                                        'name' => $esign_user_name,
                                        'email' => $esign_user_email,
                                    ],
                                ],
                                'req-sign' => [
                                    [
                                        'name' => $esign_user_name,
                                        'email' => $esign_user_email,
                                        'aksi_ttd' => 'at',
                                        'kuser' => $esign_key_user,
                                        'user' => 'ttd1',
                                        'page' => $document->sign_page,
                                        'llx' => $document->vis_digisign_llx,
                                        'lly' => $document->vis_digisign_lly,
                                        'urx' => $document->vis_digisign_urx,
                                        'ury' => $document->vis_digisign_ury,
                                        'visible' => '1',
                                    ],
                                ],
                            ],
                        ]),
                    ]);

                $responseSubmitDigisign->onError(function ($callback) use ($doc_id, $dataCheck, $document) {
                    $dataUpdate = [
                        'status' => 'Processing %',
                        'callback_message' => 'Error document_id: ' . $doc_id,
                        'callback_trace' => $callback
                    ];
                    $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                    throw new \RuntimeException($callback);
                });

                if ($responseSubmitDigisign->collect()['JSONFile']['result'] != '00') {
                    if ($responseSubmitDigisign->collect()['JSONFile']['notif'] != 'Document_id sudah digunakan') {
                        $dataUpdate = [
                            'status' => 'Processing %',
                            'callback_message' => 'Error document_id: ' . $doc_id,
                            'callback_trace' => $responseSubmitDigisign->collect()['JSONFile']['notif']
                        ];
                        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                        throw new \RuntimeException(
                            'E-METERAI: ' . $responseSubmitDigisign->collect()['JSONFile']['notif']
                        );
                    }
                } else {
                    $dataUpdate = [
                        'status' => 'Processing %',
                        'callback_message' => 'Success document_id: ' . $doc_id,
                        'callback_trace' => 'Success Submit Digital Sign'
                    ];
                    $this->createApprovalBatchLog($dataCheck, $dataUpdate);

                    $this->createLog(
                        $document->id,
                        'document',
                        'Digisign - Submit autosign document id: ' . $doc_id . ', userid: ' . $esign_user_id,
                        auth()->user()->id
                    );
                }
            }
            return true;
        } catch (\Exception $exception) {
            // Log::critical('Failed Submit Digital Sign', [
            //     'error' => $exception->getMessage(),
            //     'trace' => $exception->getTrace(),
            // ]);

            return $exception->getMessage();
        }
    }
}
