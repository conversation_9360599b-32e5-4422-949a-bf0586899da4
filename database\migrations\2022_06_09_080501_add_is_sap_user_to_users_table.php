<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsSapUserToUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('is_sap_user', 10)->nullable()->default('N');
        });

        Schema::create('approvals', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->char('final_status', 30)->default('pending');
            // $table->morphs('document');
            $table->text('callback')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('approval_rules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('approval_id');
            $table->string('name');
            $table->string('operator');
            $table->text('value');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('approval_approver', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('approval_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedInteger('sequence');
            $table->string('types', 50);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('approval_stages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('approval_id');
            $table->unsignedBigInteger('user_id');
            $table->char('status', 30)->default('pending'); // pending, approve, reject
            $table->string('notes')->nullable();
            $table->dateTime('response_date');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            //
        });

        Schema::dropIfExists('approvals');
        Schema::dropIfExists('approval_stages');
        Schema::dropIfExists('approval_rules');
    }
}
