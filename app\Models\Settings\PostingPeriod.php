<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\PostingPeriod
 *
 * @property int $id
 * @property string $period_name
 * @property string $start_period
 * @property string $end_period
 * @property string $is_locked
 * @property int $created_by
 * @property int $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod query()
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod whereEndPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod whereIsLocked($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod wherePeriodName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod whereStartPeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PostingPeriod whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class PostingPeriod extends Model
{
    use HasFactory;
}
