<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\MappingWhs
 *
 * @property int $id
 * @property string $whs_code_b1
 * @property string $whs_code_s4
 * @property string $plan
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs query()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs wherePlan($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs whereWhsCodeB1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingWhs whereWhsCodeS4($value)
 * @mixin \Eloquent
 */
class MappingWhs extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';

    protected $guarded = [];

}
