<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Traits\ApiResponse;

class SuperAppsApiMiddleware
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // return $this->errorApps('', 422, [$request->Token], $request);
        $token = $request->header('Token');
        if ($token != config('app.superapps_token')) {
            return $this->errorApps('Token credentials not match!!', '200', [], $request);
        }
        return $next($request);
    }
}
