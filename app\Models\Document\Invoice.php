<?php

namespace App\Models\Document;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Document\Invoice
 *
 * @property int $id
 * @property string $title
 * @property string $customer
 * @property string $address
 * @property string $document_number
 * @property string $document_date
 * @property string $due_date
 * @property string $sub_total
 * @property string|null $vat
 * @property string $amount
 * @property string $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $document_id
 * @property int $created_by
 * @property string $company
 * @property-read \App\Models\Document\Document|null $document
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Document\InvoiceItem> $lineItems
 * @property-read int|null $line_items_count
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice query()
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereCustomer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereDocumentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereDocumentNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereDueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereSubTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Invoice whereVat($value)
 * @mixin \Eloquent
 */
class Invoice extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $connection = 'sqlsrv';

    public function lineItems()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function document()
    {
        return $this->belongsTo(Document::class);
    }
}
