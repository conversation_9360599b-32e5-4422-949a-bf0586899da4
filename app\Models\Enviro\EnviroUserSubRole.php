<?php

namespace App\Models\Enviro;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Enviro\EnviroUserSubRole
 *
 * @property int $id
 * @property int $user_id
 * @property int $enviro_sub_role_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserSubRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserSubRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserSubRole query()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserSubRole whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserSubRole whereEnviroSubRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserSubRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserSubRole whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserSubRole whereUserId($value)
 * @mixin \Eloquent
 */
class EnviroUserSubRole extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
