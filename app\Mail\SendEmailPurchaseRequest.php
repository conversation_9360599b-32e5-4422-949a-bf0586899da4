<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Services\ProcessPostSapS4Service;
use App\Services\SapS4Service;

class SendEmailPurchaseRequest extends Mailable
{
    use Queueable, SerializesModels;

    public $content;
    public $receiver;

    /**
     * Create a new message instance.
     *
     * @param $content
     */
    public function __construct($receiver, $content)
    {
        $this->content = $content;
        $this->receiver = $receiver;
    }
    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {


        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();

        // initialize post service
        $postService = new ProcessPostSapS4Service();

        // get document flow
        $flow = $service->getFlow($this->content['header']->DocNum);

        $girNo = null;
        $prNo = null;
        $poNo = null;
        $grNo = null;
        $giNo = null;

        
        if ($flow) {
            // throw new \Exception(json_encode($flow));
            if (array_key_exists('DATA', $flow)) {
                if ($flow['DATA'] != 'NULL') {
                    $girNo = (array_key_exists('RESERVASI', $flow['DATA'][0])) ? $flow['DATA'][0]['RESERVASI'] : null;
                    // $girNo =  null;
                    $prNo = (array_key_exists("PR", $flow['DATA'][0])) ? $flow['DATA'][0]['PR'] : null;
                }
                // $poNo = $flow['DATA'][0]['PO'];
                // $grNo = $flow['DATA'][0]['GR'];
                // $giNo = $flow['DATA'][0]['GI'];
            }
        }

        Log::info('Send email PR', [
            'PR NO' => $prNo
        ]);

        $email = $this->markdown('email.pr')
            // ->from('<EMAIL>')
            // ->from(config('mailers.smtp.username'))
            ->subject($this->content['subject'])
            ->with([
                'prNo' => $prNo,
                'receiver' => $this->receiver,
                'content' => $this->content,
                'header' => $this->content['header']
            ]);

        foreach ($this->content['attachment'] as $key => $value) {
            $email->attach($value);
        }

        return $email;
    }
}
