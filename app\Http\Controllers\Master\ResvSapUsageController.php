<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Master\ResvSapUsage;
use App\Models\View\ViewEmployee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class ResvSapUsageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = ResvSapUsage::select('id', 'division', 'work_location', 'movement_type', 'description')
            ->get();

        if (count($data) < 1) {
            $data = [
                [
                    'id' => null,
                    'division' => null,
                    'work_location' => null,
                    'movement_type' => null,
                    'description' => null,
                ]
            ];
        }

        $workLocation = ViewEmployee::select('WorkLocation')->distinct()->pluck('WorkLocation');
        $division = ViewEmployee::select('Department')
            ->whereIn('Company', ['PT IMIP', 'PT BDM'])
            ->distinct()
            ->orderBy('Department')
            ->pluck('Department');

        $workLocation = $request->workLocation;
        $user = User::where('id', $request->user()->id)->first();

        $dataUsage = ResvSapUsage::where('work_location', $request->workLocation)
                ->where('division', $request->division)                   
                ->get();

        return $this->success([
            'rows' => $data,
            'pluck' => $dataUsage,
            'columns' => [
                [
                    'data' => 'id',
                    'width' => 50
                ],
                [
                    'data' => 'division',
                    'width' => 40,
                    'type' => 'dropdown',
                    'source' => $division
                ],
                [
                    'data' => 'work_location',
                    'width' => 40,
                    'type' => 'dropdown',
                    'source' => $workLocation
                ],
                [
                    'data' => 'movement_type',
                    'width' => 10
                ],
                [
                    'data' => 'description',
                    'width' => 50
                ],
            ],
            'header' => ['Id', 'Division', 'Work Location', 'Movement Type', 'Description'],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        DB::beginTransaction();
        try {
            foreach ($details as $detail) {
                    $brand = ResvSapUsage::where('id', '=', $detail['id'])->first();
                if (!$brand) {
                    $brand = new ResvSapUsage();
                }
                $brand->division = $detail['division'];
                $brand->work_location = $detail['work_location'];
                $brand->movement_type = $detail['movement_type'];
                $brand->description = $detail['description'];
                $brand->created_by = $request->user()->id;
                $brand->save();
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = ResvSapUsage::where('category', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            ResvSapUsage::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
