<?php

namespace App\Models\Task;

use App\Models\Master\MasterPriority;
use App\Models\Master\MasterSubCategory;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Task\Task
 *
 * @property int $id
 * @property string $title
 * @property string|null $description
 * @property string $department
 * @property \Illuminate\Support\Carbon|null $due_date
 * @property int $order_line
 * @property int $section_id
 * @property int $user_id
 * @property int|null $priority_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $board_id
 * @property \Illuminate\Support\Carbon|null $start_date
 * @property string $task_number
 * @property bool $complete
 * @property bool $approve
 * @property int|null $task_temp_id
 * @property int|null $sub_category_id
 * @property-read MasterPriority|null $priority
 * @property-read User|null $requester
 * @property-read \App\Models\Task\TaskSection|null $section
 * @property-read MasterSubCategory|null $subCategory
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Task\TaskTagPeople> $tags
 * @property-read int|null $tags_count
 * @method static \Illuminate\Database\Eloquent\Builder|Task newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Task newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Task query()
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereApprove($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereBoardId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereComplete($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereDueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereOrderLine($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task wherePriorityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereSectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereSubCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereTaskNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereTaskTempId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Task whereUserId($value)
 * @mixin \Eloquent
 */
class Task extends Model
{
    use HasFactory;

    protected $table = 'tasks';

    protected $guarded = [];

    protected $casts = [
        'created_at' => 'datetime:Y-m-d',
        'due_date' => 'datetime:Y-m-d',
        'start_date' => 'datetime:Y-m-d',
        'sub_category_id' => 'integer',
        'priority_id' => 'integer',
    ];

    public function requester()
    {
        return $this->belongsTo(User::class);
    }

    public function section()
    {
        return $this->belongsTo(TaskSection::class);
    }

    public function subCategory()
    {
        return $this->belongsTo(MasterSubCategory::class, 'sub_category_id', 'id');
    }

    public function priority()
    {
        return $this->belongsTo(MasterPriority::class, 'priority_id', 'id');
    }

    public function tags()
    {
        return $this->hasMany(TaskTagPeople::class, 'task_id', 'id');
    }
}
