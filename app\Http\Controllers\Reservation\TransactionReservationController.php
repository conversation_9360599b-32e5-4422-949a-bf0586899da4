<?php

namespace App\Http\Controllers\Reservation;

use App\Http\Controllers\Controller;
use App\Models\Common\Attachment;
use App\Models\Master\ResvCostCenter;
use App\Models\Resv\ResvDetail;
use App\Models\Resv\ResvHeader;
use App\Models\User\UserCompany;
use App\Models\User\UserWhs;
use App\Models\View\ViewEmployee;
use App\Models\View\ViewApprovalStage;
use App\Services\ApprovalCherryService;
use App\Services\ReservationDataService;
use App\Services\ReservationService;
use App\Services\ReservationValidateDataService;
use App\Services\SapS4Service;
use App\Traits\Approval;
use App\Traits\CherryApproval;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Models\Common\Vehicle;
use PDO;

class TransactionReservationController extends Controller
{
    use Approval, CherryApproval;

    /**
     * TransactionReservationController constructor.
     */
    public function __construct()
    {
        $this->middleware(['direct_permission:Reservation Request-index'])->only(['index', 'show', 'maxDocResv']);
        $this->middleware(['direct_permission:Reservation Request-store'])->only('store');
        $this->middleware(['direct_permission:Reservation Request-edits'])->only('update');
        $this->middleware(['direct_permission:Reservation Request-erase'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        // return $this->error(env('LARAVEL_ODBC_HOST'));
        $options = json_decode($request->options);
        $year_local = date('Y');
        $pages = isset($request->page) ? (int) $request->page : 1;
        $filter = isset($request->filter) ? (string) $request->filter : $year_local;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;

        $sorts = isset($request->sortBy[0]) ? json_decode($request->sortBy[0])->key : 'DocDate';
        $order = isset($request->sortBy[0]) ? json_decode($request->sortBy[0])->order : 'desc';

        $type = isset($request->type) ? (string) $request->type : null;
        $search = isset($request->search) ? strtolower((string) $request->search) : "";
        $search_status = isset($request->searchStatus) ? (string) $request->searchStatus : "";
        $select_data = isset($request->searchItem) ? (string) $request->searchItem : "DocDate";
        $offset = $pages;
        $username = $request->user()->username;
        $user_id = $request->user()->username;
        // dd($user_id);

        // $db_name = config('app.db_sap');
        $schema = (config('app.db_schema') !== null) ? config('app.db_schema') : 'IMIP_ERESV';

        $resv = [];

        $itemIt = [];
        $itemIt1 = [];
        // if ($request->user()->hasAnyRole(['View Data IT', 'Superuser'])) {
        //     $itemIt1 = ResvDetail::select("U_DocEntry")
        //         ->whereRaw("LEFT(ItemCode, 5) in ('66104', '66101', '66102')")
        //         ->distinct()
        //         ->get()
        //         ->pluck('U_DocEntry');

        //     $ownItem = ResvHeader::select("U_DocEntry")
        //         ->where("CreatedBy", $user_id)
        //         ->get()
        //         ->pluck("U_DocEntry");

        //     $itemIt = array_merge($itemIt1->toArray(), $ownItem->toArray());

        //     // $resvIT = ResvHeader::whereIn("WhsCode", ["M"])
        // }

        $result = array();
        $query = ResvHeader::select(
            "resv_headers.*",
            "resv_headers.RequesterName as RequestName",
            DB::raw('
                CASE
                    WHEN resv_headers."ApprovalStatus" = \'W\' THEN \'Waiting\'
                    WHEN resv_headers."ApprovalStatus" = \'P\' THEN \'Pending\'
                    WHEN resv_headers."ApprovalStatus" = \'N\' THEN \'Rejected\'
                    WHEN resv_headers."ApprovalStatus" = \'Y\' THEN \'Approved\'
                    WHEN resv_headers."ApprovalStatus" = \'-\' THEN \'-\'
                END AS "AppStatus"
            '),
            DB::raw('
                CASE
                    WHEN resv_headers."DocStatus" = \'D\' THEN \'Draft\'
                    WHEN resv_headers."DocStatus" = \'C\' THEN \'Cancel\'
                    WHEN resv_headers."RequestType" = \'Restock\' AND resv_headers."DocStatus" = \'O\' THEN \'Open\'
                    WHEN resv_headers."DocStatus" = \'O\' THEN \'Open\'
                END AS "DocumentStatus"
            ')
        )

            // $query = DB::connection('sqlsrv')->table( $schema .'.RESV_H')

            ->when($search, function ($query) use ($select_data, $search, $schema) {
                $data_query = $query;
                switch ($select_data) {
                    case 'DocNum':
                        $data_query->whereRaw('"DocNum" LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Company':
                        $data_query->whereRaw('LOWER("Company") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Warehouse':
                        $data_query->whereRaw('LOWER("WhsCode") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Req Name':
                        $data_query->whereRaw('LOWER("RequesterName") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Created Name':
                        $data_query->whereRaw('LOWER("CreatedName") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Req Type':
                        $data_query->whereRaw('LOWER("RequestType") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Req Date':
                        $data_query->whereRaw('LOWER("RequiredDate") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'App Status':
                        $data_query->whereRaw('LOWER("ApprovalStatus") LIKE( \'%' . $search . '%\') ');
                        break;

                    case 'Item Type':
                        $data_query->whereRaw('LOWER("ItemType") LIKE( \'%' . $search . '%\') ');
                        break;

                    case 'Asset Code':
                        $data_query->whereRaw('resv_headers.U_DocEntry IN ( (SELECT resv_details."U_DocEntry"
                                FROM resv_details
                                where LOWER(resv_details."AssetCode") LIKE( \'%' . $search . '%\')) )');
                        break;

                    case 'Item Code':
                        $data_query->whereRaw('resv_headers.U_DocEntry IN ( (SELECT resv_details."U_DocEntry"
                                FROM resv_details
                                where LOWER(resv_details."ItemCode") LIKE( \'%' . $search . '%\')) )');
                        break;

                    case 'Remark':
                        $data_query->whereRaw('LOWER("Memo") LIKE( \'%' . $search . '%\') ');
                        break;

                    case 'Employee Id':
                        $data_query->whereRaw('resv_headers.U_DocEntry IN ( (SELECT resv_details."U_DocEntry"
                                FROM resv_details
                                where LOWER(resv_details."EmployeeId") LIKE( \'%' . $search . '%\')) )');
                        break;

                    case 'Item Name':
                        $data_query->whereRaw('resv_headers.U_DocEntry IN ( (SELECT resv_details."U_DocEntry"
                                FROM resv_details
                                where LOWER(resv_details."ItemName") LIKE( \'%' . $search . '%\')) )');
                        break;
                }

                return $data_query;
            })
            ->when($search_status, function ($query) use ($search_status) {
                $data_query = $query;
                switch ($search_status) {
                    case '-':
                        $data_query->whereRaw('"ApprovalStatus" = \'-\' ');
                        break;
                    case 'Waiting':
                        $data_query->whereRaw('"ApprovalStatus" = \'W\' ');
                        break;
                    case 'Approved':
                        $data_query->whereRaw('"ApprovalStatus" = \'Y\' ');
                        break;
                    case 'Rejected':
                        $data_query->whereRaw('"ApprovalStatus" = \'N\' ');
                        break;
                    case 'Cancel':
                        $data_query->whereRaw('"DocStatus" = \'C\' ');
                        break;
                    case 'All':
                        $data_query->whereRaw('"ApprovalStatus" LIKE \'%%\' ');
                        break;
                }
                return $data_query;
            })
            ->when($type, function ($query) use ($type, $user_id) {
                $data_query = $query;
                switch ($type) {
                    case 'Draft':
                        $data_query->whereRaw('"ApprovalStatus" = \'-\' ')
                            ->where("resv_headers.CreatedBy", "=", $user_id);
                        break;
                    case 'Waiting':
                        $data_query->whereRaw('"ApprovalStatus" = \'W\' ')
                            ->where("resv_headers.CreatedBy", "=", $user_id);
                        break;
                    case 'Approved':
                        $data_query->whereRaw('"ApprovalStatus" = \'Y\' ')
                            ->where("resv_headers.CreatedBy", "=", $user_id);
                        break;
                    case 'Rejected':
                        $data_query->whereRaw('"ApprovalStatus" = \'N\' ')
                            ->where("resv_headers.CreatedBy", "=", $user_id);
                        break;
                    case 'All':
                        $data_query->whereRaw('"ApprovalStatus" LIKE \'%%\' ');
                        break;
                }
                return $data_query;
            })
            ->when($request, function ($query) use ($username, $user_id, $request, $itemIt) {
                // if ($request->user()->is_superuser == 'Yes') {
                //     $query->whereIn("resv_headers.U_DocEntry", $itemIt);
                // }
                if ($request->user()->is_superuser == 'No') {
                    if ($request->user()->hasAnyRole('View Data IT')) {
                        if (!isset($request->search)) {
                            $query->leftJoin("resv_details", "resv_details.U_DocEntry", "resv_headers.U_DocEntry")
                                ->where("resv_headers.CreatedBy", "=", $user_id)
                                ->orwhereRaw("resv_headers.WhsCode in ('IG03')")
                                ->orwhereRaw("LEFT(ItemCode, 5) in ('66104', '66101', '66102')");
                        } else {
                            $query->leftJoin("resv_details", "resv_details.U_DocEntry", "resv_headers.U_DocEntry");
                            // ->where("resv_headers.CreatedBy", "=", $user_id)
                            // ->whereRaw("resv_headers.WhsCode in ('IG03', 'IG01', 'MW-ZZ', 'MW-IT')")
                            // ->whereRaw("LEFT(ItemCode, 5) in ('66104', '66101', '66102')");
                        }
                        // $query->whereIn("resv_headers.U_DocEntry", $itemIt);
                        // throw new \Exception(json_encode($resv), 1);
                    } elseif ($request->user()->hasAnyRole(['Dept Head', 'Admin E-RESERVATION CAN SHOW EACH OTHER'])) {
                        $department = ResvHeader::select("Department")->where("CreatedBy", $request->user()->username)->get()->pluck("Department")->toArray();
                        $query->whereIn('Department', array_merge($department, $request->user()->userDivision()->pluck('division_name')->toArray()))
                            ->whereIn("WorkLocation", $request->user()->userWorLocation()->pluck('work_location'));
                    } elseif ($request->user()->hasAnyRole(['Admin E-RESERVATION BDM WAREHOUSE'])) {
                        $query->whereIn('WorkLocation', ['BDM MOROWALI']);
                    } elseif ($request->user()->hasAnyRole(['Admin E-RESERVATION IMIP WAREHOUSE'])) {
                        $query->whereIn('WorkLocation', ['IMIP MOROWALI', 'BDT MOROWALI']);
                    } else {
                        $query->where("resv_headers.CreatedBy", "=", $user_id);
                    }
                }
            });

        $result["total"] = $query->count();

        $all_data = $query->orderBY('resv_headers.DocDate', $order)
            ->paginate($row_data)
            ->items();

        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();

        $flow = $service->getFlow(
            collect($all_data)->pluck('DocNum')->toArray()
        );

        $dataFlow = [];

        if ($flow) {
            if (array_key_exists('DATA', $flow) && $flow['DATA'] != 'NULL') {
                foreach ($flow['DATA'] as $index => $item) {
                    $dataFlow[] = [
                        "DocNum" => (array_key_exists('ERESERVASI', $flow['DATA'][$index])) ? $flow['DATA'][$index]['ERESERVASI'] : null,
                        "SAP_GIRNo" => (array_key_exists('RESERVASI', $flow['DATA'][$index])) ? $flow['DATA'][$index]['RESERVASI'] : null,
                        "SAP_PRNo" => (array_key_exists("PR", $flow['DATA'][$index])) ? $flow['DATA'][$index]['PR'] : null,
                        "PONum" => (array_key_exists("PO", $flow['DATA'][$index])) ? $flow['DATA'][$index]['PO'] : null,
                        "GRPONum" => (array_key_exists("GR", $flow['DATA'][$index])) ? $flow['DATA'][$index]['GR'] : null,
                        "SAP_GINo" => (array_key_exists("GI", $flow['DATA'][$index])) ? $flow['DATA'][$index]['GI'] : null,
                        "REJECT" => (array_key_exists("REJECT", $flow['DATA'][$index])) ? $flow['DATA'][$index]['REJECT'] : "N",
                    ];
                }
            }
        }


        $filter = [
            "DocNum",
            "Company",
            "Warehouse",
            "Req Name",
            "Req Type",
            "Req Date",
            "App Status",
            "Remark",
            "Item Name",
            "Created Name",
            "Item Type",
            "Item Code",
            "Asset Code",
            "Employee Id"
        ];

        $document_status = ['All', 'Waiting', 'Approved', 'Rejected', 'Cancel', '-'];

        $itemType = ($request->user()->is_admin_subwh == 'N') ? ['Normal'] : ['Normal', 'Restock'];



        $collectFlowFix = collect($dataFlow);
        $collectionFix = collect($all_data);

        // Iterate through the collectionFix
        $collectionFix->each(function ($item, $key) use ($collectFlowFix) {
            $filteredFix = $collectFlowFix->whereIn('DocNum', [$item['DocNum']])->first();
            $countNPB = ResvDetail::where("U_DocEntry", $item["U_DocEntry"])
                ->where("RequestType", "NPB")
                ->count();
            if ($filteredFix) {
                $docStatus = 'Open';
                if ($item['DocStatus'] == 'C') {
                    $docStatus = 'Cancel';
                } else if ($filteredFix['REJECT'] == "Y") {
                    $docStatus = 'Reject';
                } else if ($item['WorkLocation'] == 'IMIP JAKARTA' && !empty($filteredFix['PONum'])) {
                    $docStatus = 'Closed';
                } elseif ($item['ItemType'] == 'Ready Stock' && !empty($filteredFix['SAP_GINo'])) {
                    $docStatus = 'Closed';
                } elseif (Str::contains($item['ItemType'], ['Non Ready Stock', 'Asset', 'Service']) && !empty($filteredFix['SAP_GINo']) && $countNPB > 0) {
                    $docStatus = 'Closed';
                } elseif (Str::contains($item['ItemType'], ['Non Ready Stock', 'Asset', 'Service']) && !empty($filteredFix['GRPONum'])) {
                    $docStatus = 'Closed';
                }
                //  elseif ($item['AppStatus'] == 'Approved' && ($filteredFix['SAP_GIRNo'] && $filteredFix['SAP_PRNo'])) {
                //     $docStatus = 'Canceled';
                // }

                $item['DocumentStatus'] = $docStatus;
                $item['SAP_GIRNo'] = $filteredFix['SAP_GIRNo'];
                $item['SAP_PRNo'] = $filteredFix['SAP_PRNo'];
                $item['PONum'] = $filteredFix['PONum'];
                $item['GRPONum'] = $filteredFix['GRPONum'];
                $item['SAP_GINo'] = $filteredFix['SAP_GINo'];
                $item['REJECT'] = $filteredFix['REJECT'];
            } else {
                $item['SAP_GIRNo'] = null;
                $item['SAP_PRNo'] = null;
                $item['PONum'] = null;
                $item['GRPONum'] = null;
                $item['SAP_GINo'] = null;
                $item['REJECT'] = "N";
            }
        });

        $dataService = new ReservationDataService();
        $result = array_merge($result, [
            // 'flow' => $flow,
            // "rows" => $all_data,

            "rows" => $collectionFix->all(),
            "filter" => $filter,
            "itemIt" => $itemIt,
            'status' => $document_status,
            "configDetails" => $service,
            'apdNotes' => $dataService->getApdNotes(),
            'flow' => $dataFlow
        ]);

        return response()->json($result);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $disableInput = $this->getConfigByName('DisableInput', 'GENERAL');
        if ($disableInput == '1') {
            return $this->error('System is under maintenance due SAP Data Migration!!');
        }
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422);
        }

        $details = collect($request->details);
        $form = $request->form;

        if ($form['Is_Urgent'] == 'Yes' && empty($form['UrgentReason'])) {
            return $this->error("Request Type Urgent Required Reason For That!", 422);
        }

        // if (Str::contains($form['WorkLocation'], ['IMIP MOROWALI']) ) {
        //     if ($form['Department'] == "IT - NETWORK" && empty($form['Usage'])) {
        //         return $this->error("Usage Field Is Required!", 422);
        //     }
        // }


        $doc_num = null;
        // get header
        $validationService = new ReservationValidateDataService();
        $validationService->validateDetails($details, $form, $request);
        DB::beginTransaction();
        try {
            $header = null;
            if ($form['Token']) {
                $header = ResvHeader::where("Token", "=", $form['Token'])->first();
            }
            // set created at
            $created = (!empty($header)) ? $header->created_at : Carbon::now();


            $doc_entry = $this->processHeaderDoc($header, $created, $request);
            if ($doc_entry) {
                foreach ($details as $index => $items) {
                    $line = ($index + 1);
                    // if($header->ItemType == 'Non Ready Stock') {
                    //     if (!array_key_exists('U_ATTACH', $items)) {
                    //         if (empty($items['U_ATTACH'])) {
                    //             return $this->error("Line ".($index + 1).": Document must have attachment!", '422');
                    //         }
                    //     }
                    // }
                    // Saved the data
                    $this->saveData(
                        $line,
                        $items,
                        $request,
                        $form,
                        $doc_entry
                    );
                } // Details
                $is_approval = $request->approval;
                DB::commit();
                if ($is_approval) {
                    if (empty($request->details)) {
                        return $this->error("Details is empty!", 422);
                    }

                    $header = ResvHeader::where('U_DocEntry', '=', $doc_entry)->first();
                    //app/traits/cherryapproval.php
                    $approvalService = new ApprovalCherryService();
                    return $this->submitApproval($header, $details, $request);
                    // return $approvalService->submitApproval($header, $details, $request);
                } else {
                    return $this->success([
                        "U_DocEntry" => $doc_entry
                    ], ($doc_entry != 'null') ? "Data updated!" : "Data inserted!");
                }
            } else {
                return $this->error("Failed process header!", 422);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            // Log::info('error reservasi', [
            //     'trace' => $exception->getTraceasString()
            // ]);
            return $this->error($exception->getMessage(), '422', [
                // 'trace' => ''
                'trace' => $exception->getTrace()
            ]);
        }
    }


    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $messages = [
            'form.CompanyName' => 'Company Name is required!',
            'form.DocDate.required' => 'Request Date is required!',
            'form.DocDate.before_or_equal' => 'Request date must be before or qual to today!',
            'form.RequiredDate.required' => 'Required Date is required!',
            'form.RequestType.required' => 'Request Type is required!',
            'form.U_NIK.required' => 'Requester NIK is required!',
            'form.Memo.required' => 'Notes is required!',
        ];

        $validator = Validator::make($request->all(), [
            'form.CompanyName' => 'required',
            'form.DocDate' => 'required|date|before_or_equal:today',
            'form.RequiredDate' => 'required',
            'form.RequestType' => 'required',
            'form.U_NIK' => 'required',
            'form.Memo' => 'required',
        ], $messages);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * @param $id
     * @param Request $request
     */
    private function getHeaderDoc($id, Request $request)
    {
        $header = null;
        if ($id) {
            $header = ResvHeader::where("U_DocEntry", "=", $id)->first();
        }
        return $header;
    }

    /**
     * @param $header
     * @param $created
     * @param $request
     *
     * @return int|mixed
     */
    protected function processHeaderDoc($header, $created, $request)
    {
        $employee = ViewEmployee::where('Nik', $request->form['U_NIK'])->first();
        // Log::info('memo', [
        //     $request->form['Memo']
        // ]);

        $service = new ReservationDataService();

        $data = [
            'WorkLocation' => $employee->WorkLocation,
            'Company' => $request->form['CompanyName'],
            'Customer' => $request->form['Customer'],
            'RequiredDate' => $request->form['RequiredDate'],
            'DocDate' => $request->form['DocDate'],
            'RequestType' => $request->form['RequestType'],
            'ItemType' => $request->form['ItemType'],
            'Memo' => $request->form['Memo'],
            'U_NIK' => $request->form['U_NIK'],
            'WhsCode' => $request->form['WhsCode'],
            'WhTo' => $request->form['WhTo'],
            'Requester' => $request->form['U_NIK'],
            'RequesterName' => $request->form['RequesterName'],
            'Division' => $request->form['Division'],
            'Department' => $request->form['Division'],
            'UrgentReason' => $request->form['UrgentReason'],
            'CostType' => (Str::contains($request->form['ItemType'], ['Ready Stock', 'Non Ready Stock'])) ? $request->form['CostType'] : null,
            'EmployeeType' => (array_key_exists('EmployeeType', $request->form)) ? $request->form['EmployeeType'] : null,
            'DocumentType' => (array_key_exists('DocumentType', $request->form)) ? $request->form['DocumentType'] : null,
            'Is_Urgent' => $request->form['Is_Urgent'] ?? 'No',
            'Replacement' => (array_key_exists('Replacement', $request->form)) ? $request->form['Replacement'] : null,
            'Mileage' => (array_key_exists('Mileage', $request->form)) ? $request->form['Mileage'] : null,
            'VehicleNo' => (array_key_exists('VehicleNo', $request->form)) ? $request->form['VehicleNo'] : null,
            'UsageFor' => (array_key_exists('UsageFor', $request->form)) ? $request->form['UsageFor'] : null,
            'Usage' => (array_key_exists('Usage', $request->form)) ? $request->form['Usage'] : null,
            'CategoryType' => (array_key_exists('CategoryType', $request->form)) ? $request->form['CategoryType'] : null,
            'CostCenter' => (array_key_exists('CostCenter', $request->form)) ? $request->form['CostCenter'] : null,
        ];
        if ($header) {
            $check_attachment = Attachment::where('source_id', '=', $header->U_DocEntry)
                ->where('type', 'reservation_header')
                ->first();
            if ($check_attachment) {
                $attachment = $check_attachment->file_path;
            } else {
                $attachment = '';
            }
            $add_data = [
                'UpdateDate' => date('Y-m-d'),
                'UpdateTime' => date('H:i:s'),
                'UpdatedBy' => Auth::user()->username,
                'U_ATTACH' => $attachment
            ];
            DB::connection('sqlsrv')
                ->table('resv_headers')
                ->where('U_DocEntry', '=', $header->U_DocEntry)
                ->update(array_merge($data, $add_data));

            $data_header = DB::connection('sqlsrv')
                ->table('resv_headers')
                ->where('U_DocEntry', '=', $header->U_DocEntry)
                ->first();
            $doc_num = $data_header->U_DocEntry;
        } else {
            $add_data = [
                'Token' => $request->form['Token'],
                'CreateDate' => date('Y-m-d'),
                'CreateTime' => date('H:i:s'),
                'DocNum' => $service->generateDocNum(date('Y-m-d H:i:s')),
                'CreatedBy' => Auth::user()->username,
                'CreatedName' => Auth::user()->name,
                'Canceled' => 'N',
                'DocStatus' => 'D',
                'ApprovalStatus' => '-',
            ];

            DB::connection('sqlsrv')
                ->table('resv_headers')
                ->insert(array_merge($data, $add_data));

            $doc_entry = ResvHeader::orderBy("U_DocEntry", "DESC")->first();
            $doc_num = $doc_entry->U_DocEntry;
        }
        return $doc_num;
    }

    /**
     * @param $details
     * @param $doc_entry
     * @param $form
     * @param $request
     * @param bool $is_approval
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function loopDetails($details, $doc_entry, $form, $request, bool $is_approval = false)
    {
        foreach ($details as $index => $items) {
            $line = ($index + 1);
            // Saved the data
            $this->saveData(
                $line,
                $items,
                $request,
                $form,
                $doc_entry
            );
        } // Details
        $is_approval = $request->approval;
        DB::commit();
        if ($is_approval) {
            $header = ResvHeader::where('U_DocEntry', '=', $doc_entry)->first();
            //app/traits/cherryapproval.php
            return $this->submitApproval($header, $details, $request);
        } else {
            return $this->success([
                "U_DocEntry" => $doc_entry
            ], ($doc_entry != 'null') ? "Data updated!" : "Data inserted!");
        }
    }

    /**
     * @param $index
     * @param $items
     * @param $request
     * @param $form
     * @param $doc_entry
     * @return mixed
     */
    protected function saveData($index, $items, $request, $form, $doc_entry)
    {
        $last_data = (array_key_exists('LineEntry', $items)) ? $items["LineEntry"] : null;
        $docs = ResvDetail::where("LineEntry", "=", $last_data)->first();
        if (auth()->user()->location == 'IMIP JAKARTA' && !array_key_exists("SPB", $items)) {
            $request_type = 'SPB';
        } else if ($items["SPB"] == 'Y') {
            $request_type = 'SPB';
        } else {
            $request_type = 'NPB';
        }

        $employeeName = null;
        $department = null;

        if ((array_key_exists('EmployeeId', $items))) {
            if (!empty($items['EmployeeId'])) {
                $employee = ViewEmployee::where('Nik', $items['EmployeeId'])->first();
                if ($employee) {
                    $employeeName = $employee->Name;
                    $department = $employee->Department;
                }
            }
        }

        if ($docs) {
            $check_attachment = Attachment::where('source_id', '=', $docs->LineEntry)
                ->where('type', 'reservation')
                ->first();
            if ($check_attachment) {
                $attachment = $check_attachment->file_path;
            } else {
                $attachment = '';
            }

            DB::connection('sqlsrv')
                ->table('resv_details')
                ->where('LineEntry', '=', $last_data)
                ->update([
                    'U_DocEntry' => $doc_entry,
                    'LineNum' => $index,
                    'ItemCode' => (array_key_exists('ItemCode', $items)) ? $items["ItemCode"] : null,
                    'OrderId' => $items["OrderId"],
                    'ItemName' => str_replace('”', '"', $items["ItemName"]),
                    'EmployeeName' => $employeeName,
                    'EmployeeId' => (array_key_exists('EmployeeId', $items)) ? $items["EmployeeId"] : null,
                    'VehicleNo' => (array_key_exists('VehicleNo', $items)) ? $items["VehicleNo"] : null,
                    'Mileage' => (array_key_exists('Mileage', $items)) ? $items["Mileage"] : null,
                    'AssetCode' => (array_key_exists('AssetCode', $items)) ? $items["AssetCode"] : null,
                    'U_Period' => (array_key_exists('U_Period', $items)) ? $items["U_Period"] : null,
                    'U_Category' => (array_key_exists('U_Category', $items)) ? $items["U_Category"] : null,
                    'U_AppResBy' => (array_key_exists('U_AppResBy', $items)) ? $items["U_AppResBy"] : null,
                    'U_Department' => $department,
                    'AssetName' => (array_key_exists('AssetName', $items)) ? $items["AssetName"] : null,
                    'ItemGroup' => (array_key_exists('ItemGroup', $items)) ? $items["ItemGroup"] : null,
                    'SubGroup' => (array_key_exists('SubGroup', $items)) ? $items["SubGroup"] : null,
                    'AvailableQty' => (array_key_exists('AvailableQty', $items)) ? $items["AvailableQty"] : 0,
                    'OnHand' => (array_key_exists('OnHand', $items)) ? $items["OnHand"] : 0,
                    'AvailableQtyDate' => (array_key_exists('AvailableQtyDate', $items)) ? Carbon::now() : null,
                    'ItemCategory' => (array_key_exists('ItemCategory', $items)) ? $items["ItemCategory"] : null,
                    'UoMCode' => (array_key_exists('UoMCode', $items)) ? $items["UoMCode"] : null,
                    'WhsCode' => $items["WhsCode"],
                    'ReqQty' => $items["ReqQty"],
                    'ReqDate' => date('Y-m-d', strtotime($items["ReqDate"])),
                    // 'ReqNotes' => $items["ReqNotes"],
                    // 'ReqNotes' => str_replace('”', '"', $items["ReqNotes"]),
                    // 'ReqNotes' => preg_replace('/[^\p{L}\p{N}\s]+/u', '', $items["ReqNotes"]),
                    'ReqNotes' => preg_replace("/[^a-zA-Z0-9 ]/", '', $items["ReqNotes"]),
                    'OtherResvNo' => (array_key_exists('OtherResvNo', $items)) ? $items["OtherResvNo"] : null,
                    'OIGRDocNum' => (array_key_exists('OIGRDocNum', $items)) ? $items["OIGRDocNum"] : null,
                    'InvntItem' => (array_key_exists('InvntItem', $items)) ? $items["InvntItem"] : null,
                    'RequestType' => $request_type,
                    'U_ATTACH' => $attachment
                ]);
        } else {
            $docs = new ResvDetail();
            $docs->U_DocEntry = $doc_entry;
            $docs->LineNum = $index;
            //$docs->LineEntry = ($line_entry) ? ($line_entry->LineEntry + 1) : 1;
            $docs->ItemCode = (array_key_exists('ItemCode', $items)) ? $items["ItemCode"] : null;
            $docs->OrderId = (array_key_exists('OrderId', $items)) ? $items["OrderId"] : null;
            $docs->ItemName = $items["ItemName"];
            $docs->ItemCategory = (array_key_exists('ItemCategory', $items)) ? $items["ItemCategory"] : null;
            $docs->EmployeeName = $employeeName;
            $docs->EmployeeId = (array_key_exists('EmployeeId', $items)) ? $items["EmployeeId"] : null;
            $docs->VehicleNo = (array_key_exists('VehicleNo', $items)) ? $items["VehicleNo"] : null;
            $docs->Mileage = (array_key_exists('Mileage', $items)) ? $items["Mileage"] : null;
            $docs->U_Department = $department;
            $docs->U_Period = (array_key_exists('U_Period', $items)) ? $items["U_Period"] : null;
            $docs->U_Category = (array_key_exists('U_Category', $items)) ? $items["U_Category"] : null;
            $docs->U_AppResBy = (array_key_exists('U_AppResBy', $items)) ? $items["U_AppResBy"] : null;
            $docs->UoMCode = (array_key_exists('UoMCode', $items)) ? $items["UoMCode"] : null;
            $docs->AssetCode = (array_key_exists('AssetCode', $items)) ? $items["AssetCode"] : null;
            $docs->AssetName = (array_key_exists('AssetName', $items)) ? $items["AssetName"] : null;
            $docs->ItemGroup = (array_key_exists('ItemGroup', $items)) ? $items["ItemGroup"] : null;
            $docs->SubGroup = (array_key_exists('SubGroup', $items)) ? $items["SubGroup"] : null;
            $docs->AvailableQty = (array_key_exists('AvailableQty', $items)) ? $items["AvailableQty"] : 0;
            $docs->OnHand = (array_key_exists('OnHand', $items)) ? $items["OnHand"] : 0;
            $docs->AvailableQtyDate = (array_key_exists('AvailableQty', $items)) ? Carbon::now() : null;
            $docs->WhsCode = array_key_exists('WhsCode', $items) ? $items["WhsCode"] : $form["WhsCode"];
            $docs->ReqQty = $items["ReqQty"];
            $docs->ReqDate = date('Y-m-d', strtotime($items["ReqDate"]));
            // $docs->ReqNotes = (array_key_exists('ReqNotes', $items)) ? preg_replace('/[^\p{L}\p{N}\s]+/u', '', $items["ReqNotes"]) : null;
            $docs->ReqNotes = (array_key_exists('ReqNotes', $items)) ? preg_replace("/[^a-zA-Z0-9 ]/", '', $items["ReqNotes"]) : null;
            $docs->OtherResvNo = (array_key_exists('OtherResvNo', $items)) ? $items["OtherResvNo"] : null;
            $docs->LineStatus = 'O';
            $docs->OIGRDocNum = (array_key_exists('OIGRDocNum', $items)) ? $items["OIGRDocNum"] : null;
            $docs->InvntItem = (array_key_exists('InvntItem', $items)) ? $items["InvntItem"] : null;
            $docs->RequestType = $request_type;
            $docs->save();
        }
        return $docs->LineEntry;
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetchDocNum(): JsonResponse
    {
        $doc_num = ResvHeader::select("DocNum")
            ->orderBY("DocNum", "DESC")
            ->first();

        $userCompany = UserCompany::where('user_id', '=', auth()->user()->id)->first();
        $department = [];
        // $department = ViewEmployee::select('Department')
        //     ->whereIn('Company', ['PT IMIP', 'PT BDT'])
        //     ->where('WorkLocation', 'LIKE', '%MOROWALI%')
        //     ->distinct()
        //     ->pluck('Department');

        $service = new ReservationDataService();

        $document = ($doc_num) ? (int) $doc_num->DocNum + 1 : $service->generateDocNum(date('Y-m-d H:i:s'));
        $token = Str::random(100);


        return response()->json([
            "DocNum" => $document,
            "token" => $token,
            'department' => $department,
            'apdNotes' => $service->getApdNotes(),
            'userCompany' => $userCompany->company->db_code,
            'dataSchema' => [
                'OrderId' => null
            ]
        ]);
    }

    /**
     * @param $docnum
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDocument($docnum)
    {
        try {
            $document = ResvHeader::where('DocNum', '=', $docnum)->first();
            if ($document) {
                return $this->success($document, 'Success');
            } else {
                return $this->error('Document not found!');
            }
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, int $id)
    {
        try {
            $schema = (config('app.db_schema') !== null) ? config('app.db_schema') : 'IMIP_ERESV';
            $temp_header = ResvHeader::where("U_DocEntry", "=", $id)->first();
            // throw new \Exception(json_encode($temp_header), 1);
            $db_name = $temp_header->Company;

            $header = ResvHeader::select(
                "resv_headers.*",
                "resv_headers.Company As CompanyName",
                DB::raw('
                    CASE
                        WHEN resv_headers."ApprovalStatus" = \'W\' THEN \'Waiting\'
                        WHEN resv_headers."ApprovalStatus" = \'P\' THEN \'Pending\'
                        WHEN resv_headers."ApprovalStatus" = \'N\' THEN \'Reject\'
                        WHEN resv_headers."ApprovalStatus" = \'Y\' THEN \'Approve\'
                        WHEN resv_headers."ApprovalStatus" = \'-\' THEN \'-\'
                    END AS "AppStatus"
                ')
            )
                ->where("resv_headers.U_DocEntry", "=", $id)
                ->first();

            $arr_division = [
                "U_Name" => $header['U_Division'],
                "U_DocEntry" => (int) $header['Division'],
            ];

            $arr_user_nik = [
                "U_UserName" => $header['U_Division'],
                "U_NIK" => $header['U_NIK'],
            ];

            $item_whs = '';
            $user_whs = UserWhs::where("user_id", "=", $request->user()->id)->get();
            foreach ($user_whs as $user_wh) {
                $item_whs .= "'$user_wh->whs_code',";
            }

            $item_whs = rtrim($item_whs, ', ');

            $data_details = ResvDetail::where("U_DocEntry", "=", $header['U_DocEntry'])
                ->select(
                    "resv_details.*",
                    DB::raw("
                        CASE
                            WHEN RequestType = 'SPB' THEN 'Y'
                            ELSE 'N'
                        END AS SPB
                    "),
                    DB::raw("
                        CASE
                            WHEN RequestType = 'NPB' THEN 'Y'
                            ELSE 'N'
                        END AS NPB
                    "),

                    DB::raw("
                        (
                        SELECT count(*)
                        FROM attachments
                        where source_id=resv_details.LineEntry
                        AND type = 'reservation'
                        ) as CountAttachment
                    "),
                    DB::raw(
                        "(
                        SELECT top 1 A.ReqDate
                        FROM resv_details AS A
                        left join resv_headers AS B ON A.U_DocEntry = B.U_DocEntry
                        WHERE B.ApprovalStatus NOT IN ('-', 'N', 'W')
                        AND A.WhsCode = resv_details.WhsCode
                        AND A.ItemCode = resv_details.ItemCode
                        ORDER BY A.U_DocEntry DESC) as LastReqDate"
                    ),
                    DB::raw(
                        "(
                        SELECT top 1 A.ReqNotes
                        FROM resv_details AS A
                        left join resv_headers AS B ON A.U_DocEntry = B.U_DocEntry
                        WHERE B.ApprovalStatus NOT IN ('-', 'N', 'W')
                        AND A.WhsCode = resv_details.WhsCode
                        AND A.ItemCode = resv_details.ItemCode
                        ORDER BY A.U_DocEntry DESC) as LastReqNote"
                    ),
                    DB::raw(
                        "(
                        SELECT top 1 B.RequesterName
                        FROM resv_details AS A
                        left join resv_headers AS B ON A.U_DocEntry = B.U_DocEntry
                        WHERE B.ApprovalStatus NOT IN ('-', 'N', 'W')
                        AND A.WhsCode = resv_details.WhsCode
                        AND A.ItemCode = resv_details.ItemCode
                        ORDER BY A.U_DocEntry DESC) as LastReqBy"
                    ),
                )
                ->get();

            // Initialize service sap s4
            $service = new SapS4Service();
            $service->login();

            $flow = $service->getFlowDetail($header->DocNum);

            $dataFlow = [];

            if ($flow) {
                if (array_key_exists('DATA', $flow) && $flow['DATA'] != 'NULL') {
                    foreach ($flow['DATA'] as $index => $item) {
                        foreach ($data_details as $detail) {
                            if ($detail->LineNum == $item["I_ERESERVASI"]) {
                                $detail->NoGIR = $item["RESERVASI"];
                                $detail->NoPR = $item["PR"];
                                $detail->NoPO = $item["PO"];
                                $detail->NoGR = $item["GR"];
                                $detail->NoGI = $item["GI"];
                                $detail->INBOUND = $item["INBOUND"];
                                $detail->REJECT = $item["REJECT"];
                                $detail->REASON = $item["REASON"];
                                $detail->SHIPMENT = $item["SHIPMENT"];
                                $detail->DATA_INBOUND = $item["DATA_INBOUND"];
                            }
                        }
                    }
                }
            }


            // dd($arr);

            $attachment = Attachment::where('type', 'reservation_header')
                ->where('source_id', $id)
                ->count();

            $dataService = new ReservationDataService();

            $userCompany = UserCompany::where('user_id', '=', auth()->user()->id)->first();

            return response()->json([
                "header" => $header,
                "rows" => $data_details,
                "division" => $arr_division,
                "user_nik" => $arr_user_nik,
                "count_attachment" => $attachment,
                'apdNotes' => $dataService->getApdNotes(),
                'userCompany' => $userCompany->company->db_code,
                "dataSchema" => [
                    "OrderId" => null
                ]
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                "error" => true,
                "message" => $exception->getMessage(),
                "trace" => $exception->getTraceAsString(),
            ], 422);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $disableInput = $this->getConfigByName('DisableInput', 'GENERAL');
        if ($disableInput == '1') {
            return $this->error('System is under maintenance due SAP Data Migration!!');
        }
        if ($this->validation($request)) {
            return response()->json([
                "errors" => true,
                "validHeader" => true,
                "message" => $this->validation($request)
            ]);
        }

        $details = collect($request->details);
        $form = $request->form;

        if ($form['Is_Urgent'] == 'Yes' && empty($form['UrgentReason'])) {
            return response()->json([
                "errors" => true,
                "message" => "Request Type Urgent Required Reason For That!"
            ]);
        }

        if (Str::contains($form['WorkLocation'], ['IMIP MOROWALI'])) {
            if ($form['Department'] == "IT - NETWORK" && empty($form['Usage'])) {
                return $this->error("Usage Field Is Required!", 422);
            }
        }

        if (Str::contains($form['WorkLocation'], ['MOROWALI'])) {
            if (empty($form['CostCenter'])) {
                $checkCostCenter = ResvCostCenter::where('division', $form['Division'])
                    ->where('work_location', $form['WorkLocation'])
                    // ->where('cc_code', $form['CostCenter'])
                    ->first();
                if ($checkCostCenter) {
                    return $this->error("Cost Center Is Required!", 422);
                }
            }
        }

        if (Str::contains($form['WorkLocation'], ['BDM MOROWALI'])) {
            if (empty($form['Usage']) && $form['ItemType'] != 'Service') {
                return $this->error('Movement type harus di isi!', 422);
            }
        }


        $doc_num = null;
        // get header
        $header = $this->getHeaderDoc($id, $request);
        if ($header) {
            if (
                $header->ApprovalStatus != '-' && $header->ApprovalStatus != 'N'
                && !$request->user()->hasAnyRole(['Superuser'])
            ) {
                return response()->json([
                    "errors" => true,
                    "approval status" => $header->ApprovalStatus,
                    "message" => "Document is still waiting for approval!"
                ], 422);
            }

            // if ($header->Replacement == 'Loss') {
            //     if (empty($header->U_ATTACH)) {
            //         return $this->error("Document must have attachment!", '422');
            //     }
            // }
        }

        $validationService = new ReservationValidateDataService();
        $validationService->validateDetails($details, $form, $request);

        DB::beginTransaction();
        try {

            // set created at
            $created = (!empty($header)) ? $header->created_at : Carbon::now();
            $doc_entry = $this->processHeaderDoc($header, $created, $request);
            //return response()->json($doc_entry);
            if ($doc_entry) {
                foreach ($details as $index => $items) {
                    $line = ($index + 1);
                    // Saved the data
                    $line_entry = $this->saveData(
                        $line,
                        $items,
                        $request,
                        $form,
                        $doc_entry
                    );
                    $docs = DB::connection('sqlsrv')
                        ->table('resv_details')
                        ->where('LineEntry', '=', ((array_key_exists('LineEntry', $items) ? $items['LineEntry'] : null)))
                        ->first();
                    // return response()->json($docs['U_ATTACH'], 422);

                    if ($header->ItemType == 'Non Ready Stock') {
                        if ($docs) {
                            if (empty($docs->U_ATTACH)) {
                                if ($items['SPB'] == 'Y') {
                                    if (!Str::contains($items['ItemGroup'], ['ZTRD', 'ZTRS', 'ZTRU'])) {
                                        return $this->error("Line " . ($index + 1) . ": Document must have attachment!", '422', [$docs]);
                                    }
                                }
                            }
                        }
                    }


                    if ($header->ItemType == 'Ready Stock' && str($header->WorkLocation)->contains(['BDM MOROWALI'])) {
                        if ($docs) {
                            if (empty($docs->U_ATTACH)) {
                                if ($items['SPB'] == 'Y') {
                                    if (!Str::contains($items['ItemGroup'], ['ZTRD', 'ZTRS', 'ZTRU'])) {
                                        return $this->error("Line " . ($index + 1) . ": Document must have attachment!", '422', [$docs]);
                                    }
                                }
                            }
                        }
                    }

                    if ($header->DocumentType == 'Service') {
                        if ($docs) {
                            if (empty($docs->U_ATTACH)) {
                                return $this->error("Line " . ($index + 1) . ": Document must have attachment!!", '422');
                            }
                        }
                    }

                    if ($header->RequestType == 'Restock') {
                        if ($docs) {
                            if (empty($docs->U_ATTACH)) {
                                return $this->error("Line " . ($index + 1) . ": Document must have attachment!!!", '422');
                            }
                        }
                    }

                    if ($docs) {
                        // Initialize service sap s4
                        // $service = new SapS4Service();
                        // $service->login();


                        if ($docs->ItemGroup == 'ZSER' || $docs->ItemGroup == 'ZAST') {
                            // return response()->json(isset($docs->U_ATTACH']), 422);
                            if (empty($docs->U_ATTACH)) {
                                return $this->error("Line " . ($index + 1) . ": Document must have attachment!!!!", '422');
                            }
                        }
                    }
                } // Details
                $is_approval = $request->approval;
                //return response()->json($is_approval);
                DB::commit();
                if ($is_approval) {
                    if (empty($request->details)) {
                        return $this->error("Details is empty!", 422);
                    }

                    $header = ResvHeader::where('U_DocEntry', '=', $doc_entry)->first();

                    foreach ($details as $value) {
                        if (empty($value['ItemCode']) && $header->DocumentType == 'Item') {
                            return $this->error('Item Code Cannot Empty', 422);
                        }
                    }

                    $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');
                    // if ($checkUseNewApproval != '1') {
                    //     $checkApproval = ViewApprovalStage::where('DocumentReferenceID', $header->DocNum)->first();
                    //     if ($checkApproval) {
                    //         $header->ApprovalStatus = 'W';
                    //         $header->save();
                    //         // return $this->success([
                    //         //     "U_DocEntry" => $doc_entry
                    //         // ], ($doc_entry != 'null') ? "Data updated!" : "Data inserted!");
                    //         $this->error("Document already submitted to cherry", 422);
                    //     }
                    // }

                    //app/traits/cherryapproval.php
                    $approvalService = new ApprovalCherryService();
                    return $this->submitApproval($header, $details, $request);
                    // return $approvalService->submitApproval($header, $details, $request);
                } else {
                    return $this->success([
                        "U_DocEntry" => $doc_entry
                    ], ($doc_entry != 'null') ? "Data updated!" : "Data inserted!");
                }
            } else {
                $this->error("Failed process header!", '422');
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            // Log::info('error reservasi', [
            //     'trace' => $exception->getTraceasString()
            // ]);
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace(),
                // 'trace' => $exception->getTrace()
                // 'trace' => ''
            ]);
        }
    }


    public function submitApprovalReservation(Request $request)
    {
        try {
            $header = ResvHeader::where("DocNum", "=", $request->DocNum)->first();
            $details = ResvDetail::where("U_DocEntry", $header->U_DocEntry)->get()->toArray();
            $approvalService = new ApprovalCherryService();
            return $this->submitApproval($header, $details, $request);
            // return $approvalService->submitApproval($header, $details, $request);
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), '422', [
                'trace' => $e->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param int $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            if ($id != '1') {
                DB::connection('sqlsrv')
                    ->table('resv_details')
                    ->where('LineEntry', $id)
                    ->delete();
            } else {
                DB::connection('sqlsrv')
                    ->table('resv_details')
                    ->whereIn('LineEntry', $request->doc_entry)
                    ->delete();
            }

            // Log::info("Reservation details id: " . $id);

            return $this->success('Rows Deleted!');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), 422);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function maxDocResv(Request $request): JsonResponse
    {
        $max_num = ResvHeader::selectRaw('ISNULL(MAX("U_DocEntry"), 1) as "DocEntry"')->first();
        $token = Str::random(100);
        return response()->json([
            'max_num' => $max_num,
            'token' => $token,
        ]);
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteAll($id): JsonResponse
    {
        $header = ResvHeader::where("U_DocEntry", "=", $id)->first();
        if ($header->ApprovalStatus == '-') {
            if (ResvDetail::where("U_DocEntry", "=", $id)->first()) {
                ResvDetail::where("U_DocEntry", "=", $id)->delete();
                return response()->json([
                    'message' => 'Records deleted!'
                ]);
            } else {
                return response()->json([
                    'message' => 'Row not found'
                ]);
            }
        } else {
            return response()->json([
                'message' => 'Cannot delete row'
            ]);
        }
    }
}
