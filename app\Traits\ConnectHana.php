<?php

namespace App\Traits;

trait ConnectHana
{
    /**
     * @return bool|false|resource|string
     */
    public function connectHana()
    {
        try {
            $conn = odbc_connect(
                'hanab1imipresv',
                'IMIP_ERESV',
                'Ereserve#1234',
                SQL_CUR_USE_ODBC
            );
            if (!$conn) {
                return false;
            } else {
                return $conn;
            }
        } catch (\Exception $exception) {
            return $exception->getMessage() . ' : ' . $exception->getFile();
        }
    }

    public function sapDb()
    {
        return config('app.db_sap');
    }

    public function schemaDb()
    {
        return config('app.db_schema');
    }
}
