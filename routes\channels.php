<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// Add the broadcasting routes with auth_service middleware
Broadcast::routes(['middleware' => 'auth_service']);

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->username === (int) $id;
});

Broadcast::channel('BatchProgress.{id}', function ($user, $id) {
    return (int) $user->username === (int) $id;
});

Broadcast::channel('Document.{id}', function ($user, $id) {
    return (int) $user->username === (int) $id;
});

Broadcast::channel('user.{id}', function ($user, $id) {
    return (int) $user->username === (int) $id;
});
