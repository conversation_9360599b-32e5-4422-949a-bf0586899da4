<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Services\ApprovalPrivyService;
use Carbon\Carbon;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class PrivyEnterpriseController extends Controller
{
    public function getUserEnterprise(Request $request)
    {
        $form = json_decode($request->form);
        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('ListUserEnterprise', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');
        $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');
        $params = $this->paramUserEnterprise($form->enterprise_id);
        $signature = $this->signature($timestamp, 'GET', $params, $request);

        try {
            $response = Http::withoutVerifying()
                ->timeout(90)
                ->retry(3, 100)
                ->withOptions(["verify" => false])
                ->withToken($token)
                ->withHeaders([
                    'Timestamp' => $timestamp,
                    'Signature' => $signature,
                    'Request-ID' => strtotime($timestamp),
                ])
                ->get($url, $params)
                ->throw(function ($response, $e) {
                    throw new \Exception('Get User Enterprise: ' . json_decode($response->collect()) 
                        . 'signature request: ' . $this->signature($timestamp, 'GET', $params, $request), 1);
                });


            return $this->success($response->json());
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 422, [
                'params' => $params,
                'signature' => $signature,
                // 'trace' => $e->getTraceasString(),
            ]);
        }

    }

    public function paramUserEnterprise($enterprise_id)
    {
        return [
            "enterprise_id" => $enterprise_id,
        ];
    }

    protected function signature($timestamp, $httpVerb, $params, $request)
    {
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $secret_key = $this->getConfigByName('PrivySecretKey', 'PRIVY');
        $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $body = $params;

        $strBody = json_encode($body);
        $strBody = str_replace(" ", "", $strBody);
        $method = $httpVerb;
        $body_md5 = base64_encode(md5($strBody, true));
        $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        $byte_key = utf8_encode($api_secret);
        $new_hmac = utf8_encode($hmac_signature);
        $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        $base64_message = base64_encode($hmac);
        $auth_string = $api_key . ":" . $base64_message;
        $signature = base64_encode($auth_string);

        // $timestamp = Carbon::now()->toString();
        // $method = $httpVerb;

        // Mengambil parameter URL sebagai array asosiatif
        // $query = request()->query();
        // $queryJson = json_encode($query);
        // $body_md5 = base64_encode(md5($queryJson, true));

        // $hmac_signature = "{$timestamp}:{$api_key}:{$method}:{$body_md5}";

        // // Generate HMAC-SHA256 signature
        // $hmac = hash_hmac('sha256', $hmac_signature, $secret_key, true);
        // $hmac_base64 = base64_encode($hmac);

        // $auth_string = "{$api_key}:{$hmac_base64}";
        // $signature = base64_encode($auth_string);
        return $signature;
    }

    protected function signatureEnterprise($timestamp, $httpVerb, $params)
    {
        // $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        // $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        // $body = $params;

        // $strBody = json_encode($body);
        // $strBody = preg_replace("/\s/", "", $strBody);
        // $method = $httpVerb;
        // $body_md5 = base64_encode(md5($strBody, true));
        // $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        // $byte_key = utf8_encode($api_secret);
        // $new_hmac = utf8_encode($hmac_signature);
        // $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        // // $hmac = hash_hmac('sha256', $hmac_signature, $api_secret, true);
        // $base64_message = base64_encode($hmac);
        // $auth_string = $api_key . ":" . $base64_message;
        // $signature = base64_encode($auth_string);

        // Get the API key and secret key (replace 'your_username' and 'your_password' with your actual API credentials)
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $secret_key = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        // Parse the request body as JSON
        $body = $params; // Replace this with the actual raw request body
        $strBody = json_encode($body);
        $obj_bd = '{}';
        // throw new \Exception(json_encode($obj_bd), 1);

        // Prepare the data for the HMAC signature
        $method = $httpVerb;
        // $json_bd = json_encode($obj_bd, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
        // $json_bd = preg_replace('/\s/', '', $json_bd);

        // Calculate the MD5 hash of the JSON body
        $body_md5 = base64_encode(md5($obj_bd, true));

        // Create the HMAC signature
        $hmac_signature = "{$timestamp}:{$api_key}:{$method}:{$body_md5}";
        $hmac = hash_hmac('sha256', $hmac_signature, $secret_key, true);
        $hmac_base64 = base64_encode($hmac);

        // Create the authentication string
        $auth_string = "{$api_key}:{$hmac_base64}";

        // Encode the signature using base64
        $signature = base64_encode($auth_string);
        return $signature;
    }

    public function getEnterprise(Request $request)
    {
        $service = new ApprovalPrivyService();
        $service->login();

        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('ListEnterprise', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');
        // $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');
        $timestamp = Carbon::now()->toString();
        $params = $this->paramEnterprise();
        $signature = $this->signatureEnterprise($timestamp, 'GET', $params);

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $signature,
            ])
            ->get($url, $params)
            ->throw(function (Response $response, RequestException $e) {
                throw new \Exception('Get Enterprise: ' . json_decode($response->collect()), 1);
            });

        // ->throw(function ($response, $e) {
        //     throw new \Exception('Get Enterprise: ' . json_decode($response->collect()), 1);
        // });
        if ($response->failed()) {
            throw new \Exception('test 1 '.json_encode($response->status()), 1);
        }
        return $this->success($response->json());
    }

    protected function submitRequestEnterprise($token, $timestamp, $signature, $url, $params)
    {
        return Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $signature,
            ])
            ->get($url, $params)
            ->throw(function (Response $response, RequestException $e) {
                throw new \Exception('Get Enterprise: ' . json_decode($response->collect()), 1);
            });
    }

    public function paramEnterprise()
    {
        return [];
    }
}
