<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\RegisterPrivy
 *
 * @property int $id
 * @property string $reference_number
 * @property string $channel_id
 * @property string|null $info
 * @property string $email
 * @property string $phone
 * @property string|null $nik
 * @property string|null $name
 * @property string|null $dob
 * @property string|null $selfie
 * @property string|null $identity
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $register_token
 * @property string|null $status
 * @property string|null $privy_id
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy query()
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereChannelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereDob($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereIdentity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereNik($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy wherePrivyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereReferenceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereRegisterToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereSelfie($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|RegisterPrivy whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class RegisterPrivy extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $connection = 'sqlsrv';
}
