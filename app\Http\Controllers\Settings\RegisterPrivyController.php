<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Models\Settings\RegisterPrivy;
use App\Models\View\ViewEmployee;
use App\Services\RegisterPrivyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class RegisterPrivyController extends Controller
{
    public $service;

    public function __construct(RegisterPrivyService $service)
    {
        $this->service = $service;
    }

    /**
     * Retrieves data from the database based on the given options and returns a JSON response.
     *
     * @param Request $request The HTTP request object.
     * @throws \Exception If an error occurs while retrieving the data.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the retrieved data.
     */
    public function index(Request $request)
    {
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : 'reference_number';
        $order = (!empty($options->sortDesc)) ? (($options->sortDesc[0]) ? "desc" : "desc") : 'desc';
        $offset = $pages;

        $query = RegisterPrivy::orderBY($sorts, $order);

        $total = $query->count();

        $data = $query->offset($offset)
            ->limit($row_data)
            ->get();

        return $this->success([
            'rows' => $data,
            'total' => $total,
            'form' => $this->service->getForm(),
            'employee' => ViewEmployee::where('Company', 'PT IMIP')
                ->whereNotIn('Department', ['CATERING', 'SECURITY', 'SHIPPING', 'WISMA'])
                ->select('*', DB::raw('CAST(BirthDate AS DATE) as BirthDates'))
                ->get()
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'channel_id' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors());
        }

        DB::beginTransaction();
        try {
            $data = RegisterPrivy::create($this->service->formData($request, 'store'));

            $response = $this->service->register($data);

            DB::commit();
            return $this->success([
                'data' => $data,
                'response' => $response
            ], 'Document saved!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * Show the specified resource.
     *
     * @param int $id The ID of the resource.
     * @return mixed The specified resource.
     */
    public function show($id)
    {
        return $this->success([
            'data' => RegisterPrivy::find($id)
        ], '');
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'channel_id' => 'required',
            'email' => 'required',
            'phone' => 'required',
        ]);


        if ($validator->fails()) {
            return $this->error($validator->errors());
        }

        DB::beginTransaction();
        try {
            $document = RegisterPrivy::where('id', $id)->update($this->service->formData($request, 'update'));

            DB::commit();
            return $this->success([
                'document' => $document
            ], 'Document saved!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }


    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            RegisterPrivy::where('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
