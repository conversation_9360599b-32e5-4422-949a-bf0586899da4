<?php

namespace App\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Elastic\Elasticsearch\ClientBuilder;

class ElasticsearchServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('elasticsearch', function ($app) {
            $apiKey = config('services.elasticsearch.key');

            if (!$apiKey) {
                throw new \RuntimeException('ELASTIC_API_KEY environment variable is not set.');
            }

            return ClientBuilder::create()
                ->setHosts([config('services.elasticsearch.host')])
                ->setApiKey($apiKey)
                ->build();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
