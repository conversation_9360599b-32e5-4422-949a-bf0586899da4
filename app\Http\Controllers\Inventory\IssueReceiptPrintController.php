<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Jobs\RemoveAttachment;
use App\Models\Inventory\Inventory;
use App\Services\ConvertDocxToPdfService;
use App\Services\QrCodeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\TemplateProcessor;
use Vinkla\Hashids\Facades\Hashids;

class IssueReceiptPrintController extends Controller
{
    public function store(Request $request)
    {
        $id = $request->form['id'];
        $oldLimit = ini_get('memory_limit');
        ini_set('memory_limit', '2048M');
        try {
            $header = Inventory::where("id", "=", $id)
                ->with(['lineItems', 'lineItems.resvDetail.header', 'createdUser'])
                ->first();

            if ($header->doc_type == 'out') {
                $letter_template = new TemplateProcessor(
                    public_path(
                        'template/ISSUE-HS.docx'
                    )
                );
            } else {
                $letter_template = new TemplateProcessor(
                    public_path(
                        'template/NPB.docx'
                    )
                );
            }

            foreach ($header->lineItems as $index => $item) {
                $data_letter[] = [
                    "NO" => ($index + 1),
                    "ITEMCODE" => $item->item_code,
                    "RESVNO" => $item->resv_number,
                    "ITEMNAME" => str_replace('&', '&amp;', $item->item_name),
                    "UOM" => $item->uom,
                    "QTY" => number_format($item->qty, 0),
                    "DATE" => $item->ReqDate,
                    "WHS" => $item->whs_code,
                    "NOTES" => str_replace('&', '&amp;', $item->ReqNotes),
                    // "REQUESTER" => $item->resvDetail->header->RequesterName
                    "REQUESTER" => $item->resvDetail->EmployeeName
                ];
            }

            $letter_template->cloneRowAndSetValues('NO', $data_letter);

            $letter_template->setValue('RECEIVED', '');
            $letter_template->setValue('ISSUE', '');
            $letter_template->setValue('CHECK', '');
            $letter_template->setValue('DOCNUM', $header->doc_num);
            $letter_template->setValue('DOCDATE', date('Y-m-d', strtotime($header->post_date)));
            $letter_template->setValue('PREPAREBY', $header->created_name);
            $letter_template->setValue('REMARKS', $header->notes);
            $letter_template->setValue('DATETIME', 'Print Date: ' . date('Y-m-d H:i:s'));

            $username = $request->user()->username;
            $file_path_name = public_path() . '/Attachment/GI/' . $username . '/';

            $qrCodeService = new QrCodeService();
            $file_export_name = Hashids::encode($header->id);

            $qr_file = "https://eportal.imip.co.id/eresv" . '/verification?str=' . $file_export_name . '&type=in';

            $qrCodeService->generatePath($file_path_name);
            // generate QR Code
            $qrCodeService->generateQrCodeDoc($qr_file, $file_path_name . $header->doc_num . '.png', size: 270);

            $qrCodePath = $file_path_name . $header->doc_num . '.png';
            $letter_template->setImageValue(
                'QRCODE',
                $qrCodePath
            );
            $file_str = Str::upper(Str::slug($header->doc_num));
            $file_name = $file_path_name . $file_str . '.docx';

            $letter_template->saveAs($file_name);


            $pdf_file = $file_path_name . $file_str . ".pdf";

            $serviceConvert = new ConvertDocxToPdfService();
            $pathToSavingDirectory = $file_path_name;
            $pdfFileName = $file_str . ".pdf";
            $serviceConvert->convert($file_name, $pathToSavingDirectory, $pdfFileName);
            $pdf_file = $file_path_name . $file_str . ".pdf";

            if ((filesize($pdf_file) / 1024) < 2) {
                throw new \Exception('Failed generate pdf file, please check pdf engine', 1);
            }
            $all_files = [
                $file_name,
                $pdf_file,
                $qrCodePath,
            ];

            // Remove Attachment
            ini_set('memory_limit', $oldLimit);
            RemoveAttachment::dispatch($all_files)->delay(now()->addMinutes(15));

            $base64 = chunk_split(
                base64_encode(
                    file_get_contents(
                        $pdf_file
                    )
                )
            );

            return $this->success([
                'base64' => $base64
            ]);
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }
}
