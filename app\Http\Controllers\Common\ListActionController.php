<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ListActionController extends Controller
{
    public function index(Request $request)
    {
        $items = [
            [
                "header" => 'General',
                "items" => [
                    [
                        "name" => 'Users',
                        "route" => '/master/users',
                        "desc" => 'Display list of users',
                    ],
                    [
                        "name" => 'Permissions',
                        "route" => '/master/permission',
                        "desc" => 'Display list of permissions',
                    ],
                    [
                        "name" => 'Roles',
                        "route" => '/master/roles',
                        "desc" => 'Display list of roles',
                    ],
                    [
                        "name" => 'Document Sub Type',
                        "route" => '/master/sub-type',
                        "desc" => 'Display sub type',
                    ],
                    [
                        "name" => 'User Enterprise',
                        "route" => '/master/privy-enterprise',
                        "desc" => 'Display privy enterprise',
                    ],
                    [
                        "name" => 'List Inventory Usage',
                        "route" => '/master/usage',
                        "desc" => 'Display data usage',
                    ],
                    [
                        "name" => 'Cost Center',
                        "route" => '/master/cost-center',
                        "desc" => 'Display cost center list and management',
                    ],
                    [
                        "name" => 'Approval Mapping User',
                        "route" => '/master/mapping-approval',
                        "desc" => 'Approval Mapping User',
                    ],
                    [
                        "name" => 'Release Notes',
                        "route" => '/list/release-note',
                        "desc" => 'App Release Notes',
                    ],
                    [
                        "name" => 'Approval callback',
                        "route" => '/list/approval-callback',
                        "desc" => 'Approval Callback Action',
                    ],
                    [
                        "name" => 'Re Send Email PR',
                        "route" => '/list/resend-email',
                        "desc" => 'Re Send Email PR Action',
                    ],
                ],
            ],

        ];

        $safety = [
            [
                "header" => 'Safety',
                "items" => [
                    [
                        "name" => 'Data Safety Old',
                        "route" => '/list/data-safety',
                        "desc" => 'Data Safety that already input',
                    ],
                    [
                        "name" => 'Department Mapping',
                        "route" => '/list/department-mapping',
                        "desc" => 'Department Mapping For Goods Issue',
                    ],
                    [
                        "name" => 'APD Notes',
                        "route" => '/list/apd-notes',
                        "desc" => 'Notes for request APD',
                    ],
                    [
                        "name" => 'Mapping Item Safety',
                        "route" => '/list/mapping-item-safety',
                        "desc" => 'Mapping item safety',
                    ],
                ],
            ],
        ];

        $itemSalesUser = [
            [
                "header" => 'Carpool',
                "items" => [
                    [
                        "name" => 'Data Vehicle',
                        "route" => '/list/vehicle',
                        "desc" => 'Data Vehicle for request reservation',
                    ],
                ],
            ],
        ];

        if ($request->user()->hasRole('Superuser')) {
            return $this->success(array_merge($items, $safety, $itemSalesUser));
        } elseif (str($request->user()->department)->contains(['OCCUPATIONAL HEALTH & SAFETY'])) {
            return $this->success($safety);
        } elseif ($request->user()->is_sales_user == 'Y') {
            return $this->success($itemSalesUser);
        }
    }
}
