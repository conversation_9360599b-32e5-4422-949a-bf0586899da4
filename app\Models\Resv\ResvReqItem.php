<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


/**
 * App\Models\Resv\ResvReqItem
 *
 * @property int $U_DocEntry
 * @property string|null $U_Description
 * @property string|null $U_UoM
 * @property string|null $U_Status
 * @property string|null $U_Remarks
 * @property string|null $U_Supporting
 * @property string|null $U_CreatedBy
 * @property string|null $U_Comments
 * @property string|null $U_ItemType
 * @property string|null $U_CreatedAt
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUComments($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUDocEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUItemType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereURemarks($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUSupporting($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUUoM($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUpdatedAt($value)
 * @property int $DocNum
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereDocNum($value)
 * @mixin \Eloquent
 */
class ResvReqItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $connection = 'sqlsrv';
    protected $primaryKey = 'U_DocEntry';
}
