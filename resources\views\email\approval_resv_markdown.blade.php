<x-mail::message>
# Approval Request

<x-mail::table>
@if ($newApproval)
| Field | Value |
|:------|:------|
| **Doc Number** | {{ $header->DocNum }} |
| **Department** | {{ $header->Department }} |
| **Item Request Type** | {{ $details[0]['RequestType'] }} |
@endif
| **Request Type** | {{ $header->RequestType }} |
| **Requester** | {{ $header->RequesterName }} ({{ $header->Requester }}) |
| **Item Type** | {{ $header->ItemType }} |
| **Warehouse** | {{ $header->WhsCode }} |
@if ($header->WorkLocation == 'IMIP MOROWALI')
@if ($header->Department == "IT - NETWORK")
| **Movement Type** | {{ $movementType->movement_type }} - {{ $movementType->description }} |
@endif
@if ($header->CategoryType == "APD")
| **Replacement** | {{ $header->Replacement }} |
@endif
@endif
| **Notes** | {{ $header->Memo }} |
</x-mail::table>

## Item Details

<x-mail::table>
| # | @if($header->DocumentType != 'Service')Item Code | @endif Item Name | @if ($header->ItemType == "Asset")Asset Code | Asset Name | @endif @if ($countOrder > 0)Order | @endif Required Date | UoM | Req Qty | Req Date | Notes | @if ($header->CategoryType == "APD")Employee Name | Employee Id@endif |
|:--|@if($header->DocumentType != 'Service'):--|@endif:--|@if ($header->ItemType == "Asset"):--|:--|@endif@if ($countOrder > 0):--|@endif:--|:--|--:|:--|:--|@if ($header->CategoryType == "APD"):--|:--@endif|
@foreach ($details as $key => $value)
| {{ $key + 1 }} | @if($header->DocumentType != 'Service')@if($header->ItemType == 'Non Ready Stock')[{{ $value["ItemCode"] }}]({{ $value["U_ATTACH"] }})@else{{ $value["ItemCode"] }}@endif | @endif @if($header->DocumentType == 'Service')[{{ $value["ItemName"] }}]({{ $value["U_ATTACH"] }})@else{{ $value["ItemName"] }}@endif | @if ($header->ItemType == "Asset"){{ $value["AssetCode"] }} | {{ $value["AssetName"] }} | @endif @if (isset($value['OrderId'])){{ $value['OrderId'] }} | @endif {{ $header->RequiredDate }} | {{ $value["UoMCode"] }} | {{ floatval($value["ReqQty"]) }} | {{ $value["ReqDate"] }} | {{ (array_key_exists('ReqNotes', $value)) ? $value["ReqNotes"] : '' }} | @if ($header->CategoryType == "APD"){{ (array_key_exists('EmployeeName', $value)) ? $value["EmployeeName"] : '' }} | {{ (array_key_exists('EmployeeId', $value)) ? $value["EmployeeId"] : '' }}@endif |
@endforeach
</x-mail::table>

Thanks,<br>
{{ config('app.name') }}
</x-mail::message>


