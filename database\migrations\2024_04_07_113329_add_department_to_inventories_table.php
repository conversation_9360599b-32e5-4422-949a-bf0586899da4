<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventories', function (Blueprint $table) {
            $table->string('department', 150)->nullable();
            $table->string('prefix');
            $table->string('suffix');

            $table->index(['department', 'prefix', 'suffix']);
        });

        Schema::create('inventory_dept_maps', function (Blueprint $table) {
            $table->id('id');
            $table->string('department', 150)->nullable();
            $table->string('prefix', 20)->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('inventory_dept_maps');
        Schema::table('inventories', function (Blueprint $table) {
            //
        });
    }
};
