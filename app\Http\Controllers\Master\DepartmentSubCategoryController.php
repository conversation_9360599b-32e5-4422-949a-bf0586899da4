<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Master\MasterSubCategory;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DepartmentSubCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $category = $request->formData;
        $brands = MasterSubCategory::select('id', 'title')
            ->where('category_name', '=', $category)
            ->get();

        $simple_data = MasterSubCategory::select('id', 'title')
            ->where('category_name', '=', $category)
            ->pluck('title');

        if (count($brands) < 1) {
            $brands = [
                [
                    'id' => null,
                    'title' => null,
                ]
            ];
        }

        if (count($simple_data) < 1) {
            $simple_data = [''];
        }

        $all_data = MasterSubCategory::select('id', 'title', 'category_name')
            ->orderBy('category_name')
            ->get();

        return $this->success([
            'rows' => $brands,
            'header' => ['Sub Category Id', 'Sub Category'],
            'simple' => $simple_data,
            'all' => $all_data
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        // $category = $request->formData;
        DB::beginTransaction();
        try {
            foreach ($details as $detail) {
                if (empty($detail['title'])) {
                    return $this->error('Title cannot empty', '422');
                }
                $brand = MasterSubCategory::where('id', '=', $detail['id'])->first();
                if ($brand) {
                    $brand->title = Str::ucfirst($detail['title']);
                    $brand->category_name = $detail['category_name'];
                    $brand->updated_at = Carbon::now();
                } else {
                    $brand = new MasterSubCategory();
                    $brand->category_id = 0;
                    $brand->title = Str::ucfirst($detail['title']);
                    $brand->category_name = $detail['category_name'];
                    $brand->created_at = Carbon::now();
                }
                $brand->save();
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = MasterSubCategory::where('category_name', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param MasterSubCategory $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, MasterSubCategory $productBrand)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            MasterSubCategory::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
