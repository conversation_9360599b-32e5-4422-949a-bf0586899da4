<?php

namespace App\Console\Commands;

use App\Jobs\ProcessPendingDocumentQueue;
use App\Models\Approval\ApprovalStage;
use App\Models\Document\Document;
use App\Models\View\ViewEmployee;
use App\Services\ApprovalActionService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessPendingDocument extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'document:pending {company}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending document that already approved';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $service = new ApprovalActionService();

        $approver = ($this->argument('company') == 'PT MMM') ? '88104590' : 'ADWIN';

        $stages = ApprovalStage::whereNotNull('document_id')
            ->leftJoin('users', 'users.id', 'approval_stages.user_id')
            ->leftJoin('documents', 'documents.id', 'approval_stages.document_id')
            ->select('approval_stages.document_id')
            ->where('approval_stages.status', 'approved')
            ->where('users.username', '=',  $approver)
            ->where('documents.status', '=',  'pending')
            ->where('documents.company', '=',  $this->argument('company'))
            ->distinct()
            ->pluck('document_id');

        $rows = Document::select(
            'documents.*',
            'document_number as paper_no',
            'document_sub_types.name as document_sub_type_name',
            'users.name as user_name'
        )
            ->leftJoin('customers', 'customers.id', 'documents.customer_id')
            ->leftJoin('users', 'users.id', 'documents.created_by')
            ->leftJoin('document_sub_types', 'document_sub_types.id', 'documents.document_sub_type_id')
            ->with(['attachment', 'userCreate', 'approver', 'approver.user'])
            ->whereIn('documents.id', $stages)
            ->orderBy('documents.document_number', 'desc')
            ->get();
        // DB::beginTransaction();

        Log::info('count pending approved document' . count($rows));
        
        dispatch(new ProcessPendingDocumentQueue($rows));
    }
}
