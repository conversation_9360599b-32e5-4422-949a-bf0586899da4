<?php

namespace App\Http\Controllers\Approval;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessApprovalDocumentAction;
use App\Models\Document\Document;
use App\Models\User;
use App\Models\View\ViewEmployee;
use App\Notifications\ApprovalMeteraiApprove;
use App\Services\ApprovalActionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ApprovalActionController extends Controller
{
    /**
     * Process approval of multiple documents and send notifications to requesters.
     *
     * @param Request $request The HTTP request data.
     * @throws \Exception If there's an error processing the documents.
     * @return JsonResponse Returns a JSON response indicating that the document was approved.
     */
    public function batchApprove(Request $request): JsonResponse
    {
        $rows = json_decode(json_encode($request->rows), false);
        $formApproval = json_decode(json_encode($request->formApproval), false);

        $notes = ($formApproval->notes) ?: '';
        $userId = $request->user()->id;
        $service = new ApprovalActionService();

        $documentId = [];
        foreach ($rows as $row) {
            $documentId[] = ($request->approvalType) ? $row->document_id : $row->id;
        }

        $service->updateApprovalStageMultiple($documentId, $notes, $userId, 'approved');

        ProcessApprovalDocumentAction::dispatch($rows, $formApproval, $request->approvalType, $userId)
            ->onQueue('processDocument');

        return $this->success([
            'message' => 'Document approved successfully!'
        ]);
    }

    public function digisignBulkApprove(Request $request)
    {
        $rows = json_decode(json_encode($request->rows), false);
        $doc_id = [];
        foreach ($rows as $key => $value) {
            if (date('Y-m-d', strtotime($value->created_at)) < '2022-06-27') {
                $doc_id[] = Str::slug(strtoupper($value->document_number));
            } else {
                $doc_id[] = Str::slug(strtoupper($value->external_document_number));
            }

            $token = $this->getConfigByName('TokenEsign', 'ESIGN', $value->company);
            $url = $this->getConfigByName('BulkSign', 'ESIGN');
            $esignUserId = $this->getConfigByName('UserIdEsign', 'ESIGN', $value->company);
        }

        $str = Str::random(20);

        $options = [
            'jsonfield' => json_encode(
                [
                    'JSONFile' => [
                        'userid' => $esignUserId,
                        'email_user' => $esignUserId,
                        'document_id' => $doc_id,
                        // 'document_id' => ['0363bdminvv23', '0362bdminvv23'],
                        'must_read' => false
                    ],
                ]
            ),
        ];

        // $headers[] = "Authorization: Bearer $token; Content-Type: multipart/form-data; boundary=" . $str;
        $headers[] = "Authorization: Bearer $token";
        // $headers[] = "Authorization: Bearer $token;";
        // throw new \Exception($url, 1);
        // throw new \Exception(json_encode($options), 1);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl, CURLOPT_VERBOSE, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_TIMEOUT, 95);
        // timeout in seconds
        curl_setopt($curl, CURLOPT_POSTFIELDS, $options);

        $MAX_RETRIES = 5;
        for ($i = 0; $i < $MAX_RETRIES; $i++) {

            // Send the request and save response to $response
            $response = curl_exec($curl);

            // Stop if fails
            if (!$response) {
                Log::info('result from approve document failed', [
                    'result' => curl_error($curl) . '" - Code: ' . curl_errno($curl),
                    'response' => $response
                ]);
                die('Error: "' . curl_error($curl) . '" - Code: ' . curl_errno($curl));
            }

            $status_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            // If it's a successful request (200 or 404 status code):
            if (in_array($status_code, array(200, 404))) {
                // $response = curl_exec($curl);
                curl_close($curl);
                // dd($options);
                // dd(config('app.sign_url_download'));
                $response_text = json_decode($response);
                // return $this->error('', 422, $response);
                // dd($response);
                $collect_response = collect($response_text);

                // throw new \Exception(json_encode($collect_response), 1);
                if ($collect_response['JSONFile']->result != '00') {
                    return $this->success([
                        'message' => $collect_response['JSONFile']->notif,
                        'error' => true
                    ]);
                }
                return $this->success([
                    'message' => $collect_response['JSONFile']->link,
                    'error' => false
                ]);
                // break;
            }
        }
    }

    /**
     * Updates the approval stage status of multiple documents to 'rejected' and also updates
     * the status of each document to 'rejected'.
     *
     * @param Request $request The HTTP request object containing rows, formApproval and other data
     * @throws \Exception If any error occurs while processing the request
     * @return JsonResponse A JSON response indicating the success status of the operation
     */
    public function batchReject(Request $request): JsonResponse
    {
        $service = new ApprovalActionService();

        $rows = json_decode(json_encode($request->rows), false);
        $formApproval = json_decode(json_encode($request->formApproval), false);

        DB::beginTransaction();
        try {
            foreach ($rows as $row) {
                $document_id = $row->document_id;
                $notes = $formApproval->notes;

                $service->updateApprovalStage($document_id, $notes, $request->user()->id, 'rejected');
            }
            DB::commit();
            return $this->success('document rejected');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), 422, [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * batchCancel method cancels one or more documents in the database,
     * and saves information about the cancellation in the ApprovalStage table.
     *
     * @param Request $request The HTTP request object containing the rows and formApproval data.
     * @throws \Exception When there is an error cancelling the document.
     * @return string Returns a success message if the cancellation is successful.
     */
    public function batchCancel(Request $request)
    {
        $service = new ApprovalActionService();

        $rows = json_decode(json_encode($request->rows), false);
        $formApproval = json_decode(json_encode($request->formApproval), false);

        DB::beginTransaction();
        try {
            foreach ($rows as $row) {
                $document_id = $row['id'];

                $service->updateApprovalStage($document_id, '', $request->user()->id, 'canceled');
            }
            DB::commit();
            return $this->success('document canceled');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), 422, [
                'trace' => $exception->getTrace()
            ]);
        }
    }
}
