<?php

namespace App\Services\Reports;

use App\Models\Resv\ResvHeader;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class OpenReservationService
{
    public function show(
        $request,
        $date_from,
        $date_to,
        $item_type,
        $request_type,
        $created_by,
        $db_name,
    ) {
        $rows = ResvHeader::select(
            "resv_headers.DocNum",
            "resv_headers.DocDate",
            "resv_headers.WhsCode",
            DB::raw('resv_headers."RequestType" as "HeaderRequestType"'),
            "resv_headers.ItemType",
            "resv_details.ItemCode",
            "resv_details.ItemName",
            "resv_details.AssetCode",
            "resv_details.AssetName",
            "resv_details.UoMCode",
            "resv_details.ReqQty",
            "resv_details.ReqNotes",
            "resv_details.RequestType",
            "resv_details.LineNum",
            "resv_headers.RequesterName",
            "resv_headers.WorkLocation",
            "resv_headers.DocStatus",
            DB::raw('
                     CASE
                        WHEN resv_headers."DocStatus" = \'D\' THEN \'Draft\'
                        ELSE (
                            SELECT G0."Status"
                            FROM ' . $db_name . '."ERESV_FLOW_DOC" G0
                            WHERE G0."ERESV_DOCNUM" = resv_headers."DocNum"
                        )
                    END AS "DocumentStatus"
                '),
            DB::raw('
                    CASE
                        WHEN resv_headers."ApprovalStatus" = \'W\' THEN \'Waiting\'
                        WHEN resv_headers."ApprovalStatus" = \'P\' THEN \'Pending\'
                        WHEN resv_headers."ApprovalStatus" = \'N\' THEN \'Rejected\'
                        WHEN resv_headers."ApprovalStatus" = \'Y\' THEN \'Approved\'
                        WHEN resv_headers."ApprovalStatus" = \'-\' THEN \'-\'
                    END AS "AppStatus"
                ')
        )
            ->leftJoin("resv_details", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            ->whereRaw("resv_headers.\"DocDate\" BETWEEN '${date_from}' AND '${date_to}' ")
            ->whereRaw("resv_details.\"RequestType\" LIKE '%${request_type}%' ");

        if ($item_type == 'Open Reservation') {
            $rows = $rows->whereRaw('
                    (
                        SELECT G0."Status"
                            FROM ' . $db_name . '."ERESV_FLOW_DOC" G0
                            WHERE G0."ERESV_DOCNUM" = resv_headers."DocNum"

                    ) = \'Open\'

                ')->orderBy("resv_headers.DocNum")
                ->get();
        } else {
            $rows = $rows->whereRaw("resv_headers.\"ItemType\" LIKE '%${item_type}%'");

            if (
                Str::contains($request->user()->department, ['WAREHOUSE', 'SAP'])
                || $request->user()->hasRole('Admin Warehouse')
            ) {
                // if ($request->user()->hasAnyRole('Superuser')) {
                //     $rows = $rows
                //         ->whereIn("resv_headers.WhsCode", ["MW-IT", "MW-ZZ"])
                //         // ->orWhereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
                //     ->orderBy("resv_headers.DocNum")
                //     ->get();
                // } else {

                //     $rows = $rows->orderBy("resv_headers.DocNum")
                //     ->get();
                // }

                $rows = $rows->orderBy("resv_headers.DocNum")
                    ->get();
            } elseif ($request->user()->hasAnyRole('View Data IT')) {
                $rows = $rows
                    ->whereIn("resv_headers.WhsCode", ["MW-IT", "MW-ZZ"])
                    // ->orWhereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
                    ->orderBy("resv_headers.DocNum")
                    ->get();
            } else {
                $rows = $rows->whereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
                    ->orderBy("resv_headers.DocNum")
                    ->get();
            }
        }

        return $rows;
    }
}
