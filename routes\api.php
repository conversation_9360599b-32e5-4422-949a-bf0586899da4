<?php

use App\Http\Controllers\AppController;
use App\Http\Controllers\Approval\ApprovalActionController;
use App\Http\Controllers\Approval\ApprovalMappingUserController;
use App\Http\Controllers\Approval\ApprovalRuleController;
use App\Http\Controllers\Approval\ApprovalStagesController;
use App\Http\Controllers\Approval\BatchApprovalController;
use App\Http\Controllers\Approval\CherryApprovalStagesController;
use App\Http\Controllers\Approval\DocumentApprovalStagesController;
use App\Http\Controllers\AttachmentController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Callback\CallbackPrivyController;
use App\Http\Controllers\Callback\CallbackSignController;
use App\Http\Controllers\Common\ApdNoteController;
use App\Http\Controllers\Common\EmployeePhotoController;
use App\Http\Controllers\Common\ListActionController;
use App\Http\Controllers\Common\MappingItemSafetyController;
use App\Http\Controllers\Common\NewEmployeeController;
use App\Http\Controllers\Common\SafetyDataController;
use App\Http\Controllers\Common\VehicleController;
use App\Http\Controllers\Dashboard\RecentReservationController;
use App\Http\Controllers\Dashboard\ShortcutController;
use App\Http\Controllers\Document\CheckStatusPrivyEkbController;
use App\Http\Controllers\Document\DocumentApprovalController;
use App\Http\Controllers\Document\DocumentController;
use App\Http\Controllers\Document\DocumentCoordinateController;
use App\Http\Controllers\Document\DocumentDownloadController;
use App\Http\Controllers\Document\DocumentDownloadEkbController;
use App\Http\Controllers\Document\DocumentPrivyBackupPlanController;
use App\Http\Controllers\Document\DocumentPrivyController;
use App\Http\Controllers\Document\InvoiceController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Master\ConfigController;
use App\Http\Controllers\Master\DepartmentSubCategoryController;
use App\Http\Controllers\Master\DocumentSubTypeController;
use App\Http\Controllers\Master\EnviroRoleController;
use App\Http\Controllers\Master\EnviroSubRoleController;
use App\Http\Controllers\Master\MasterAirportController;
use App\Http\Controllers\Master\MasterCompanyController;
use App\Http\Controllers\Master\MasterDataController;
use App\Http\Controllers\Master\MasterEmployeeController;
use App\Http\Controllers\Master\MasterPaperController;
use App\Http\Controllers\Master\MasterPermissionController;
use App\Http\Controllers\Master\MasterRolesController;
use App\Http\Controllers\Master\MasterWhsController;
use App\Http\Controllers\Master\OrderIdController;
use App\Http\Controllers\Master\PriorityController;
use App\Http\Controllers\Master\PrivyEnterpriseController;
use App\Http\Controllers\Master\ReleaseNoteController;
use App\Http\Controllers\Master\ResvCostCenterController;
use App\Http\Controllers\Master\ResvSapUsageController;
use App\Http\Controllers\Master\TaskSectionController;
use App\Http\Controllers\Master\UomController;
use App\Http\Controllers\MigrateSapDataController;
use App\Http\Controllers\Notification\NotificationController;
use App\Http\Controllers\Paper\PaperApiController;
use App\Http\Controllers\Report\ReportController;
use App\Http\Controllers\Reservation\ChangeCompanyController;
use App\Http\Controllers\Reservation\ReSendEmailPrController;
use App\Http\Controllers\SapS4\MappingErrorS4Controller;
use App\Http\Controllers\SapS4\ReprocessDocumentToS4Controller;
use App\Http\Controllers\Settings\PrivyRegisterStatusController;
use App\Http\Controllers\Settings\RegisterPrivyController;
use App\Http\Controllers\Tasks\TaskController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Models\Resv\ResvHeader;
use App\Models\Master\ResvSapUsage;
use App\Models\Master\Config;


Route::get('employees2', [MasterEmployeeController::class, 'index']);


Route::get("cherry-document-content/{docnum}", function (Request $request) {
    $header = ResvHeader::where('DocNum', '=', $request->docnum)->first();
    $movementType = ResvSapUsage::where('movement_type', $header->Usage)->first();

    $checkUseNewApproval = Config::where('key', 'ApprovalUseNew')
        ->where('type', 'ApprovalService')
        ->where('company', 'PT IMIP')
        ->first();

    // $document_content = view('email.approval_resv', [
    //     'details' => collect($header->details)->toArray(),
    //     'movementType' => $movementType,
    //     'header' => $header,
    //     'newApproval' => $checkUseNewApproval
    // ])->render();

    $document_content = \Illuminate\Mail\Markdown::parse(
        view('email.approval_resv_markdown', [
            'details' => collect($header->details)->toArray(),
            'movementType' => $movementType,
            'header' => $header,
            'newApproval' => $checkUseNewApproval,
            'countOrder' => collect(collect($header->details)->toArray())->where('OrderId', '!=', null)->count()
        ])->render()
    );


    return response()->json([
        "html" => $document_content
    ]);
});

Route::get('apps', [AppController::class, 'frontData']);
Route::post('/auth/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/auth/login', [AuthController::class, 'login']);
Route::post('/auth/login-local', [AuthController::class, 'loginLocal']);
Route::get('download-manual', [HomeController::class, 'downloadManual']);

Route::get('verification/{url}', [\App\Http\Controllers\VerificationController::class, 'verification']);
Route::get('verification-document/{url}', [
    \App\Http\Controllers\VerificationController::class,
    'verificationDocument'
]);

Route::post('callback', [\App\Http\Controllers\CherryApprovalController::class, 'callback']);
Route::post('callback-sign', [CallbackSignController::class, 'store']);

Route::match(['get', 'post'], '/callback-test', [CallbackPrivyController::class, 'store']);
Route::match(['get', 'post'], '/callback-privy', [CallbackPrivyController::class, 'store']);

Route::post('callback-sap', [\App\Http\Controllers\Approval\SapApprovalController::class, 'callback']);
Route::post('migrate-data-sap', [MigrateSapDataController::class, 'store']);
Route::put('migrate-data-sap', [MigrateSapDataController::class, 'update']);
Route::post('/auth/refresh-token', [AuthController::class, 'refreshToken']);
Route::post('/generate-str', function () {
    return response()->json(\Illuminate\Support\Str::random(40));
});

Route::group(['prefix' => 'sapapproval'], function () {
    Route::post('formpenolakan', [
        \App\Http\Controllers\Approval\SapApprovalController::class,
        'approvalFormPenolakan'
    ]);
});

Route::group(['middleware' => ['superapps_auth']], function () {
    Route::post('reference-no/{username}', [PaperApiController::class, 'referenceNo']);
    Route::post('form/print', [PaperApiController::class, 'print']);
    Route::post('/form/Attachment-list', [PaperApiController::class, 'listAttachment']);
    Route::post('form/Attachment', [PaperApiController::class, 'storeAttachment']);
    Route::post('form/Attachment-delete', [PaperApiController::class, 'destroyAttachment']);


    Route::post('master-form', [PaperApiController::class, 'getMasterForm']);
    Route::post('form/list', [PaperApiController::class, 'index']);
    Route::post('form/single/{id}', [PaperApiController::class, 'show']);
    Route::post('form/delete/{id}', [PaperApiController::class, 'destroy']);
    Route::apiResource('form', PaperApiController::class);
});

Route::group(['prefix' => 'auth'], function () {
    Route::get('/session', [AuthController::class, 'checkSession']);
    Route::get('/user/{nik}', [AuthController::class, 'checkUser']);
});

// Route::apiResource('document', DocumentController::class);
// Route::group(['middleware' => ['auth:sanctum']], function () {
//     Route::apiResource('document', DocumentController::class);
// });

// Route::group(['middleware' => ['auth:sanctum']], function () {
Route::group(['middleware' => ['auth_service']], function () {
    Route::get('photo', [EmployeePhotoController::class, 'index']);

    Route::post('send-email-pr', [ReSendEmailPrController::class, 'store']);

    Route::group(['prefix' => 'dashboard'], function () {
        Route::get('/shortcut', [ShortcutController::class, 'index']);
        Route::get('/recent-reservation', [RecentReservationController::class, 'index']);
    });

    Route::post('notification', [NotificationController::class, 'index']);
    Route::get('notification', [NotificationController::class, 'index']);
    Route::get('unread-notification', [NotificationController::class, 'unRead']);
    Route::get('notification/count', [NotificationController::class, 'countData']);
    Route::post('notification/{id}', [NotificationController::class, 'markAsReadSingleRow']);

    Route::get('list', [ListActionController::class, 'index']);
    Route::group(['prefix' => 'auth'], function () {
        Route::get('/roles', [AuthController::class, 'roles']);
        Route::post('/roles', [AuthController::class, 'roles']);
        Route::get('/permissions', [AuthController::class, 'permissions']);
        Route::post('/permissions', [AuthController::class, 'permissions']);
        Route::get('/me', [AuthController::class, 'user']);
        Route::post('/user', [AuthController::class, 'user']);
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/refresh', [AuthController::class, 'refresh']);
    });

    Route::get("privy-enterprise", [PrivyEnterpriseController::class, 'getEnterprise']);
    Route::get("privy-user-enterprise", [PrivyEnterpriseController::class, 'getUserEnterprise']);

    Route::group(['prefix' => 'report'], function () {
        Route::get('/params', [ReportController::class, 'params']);
        Route::get('/preview', [ReportController::class, 'show']);
        Route::get('/export', [ReportController::class, 'export']);
    });

    Route::post('release-note-read', [ReleaseNoteController::class, 'markAsRead']);
    Route::apiResource('release-note', ReleaseNoteController::class);
    Route::apiResource('register-privy', RegisterPrivyController::class);
    Route::apiResource('register-privy-status', PrivyRegisterStatusController::class);
    Route::apiResource('safety-data', SafetyDataController::class);
    Route::apiResource('apd-note', ApdNoteController::class);
    Route::apiResource('vehicle', VehicleController::class);
    Route::apiResource('mapping-item-safety', MappingItemSafetyController::class);
    Route::apiResource('new-employee', NewEmployeeController::class);
    Route::get('status-batch-ekb', [CheckStatusPrivyEkbController::class, 'index']);
    Route::get('status-batch-ekb/{id}', [CheckStatusPrivyEkbController::class, 'show']);

    Route::get('home-data', [HomeController::class, 'homeData']);
    Route::get('stats', [HomeController::class, 'statistics']);
    Route::get('menus', [HomeController::class, 'menus']);

    Route::get('visit-area', [MasterDataController::class, 'getVisitArea']);
    Route::get('facilities', [MasterDataController::class, 'getFacilities']);

    Route::get('item-master-data', [MasterDataController::class, 'getItemMasterData']);
    Route::post('item-master-data', [MasterDataController::class, 'getItemMasterData']);
    Route::get('item-master-data-export', [MasterDataController::class, 'reportItemMasterdata']);
    Route::get('asset-master-data', [MasterDataController::class, 'getAssetMasterData']);
    Route::get('uom-master-data', [UomController::class, 'index']);
    Route::get('orderid-master-data', [OrderIdController::class, 'index']);
    Route::get('latest-req-item', [MasterDataController::class, 'getLatestRequest']);
    Route::get('list-latest-req', [MasterDataController::class, 'getListRequest']);

    Route::get('attachment', [AttachmentController::class, 'index']);
    Route::post('attachment', [AttachmentController::class, 'store']);
    Route::delete('attachment', [AttachmentController::class, 'destroy']);


    Route::get('cherry/approval', [CherryApprovalStagesController::class, 'index']);
    Route::post('cherry/approval', [CherryApprovalStagesController::class, 'store']);

    Route::prefix('reservation')
        ->group(__DIR__ . '/transaction/reservation.php');

    // e-reservation inventory route
    Route::group([], __DIR__ . '/transaction/inventory.php');
    Route::group([], __DIR__ . '/transaction/enviro.php');

    // Paper Route
    Route::group([], __DIR__ . '/transaction/paper.php');

    Route::group(['prefix' => 'export'], function () {
        Route::get('paper-rapid', [\App\Http\Controllers\Export\ExportDataController::class, 'exportRapid']);
    });

    Route::group(['prefix' => 'tasks'], function () {
        Route::get('workspace', [TaskController::class, 'listWorkSpace']);
        Route::apiResource('task', TaskController::class);
    });

    // approval route
    Route::get('approval/list', [ApprovalStagesController::class, 'index']);
    Route::get('approval/stages', [ApprovalStagesController::class, 'stages']);
    Route::post('approval/cancel', [ApprovalStagesController::class, 'cancelDocument']);
    Route::post('approval/approve', [ApprovalStagesController::class, 'approveDocument']);
    Route::post('approval/reject', [ApprovalStagesController::class, 'rejectDocument']);

    Route::post('bulk-sign-digisign', [ApprovalActionController::class, 'digisignBulkApprove']);
    Route::post('approval/batch-cancel', [ApprovalActionController::class, 'batchCancel']);
    Route::post('approval/batch-approve', [ApprovalActionController::class, 'batchApprove']);
    Route::post('approval/batch-reject', [ApprovalActionController::class, 'batchReject']);

    Route::get('approval/batch-status/{document_id}', [BatchApprovalController::class, 'batchStatus']);
    Route::post('approval/batch-reprocess/{document_id}', [BatchApprovalController::class, 'reProcessBatch']);

    Route::post('document/privy-backup', [DocumentPrivyBackupPlanController::class, 'store']);

    Route::get('document/download', [DocumentDownloadController::class, 'downloadDocument']);
    Route::get('document-ekb/download', [DocumentDownloadEkbController::class, 'index']);
    Route::get('document-ekb/download/{id}', [DocumentDownloadEkbController::class, 'show']);

    Route::get('document/customer', [DocumentController::class, 'getCustomer']);
    Route::get('document/sub-type/{type}', [DocumentController::class, 'getSubType']);

    Route::get('document/stages', [DocumentApprovalStagesController::class, 'index']);
    Route::get('document/cherry', [DocumentApprovalStagesController::class, 'cherry']);
    Route::post('document/stages', [DocumentApprovalStagesController::class, 'store']);

    Route::get('document/approval', [DocumentApprovalController::class, 'documentApproval']);
    Route::post('document/approval', [DocumentApprovalController::class, 'store']);

    Route::post('document/batch-approval', [DocumentApprovalController::class, 'batchSubmitApproval']);
    Route::post('document/cancel-document', [DocumentApprovalController::class, 'cancelApproval']);
    Route::post('document/batch-cancel-document', [DocumentApprovalController::class, 'batchCancelDocument']);
    Route::post('document/batch-delete-document', [DocumentApprovalController::class, 'batchDeleteDocument']);

    Route::post('document/update-coordinate', [DocumentCoordinateController::class, 'updateCoordinate']);
    Route::post('invoice/generate-document', [InvoiceController::class, 'generateDocument']);

    Route::get('check-document', [DocumentController::class, 'checkExternalDocument']);

    Route::apiResource('document-approval', DocumentApprovalController::class);
    Route::apiResource('document', DocumentController::class);
    Route::apiResource('document-privy', DocumentPrivyController::class);
    Route::apiResource('invoice', InvoiceController::class);
    Route::apiResource('resv-sap-usage', ResvSapUsageController::class);
    Route::apiResource('resv-cost-center', ResvCostCenterController::class);

    Route::group(['prefix' => 'master'], function () {
        Route::get('change-company', [ChangeCompanyController::class, 'getWhs']);

        Route::get('employees', [MasterEmployeeController::class, 'index']);
        Route::get('related-employees', [MasterEmployeeController::class, 'relatedEmployee']);
        Route::get('employee-leave/{nik}', [MasterEmployeeController::class, 'leave']);
        Route::get('employee-by-name', [MasterEmployeeController::class, 'employeeByName']);
        Route::get('division', [MasterCompanyController::class, 'division']);
        Route::get('whs', [MasterWhsController::class, 'index']);
        Route::get('item-group-code', [MasterDataController::class, 'getItemGroupCode']);

        Route::get('departments', [MasterEmployeeController::class, 'departments']);

        Route::prefix('users')
            ->group(__DIR__ . '/master/user.php');

        Route::get('permission-role', [MasterRolesController::class, 'permissionRole']);
        Route::post('permission-role', [MasterRolesController::class, 'storePermissionRole']);

        Route::delete('approval/delete-rules/{id}', [ApprovalRuleController::class, 'deleteRules']);
        Route::delete('approval/delete-approver/{id}', [ApprovalRuleController::class, 'deleteApprover']);
        Route::post('approval-specimen', [ApprovalRuleController::class, 'storeSpecimen']);

        // Route::apiResource('user', UserController::class);
        Route::apiResource('department-sub-category', DepartmentSubCategoryController::class);
        Route::apiResource('approval-mapping-user', ApprovalMappingUserController::class);
        Route::apiResource('task-section', TaskSectionController::class);
        Route::apiResource('document-sub', DocumentSubTypeController::class);
        Route::apiResource('task-priority', PriorityController::class);
        Route::apiResource('airport', MasterAirportController::class);
        Route::apiResource('config', ConfigController::class);
        Route::apiResource('enviro-role', EnviroRoleController::class);
        Route::apiResource('enviro-sub-role', EnviroSubRoleController::class);
        Route::apiResource('papers', PaperController::class)->names([
            'index' => 'master-paper.index',
            'store' => 'master-paper.store',
            'show' => 'master-paper.show',
            'update' => 'master-paper.update',
            'destroy' => 'master-paper.destroy'
        ]);
        Route::apiResource('apps', AppController::class);
        Route::apiResource('permissions', MasterPermissionController::class);
        Route::apiResource('roles', MasterRolesController::class);
        Route::apiResource('approval', ApprovalRuleController::class);
    });
});
// });



Route::post('reprocess-push-to-s4-3', [ReprocessDocumentToS4Controller::class, 'pushDocumentTransType3']);
Route::post('reprocess-push-to-s4', [ReprocessDocumentToS4Controller::class, 'pushDocument']);
Route::post('mapping-error-item-code', [MappingErrorS4Controller::class, 'mappingErrorItemCode']);


Route::get('mapping-item-code', function () { });

Route::group(['prefix' => 'apps'], function () {
    // Paper Route
    Route::group([], __DIR__ . '/transaction/app-paper.php');
});
