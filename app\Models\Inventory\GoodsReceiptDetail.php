<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Inventory\GoodsReceiptDetail
 *
 * @property int $id
 * @property int $master_item_id
 * @property int $header_id
 * @property string $item_code
 * @property string $item_name
 * @property string $uom
 * @property float $qty
 * @property string $notes
 * @property string $whs_code
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail query()
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereHeaderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereItemCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereItemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereMasterItemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereUom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceiptDetail whereWhsCode($value)
 * @mixin \Eloquent
 */
class GoodsReceiptDetail extends Model
{
    use HasFactory;
}
