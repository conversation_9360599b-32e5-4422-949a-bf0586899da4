<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Services\SapS4Service;
use Illuminate\Http\Request;

class OrderIdController extends Controller
{
    public function index(Request $request)
    {
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $searchType = isset($request->searchType) ? (string) $request->searchType : null;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 10;

        $service = new SapS4Service();

        $service->login();

        $data = null;
        $total = 0;

        $items = $service->getIoOrder($pages, $row_data, null);
        if ($items) {
            if (!array_key_exists('DATA', $items)) {
                return $this->error('Error', '422', [
                    $items
                ]);
            }

            $data = [];
            foreach ($items['DATA'] as $index => $item) {
                $data[] = [
                    "Keys" => $index,
                    "DESC" => $item["DESC"],
                    "ORDER" => $item["ORDER"],
                    "ORDERTYPE" => $item["ORDERTYPE"],
                ];
            }
            $total = $items['TOTAL_DATA'];
        } else {
            $items = [];
        }

        return response()->json([
            'rows' => $data,
            'items' => $items,
            // 'whs' => $service->getSloc(1, 100, null)['data'],
            "item_groups" => [],
            'total' => $total,
        ]);
    }
}
