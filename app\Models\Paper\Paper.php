<?php

namespace App\Models\Paper;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Master\Airport;

/**
 * App\Models\Paper\Paper
 *
 * @property int $id
 * @property int|null $master_paper_id
 * @property int|null $user_id
 * @property string|null $paper_no
 * @property string|null $user_name
 * @property string|null $address
 * @property string|null $no_hp
 * @property string|null $ktp
 * @property string|null $id_card
 * @property string|null $department
 * @property string|null $company
 * @property string|null $occupation
 * @property string|null $payment
 * @property string|null $name_boss
 * @property string|null $position_boss
 * @property string|null $nik_boss
 * @property string|null $paper_date
 * @property string|null $reason
 * @property string|null $date_out
 * @property string|null $date_in
 * @property string|null $period_stay
 * @property string|null $destination
 * @property string|null $transportation
 * @property string|null $route
 * @property \Illuminate\Support\Carbon|null $print_date
 * @property int|null $created_by
 * @property string $deleted
 * @property string $for_self
 * @property string|null $str_url
 * @property string $status
 * @property string|null $created_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $leave_from_to
 * @property string|null $reference_number
 * @property string|null $swab_date
 * @property string|null $reason_swab
 * @property string $is_complete
 * @property string|null $resv_for
 * @property string|null $travel_purpose
 * @property string|null $reason_purpose
 * @property string|null $cost_cover
 * @property string|null $work_location
 * @property int|null $total_seat
 * @property string|null $request_date
 * @property int|null $flight_origin
 * @property int|null $flight_destination
 * @property string|null $notes
 * @property string|null $email
 * @property string|null $flight_no
 * @property string|null $host_company
 * @property string|null $visitor_company
 * @property string|null $company_officer
 * @property string|null $visitor_officer
 * @property string|null $visitor_address
 * @property string|null $company_email
 * @property string|null $visitor_email
 * @property array|null $plan_visit_area
 * @property string|null $purpose_visit
 * @property int|null $total_guest
 * @property array|null $facilities
 * @property string|null $paper_place
 * @property int|null $seat_available
 * @property int|null $total_assigned
 * @property int|null $flight_origin_approve
 * @property int|null $flight_destination_approve
 * @property string|null $flight_date_approve
 * @property string|null $visitor_no_hp
 * @property string|null $other_facilities
 * @property string|null $reference_no
 * @property string|null $clinic_response
 * @property string|null $updated_name
 * @property string|null $updated_by
 * @property string|null $swab_type
 * @property string|null $vehicle
 * @property string|null $vehicle_no
 * @property string|null $driver
 * @property string|null $issue_date
 * @property-read Airport|null $flightDestination
 * @property-read Airport|null $flightOrigin
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Paper\PaperDetails> $lineItems
 * @property-read int|null $line_items_count
 * @property-read \App\Models\Paper\MasterPaper|null $master
 * @method static \Illuminate\Database\Eloquent\Builder|Paper newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Paper newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Paper query()
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereClinicResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereCompanyEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereCompanyOfficer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereCostCover($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereCreatedName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereDateIn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereDateOut($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereDeleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereDestination($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereDriver($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereFacilities($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereFlightDateApprove($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereFlightDestination($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereFlightDestinationApprove($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereFlightNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereFlightOrigin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereFlightOriginApprove($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereForSelf($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereHostCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereIdCard($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereIsComplete($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereIssueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereKtp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereLeaveFromTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereMasterPaperId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereNameBoss($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereNikBoss($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereNoHp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereOccupation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereOtherFacilities($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePaperDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePaperNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePaperPlace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePayment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePeriodStay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePlanVisitArea($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePositionBoss($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePrintDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper wherePurposeVisit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereReasonPurpose($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereReasonSwab($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereReferenceNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereReferenceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereRequestDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereResvFor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereRoute($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereSeatAvailable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereStrUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereSwabDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereSwabType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereTotalAssigned($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereTotalGuest($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereTotalSeat($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereTransportation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereTravelPurpose($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereUpdatedName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereUserName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereVehicle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereVehicleNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereVisitorAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereVisitorCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereVisitorEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereVisitorNoHp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereVisitorOfficer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Paper whereWorkLocation($value)
 * @mixin \Eloquent
 */
class Paper extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $casts = [
        'facilities' => 'array',
        'plan_visit_area' => 'array',
        'created_at' => 'datetime:Y-m-d h:i:s',
        'print_date' => 'datetime:Y-m-d h:i:s',
    ];

    public function lineItems()
    {
        return $this->hasMany(PaperDetails::class);
    }

    public function master()
    {
        return $this->belongsTo(MasterPaper::class, 'master_paper_id', 'id');
    }

    public function flightOrigin()
    {
        return $this->belongsTo(Airport::class, 'flight_origin', 'id');
    }

    public function flightDestination()
    {
        return $this->belongsTo(Airport::class, 'flight_destination', 'id');
    }
}
