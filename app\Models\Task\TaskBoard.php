<?php

namespace App\Models\Task;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Task\TaskBoard
 *
 * @property int $id
 * @property string $department
 * @property string|null $color
 * @property string|null $background_image
 * @property string $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard whereBackgroundImage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskBoard whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TaskBoard extends Model
{
    use HasFactory;

    protected $guarded = [];
}
