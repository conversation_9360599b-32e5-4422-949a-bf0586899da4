<?php

namespace App\Logging;

use Monolog\Handler\ElasticsearchHandler;
use Monolog\Logger;
use Monolog\Formatter\ElasticsearchFormatter;
use Elastic\Elasticsearch\ClientBuilder;

class CreateElasticsearchLogger
{
    public function __invoke(array $config)
    {
        $client = app('elasticsearch');

        $index = config('services.elasticsearch.index');

        $handler = new ElasticsearchHandler($client, [
            'index' => $index,
            'type' => '_doc',
        ]);

        $handler->setFormatter(new ElasticsearchFormatter($index, '_doc'));

        return new Logger('elasticsearch', [$handler]);
    }
}
