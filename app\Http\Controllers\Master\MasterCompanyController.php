<?php

namespace App\Http\Controllers\Master;

use App\Models\Common\Vehicle;
use App\Http\Controllers\Controller;
use App\Models\User\UserDivision;
use App\Services\SapS4Service;
use App\Traits\MasterSap;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MasterCompanyController extends Controller
{
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function division(Request $request)
    {
        // $department = substr($request->user()->department, 0, 4);
        $userDivision = UserDivision::where('user_id', $request->user()->id)->pluck('division_name');

        // $service = new SapS4Service();
        // $service->login();

        $customer = [];
        // $customers = $service->getCustomer();
        // if ($customers) {
        //     if (array_key_exists('DATA', $customers)) {
        //         foreach ($customers['DATA'] as $key => $value) {
        //             $customer[] = [
        //                 "CardName" => $value['NAME1'],
        //                 "CardCode" => $value['KUNNR'],
        //                 "Address" => $value['KUNNR'],
        //             ];
        //         }
        //     }
        // }

        $vehicle = Vehicle::pluck('vehicle_no');
        // $all_division = ViewEmployee::where('Department', 'LIKE', '%' . $department . '%')
        // $all_division = ViewEmployee::whereIn('Department', $userDivision)
        //     ->select('Department')
        //     ->distinct()
        //     ->get();

        // $arr_division = [];
        // foreach ($all_division as $item) {
        //     $arr_division[] = $item->Department;
        // }

        return response()->json([
            'all_division' => $userDivision,
            'customer' => $customer,
            'vehicle' => $vehicle
        ]);
    }
}
