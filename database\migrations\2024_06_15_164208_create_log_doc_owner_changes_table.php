<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('log_doc_owner_changes', function (Blueprint $table) {
            $table->id();
            $table->string('old_owner', 100);
            $table->string('new_owner', 100);
            $table->date('efective_date');
            $table->string('notes', 200);
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('log_doc_owner_changes');
    }
};
