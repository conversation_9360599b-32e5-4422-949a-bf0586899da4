<?php

namespace App\Notifications\Tasks;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SendAssignNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;
    protected $task;
    protected $assigner;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user, $task, $assigner)
    {
        $this->user = $user;
        $this->task = $task;
        $this->assigner = $assigner;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = config('app.front_url') . '/tasks?department='
            . $this->task->department . '&userId='
            . $this->user->id . '&display=list';

        return (new MailMessage)
            ->subject($this->assigner . ' assign you to task: ' . $this->task->title)
            ->line('Hey ' . $this->user->name . ', ' . $this->assigner . ' assign you to task: ')
            ->line($this->task->title)
            ->line(' created by ' . $this->task->user->name)
            ->action('Go to Task', $url)
            ->line('This is auto generate message!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            // 'source_id' => $this->task->board_id,
            'department' => $this->task->department,
            'type' => 'task'
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return BroadcastMessage
     */
    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'task' => $this->task,
            'user' => $this->user,
            'assigner' => $this->assigner
        ]);
    }
}
