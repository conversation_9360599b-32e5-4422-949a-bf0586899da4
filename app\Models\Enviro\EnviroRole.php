<?php

namespace App\Models\Enviro;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Enviro\EnviroRole
 *
 * @property int $id
 * @property string $name
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroRole query()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroRole whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroRole whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroRole whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroRole whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EnviroRole extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
