<?php

namespace App\Services;

use App\Jobs\ProcessInternalSignDocument;
use App\Models\Approval\ApprovalStage;
use App\Models\Approval\BatchApproval;
use App\Models\Common\Attachment;
use App\Models\Document\Document;
use App\Models\User;
use App\Models\View\ViewEmployee;
use App\Notifications\ApprovalMeteraiApprove;
use App\Services\ChangeSignService;
use App\Traits\AppConfig;
use Carbon\Carbon;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;

class ApprovalActionService
{
    use AppConfig;
    /**
     * Sends a notification to a requester indicating the status of their request.
     *
     * @param array $requesterName An array of requester data, including their name and ID.
     * @param array $requestItem An array of requested items.
     * @param string $status The status of the request.
     * @param object $document The document associated with the request.
     * @throws \Exception Description of exception
     * @return void
     */
    public function sendNotificationToRequester($requesterName, $requestItem, $status, $document, $requesterCount, $countDocument)
    {
        $requester = array_unique($requesterName, SORT_REGULAR);
        // $requester = array_unique($requesterName);
        $count_doc = array_count_values($requesterCount);
        // $documentType = ucfirst($document->document_type);
        foreach ($requester as $key => $value) {
            $project = [
                'greeting' => 'Dear ' . $value['name'] . ',',
                // 'body' => $countDocument . ": $documentType Documents has been $status. Please check the documents.",
                'body' => $countDocument . ": Documents has been $status. Please check the documents.",
                'inv' => implode(", ", $requestItem[$value['name']]),
                // 'subject' => $countDocument . ": $documentType Documents has been $status",
                'subject' => $countDocument . ":  Documents has been $status",
                'document' => $document,
                'status' => $status
                // 'cc' => $requester->OfficeEmailAddress
            ];
            // throw new \Exception(json_encode($project), 1);


            $user = User::find($value['id']);

            $user->notify(new ApprovalMeteraiApprove($project));
        }
    }

    /**
     * Retrieves approval request data for a given set of rows and request.
     *
     * @param array $rows The rows to retrieve approval request data for.
     * @return array The approval request data.
     *@throws \Exception Description of exception
     */
    public function getApprovalRequestData(array $rows, $approvalType): array
    {
        $requesterName = [];
        $requesterCount = [];
        $requestItem = [];
        foreach ($rows as $row) {
            $document_id = ($approvalType) ? $row->document_id : $row->id;
            $document = Document::find($document_id);

            $requester = ViewEmployee::where('Nik', $document->userCreate->username)->first();
            $requestItem[$requester->Name][] = $document->external_document_number;
            $requesterName[$requester->Name] = [
                'id' => $document->created_by,
                'name' => $requester->Name,
                'email' => $requester->OfficeEmailAddress
            ];
            $requesterCount[] = $requester->Name;
        }

        return [
            "requesterName" => $requesterName,
            "requesterCount" => $requesterCount,
            "requestItem" => $requestItem
        ];
    }

    /**
     * Updates the approval stage of a document.
     *
     * @param int $document_id The ID of the document to update.
     * @param string $notes The notes to add to the document.
     * @param int $user_id The ID of the user updating the document.
     * @param string $status The new status of the document, should be one of 'approved' or 'rejected'.
     * @return void
     * @throws \Exception
     */
    public function updateApprovalStage(int $document_id, string $notes, int $user_id, string $status)
    {
        if (Str::contains($status, ['approved', 'rejected'])) {
            $data = [
                'status' => str_replace(' ', '', $status),
                'notes' => $notes,
                'response_date' => Carbon::now()
            ];
        } else {
            $data = [
                'status' => 'canceled',
                'cancel_by' => $user_id,
                'cancel_date' => Carbon::now()
            ];
        }

        // Log::info('data approved or reject', [
        //     'data' => $data,
        //     'document_id' => $document_id,
        //     'user_id' => $user_id,
        //     'status' => $status,
        // ]);

        ApprovalStage::where('document_id', $document_id)
            // ->where('user_id', $user_id)
            ->update($data);

        if (Str::contains($status, ['rejected', 'canceled'])) {
            $this->updateDocumentStatus($document_id, $status);
        }
    }

    /**
     * Updates the approval stage of a document.
     *
     * @param array $document_id The ID of the document to update.
     * @param string $notes The notes to add to the document.
     * @param int $user_id The ID of the user updating the document.
     * @param string $status The new status of the document, should be one of 'approved' or 'rejected'.
     * @return void
     * @throws \Exception
     */
    public function updateApprovalStageMultiple(array $document_id, string $notes, int $user_id, string $status)
    {
        if (Str::contains($status, ['approved', 'rejected'])) {
            $data = [
                'status' => str_replace(' ', '', $status),
                'notes' => $notes,
                'response_date' => Carbon::now()
            ];
        } else {
            $data = [
                'status' => 'canceled',
                'cancel_by' => $user_id,
                'cancel_date' => Carbon::now()
            ];
        }

        // Log::info('data approved or reject multiple', [
        //     'data' => $data,
        //     'document_id' => $document_id,
        //     'user_id' => $user_id,
        //     'status' => $status,
        // ]);

        $countApprovalStages = ApprovalStage::where("document_id", $document_id)->count();

        if ($countApprovalStages > 1) {
            ApprovalStage::whereIn('document_id', $document_id)
                ->where('user_id', $user_id)
                ->update($data);

            ApprovalStage::whereIn('document_id', $document_id)
                ->where('user_id', '<>', $user_id)
                ->update([
                    'status' => str_replace(' ', '', $status),
                ]);
        } else {
            ApprovalStage::whereIn('document_id', $document_id)
                // ->where('user_id', $user_id)
                ->update($data);
        }


        if (Str::contains($status, ['rejected', 'canceled'])) {
            Document::whereIn('id', $document_id)
                ->update(['status' => $status]);
        }
    }

    /**
     * Updates the status of a document in the database.
     *
     * @param int $document_id The ID of the document to update.
     * @param string $status The new status to set for the document.
     * @throws \Exception If the update query fails to execute.
     * @return void
     */
    public function updateDocumentStatus($document_id, $status)
    {
        Document::where('id', $document_id)
            ->update(['status' => $status]);
    }

    /**
     * Check if a document has received final approval and send a notification to the requester if so.
     *
     * @param object $document The ID of the document to check.
     * @param array $requesterName The number of requesters associated with the document.
     * @param mixed $requestItem The request item to check.
     * @param array $requesterName The name of the requester.
     * @throws \Exception if an error occurs while checking the approval status.
     */
    public function checkFinalApproval($document, $requesterName, $requestItem, $requesterCount, $countDocument)
    {
        $stage = ApprovalStage::where('document_id', $document->id)
            ->orderBy('id', 'desc')
            ->first();

        if ($stage) {
            // Log::info('stage', [
            //     'stage update' => $stage,
            //     'document number' => $document->document_number
            // ]);

            if (Str::contains($stage->status, ['approved'])) {
                $this->updateDocumentStatus($document->id, 'approved - on process');
            }

            if (Str::contains($stage->status, ['approved', 'rejected'])) {
                $this->sendNotificationToRequester($requesterName, $requestItem, $stage->status, $document, $requesterCount, $countDocument);
            }
        }
    }
    /**
     * Processes a sign document.
     *
     * @param Document $document the document to be signed
     * @param int $countDocument the number of documents to be signed
     * @param int $userId the ID of the user signing the document
     * @throws \Exception if there is an error in processing the document
     * @return void
     */
    public function processSignDocument(Document $document, $countDocument, $userId)
    {
        $fileName = Attachment::where('source_id', $document->id)
            ->where('type', 'peruri')
            ->first();

        if ($document->status == 'approved - on process') {
            if ($document->internal_document == 'Y') {
                // Log::info('Process sign document internal =' . $document->document_number);
                $this->processInternalDocument($document, $fileName, $document->id);
            } else {
                // Log::info('Process sign document NON INTERNAL =' . $document->document_number);
                $this->processNonInternalDocument($document->id, $document, $countDocument, $fileName, $userId);
            }
        }
    }

    /**
     * Processes an internal document.
     *
     * @param mixed $document the document to process
     * @param string $fileName the name of the file
     * @param int $doc_id the ID of the document
     * @return void
     */
    public function processInternalDocument($document, $fileName, $doc_id)
    {
        $batch_approval = BatchApproval::where('document_id', $doc_id)->pluck('callback_message');
        // Artisan::call('queue:restart');
        Artisan::call('cache:clear');

        $batch = Bus::batch([
            [new ProcessInternalSignDocument($document, $fileName)],
        ])->catch(function (Batch $batch, Throwable $e) use ($doc_id) {
            $document = Document::find($doc_id);
            $document->status = 'approved - failed';
            $document->save();

            // event(new DocumentEvent($document->created_by));
        })->finally(function (Batch $batch) use ($batch_approval, $doc_id) {
            $batch_approval = BatchApproval::where('document_id', $doc_id)->pluck('callback_message');
            $document = Document::find($doc_id);
            $count_success = 0;
            foreach ($batch_approval as $key => $value) {
                if (Str::contains($value, ['Success'])) {
                    ++$count_success;
                }
            }
            $document->status = 'approved - finish';
            // if (count($batch_approval) == $count_success && $count_success > 0) {
            //     $document->status = 'approved - finish';
            // } else {
            //     $document->status = 'approved - failed';
            // }
            $document->save();

            // event(new DocumentEvent($document->created_by));
        })
            ->allowFailures()
            ->dispatch();

        $document = Document::find($document->id);
        $document->batch_id = $batch->id;
        $document->save();
    }

    /**
     * Processes a non-internal document.
     *
     * @param mixed $doc_id The document ID.
     * @param mixed $document The document.
     * @param mixed $count_document The document count.
     * @param mixed $fileName The file name.
     * @param mixed $request The request.
     */
    public function processNonInternalDocument($doc_id, $document, $count_document, $fileName, $userId)
    {
        $batch_approval = BatchApproval::where('document_id', $doc_id)->pluck('callback_message');

        $requester = ViewEmployee::where('Nik', $document->userCreate->username)->first();
        // $requestItem[$requester->Name][] = $document->external_document_number;

        if ($count_document == 1) {
            $project = [
                'greeting' => 'Dear ' . $requester->Name . ',',
                'body' => $document->external_document_number . ' has been approved',
                'subject' => $document->external_document_number . ' has been approved',
                'inv' => '',
                'cc' => $requester->OfficeEmailAddress
            ];

            // Notification::route('mail', $requester->OfficeEmailAddress)
            //     ->notify(new ApprovalMeteraiApprove($project));
        }

        // Artisan::call('queue:restart');
        Artisan::call('cache:clear');
        // Log::info('processNonInternalDocument');
        $checkUseDigisign = $this->getConfigByName('UseDigisign', 'GENERAL');
        // change approval sign provider
        $changeSign = new ChangeSignService();
        $changeSign->changeSign($document, $fileName, $userId, $doc_id, $batch_approval, $checkUseDigisign);
    }
}
