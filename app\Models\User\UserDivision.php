<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\User\UserDivision
 *
 * @property int $id
 * @property int $user_id
 * @property string $division_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserDivision newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserDivision newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserDivision query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserDivision whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserDivision whereDivisionName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserDivision whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserDivision whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserDivision whereUserId($value)
 * @mixin \Eloquent
 */
class UserDivision extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $connection = 'sqlsrv';
}
