<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreGoodsIssueRequest;
use App\Models\Inventory\Inventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GoodsIssueController extends Controller
{
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "name";
        $order = (!empty($options->sortDesc)) ? (($options->sortDesc[0]) ? "desc" : "asc") : 'asc';
        $offset = $pages;

        $result = array();
        $query = GoodsIssue::with(['lineItems', 'createdUser', 'updatedUser']);

        $result["total"] = $query->count();

        $all_data = $query->offset($offset)
            ->orderBy($sorts, $order)
            ->limit($row_data)
            ->get();

        $result = array_merge($result, [
            "rows" => $all_data,
        ]);
        return $this->success($result);
    }

    public function store(StoreGoodsIssueRequest $request)
    {
        DB::beginTransaction();
        try {
            $header = GoodsIssue::create($request->form);

            DB::commit();
            return $this->success('Date stored');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage());
        }
    }
}
