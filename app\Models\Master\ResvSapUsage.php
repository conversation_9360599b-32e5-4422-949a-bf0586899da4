<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\ResvSapUsage
 *
 * @property int $id
 * @property string $division
 * @property string $work_location
 * @property string $movement_type
 * @property string $description
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage query()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage whereDivision($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage whereMovementType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvSapUsage whereWorkLocation($value)
 * @mixin \Eloquent
 */
class ResvSapUsage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
