<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ClinicNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $receiver;
    public $subjects;
    public $content;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($receiver, $content, $subjects)
    {
        $this->receiver = $receiver;
        $this->content = $content;
        $this->subjects = $subjects;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('email.clinic_notification')
            // ->from('<EMAIL>')
            ->subject($this->subjects)
            ->with([
                'receiver' => $this->receiver,
                'content' => $this->content
            ]);
    }
}
