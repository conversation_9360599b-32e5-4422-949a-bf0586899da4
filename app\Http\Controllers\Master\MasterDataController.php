<?php

namespace App\Http\Controllers\Master;

use App\Exports\ReportExport;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\View\ViewEmployee;
use App\Services\SapS4Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class MasterDataController extends Controller
{
    protected $totalRow = 0;

    public function __construct()
    {
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVisitArea(Request $request)
    {
        $arr = [
            'Smelter',
            'Power Plan',
            'Factory',
            'Mine Site',
            'Harbour',
            'All Area'
        ];
        return $this->success([
            'rows' => $arr
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFacilities(Request $request)
    {
        $arr = [
            'Meeting Room with',
            'LCD Projector & Dispay Screen ',
            'Transport on site ',
            'Other',
        ];
        return $this->success([
            'rows' => $arr
        ]);
    }

    /**
     * @param $request
     * @param $response_type
     * @return array|void
     */
    protected function queryItemMasterData($request, $response_type)
    {
        $start = time();
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $itemGroup = $request->itemGroups ?? null;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 10;
        $row_data = ((isset($response_type)) ? 10000 : $row_data);
        $search = isset($request->search) ? (string) $request->search : "";
        $form = json_decode($request->form);
        $item_type = isset($form->ItemType) ? $this->convertItemType((string) $form->ItemType) : null;
        $category_type = isset($form->CategoryType) ? (string) $form->CategoryType : null;
        $request_type = isset($form->RequestType) ? (string) $form->RequestType : null;
        $select_type = isset($request->searchType) ? (string) $request->searchType : null;
        $whs = (isset($form->WhsCode)) ? explode(' - ', $form->WhsCode)[0] : null;

        $service = new SapS4Service();

        $service->login();

        $itemName = ($select_type == 'Item Name') ? $search : "";
        $itemCode = ($select_type == 'Item Code') ? (str_contains($search, ',') ? explode(',', $search) : $search) : "";

        if ($row_data == '-1') {
            $row_data = 10000;
        }

        $data = null;
        $total = 0;
        $user = User::where('id', '=', auth()->user()->id)->first();
        if ($request_type == 'Restock') {
            if ($user->department == 'ITRACC' || $user->hasAnyRole(['Superuser', 'Admin E-Resv Restock NRS'])) {
                $item_type = ['RS', 'NRS'];
            }
        }

        if ($user->location == 'BDM MOROWALI') {
            $item_type = ['RS', 'NRS'];
        }

        if ($user->username == '88101989') {
            $item_type = ['RS', 'NRS'];
        }

        $items = $service->getMaterial($pages, $row_data, $itemCode, $itemGroup, $itemName, $item_type, $whs);
        if ($items) {
            if (!array_key_exists('DATA', $items)) {
                return response()->json([
                    'rows' => [],
                    'message' => $items['MESSAGE']
                ]);
            }
            session(['totalItem' . $request->user()->id => $items['TOTAL_DATA']]);

            $data = $service->transformItemDataFromS4($items, $whs);

            $total = $items['TOTAL_DATA'];
        }

        return $data;
    }

    public function reportItemMasterdata(Request $request)
    {
        $arr = $this->queryItemMasterData($request, 'report');
        $header = ['No', 'Item Code', 'Item Name', 'Item Type', 'WH', 'UoM', 'OnHand', 'Available', 'Sub Group'];

        $rows = [];
        foreach ($arr as $value) {
            $rows[] = [
                $value['RowNo'],
                $value['ItemCode'],
                $value['ItemName'],
                $value['U_ItemType'],
                $value['WhsCode'],
                $value['InvntryUom'],
                $value['OnHand'],
                $value['Available'],
                $value['SubGroupName'],
            ];
        }

        return Excel::download(new ReportExport($header, $rows, $request), "report.xlsx");
    }

    protected function convertItemType($itemType)
    {
        switch ($itemType) {
            case 'Ready Stock':
                return 'RS';

            case 'Non Ready Stock':
                return 'NRS';

            case 'Service':
                return 'SERVICE';

            case 'Asset':
                return ['NRS', 'ASSET'];

            default:
                return null;
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getItemMasterData(Request $request): \Illuminate\Http\JsonResponse
    {
        $start = time();
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $itemGroup = $request->itemGroups ?? null;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 10;
        $search = isset($request->search) ? (string) $request->search : "";
        $form = json_decode($request->form);
        $item_type = isset($form->ItemType) ? $this->convertItemType((string) $form->ItemType) : null;
        $category_type = isset($form->CategoryType) ? (string) $form->CategoryType : null;
        $request_type = isset($form->RequestType) ? (string) $form->RequestType : null;
        $select_type = isset($request->searchType) ? (string) $request->searchType : null;
        $whs = (isset($form->WhsCode)) ? (($request->externalApp) ? $request->WhsCode : explode(' - ', $form->WhsCode)[0]) : null;

        $service = new SapS4Service();

        // $service->login();

        $itemName = ($select_type == 'Item Name') ? $search : "";
        $itemCode = ($select_type == 'Item Code') ? (str_contains($search, ',') ? explode(',', $search) : $search) : "";

        if ($row_data == '-1') {
            $row_data = 10000;
        }

        $data = null;
        $total = 0;

        $user = User::where("id", $request->user()->id)->first();

        if ($request_type == 'Restock') {
            if ($user->department == 'ITRACC' || $user->hasAnyRole(['Superuser', 'Admin E-Resv Restock NRS'])) {
                $item_type = ['RS', 'NRS'];
            }
        }

        if ($user->location == 'BDM MOROWALI' || $user->hasAnyRole(['Admin E-Resv Display All Item'])) {
            if (!isset($request->WhsCode)) {
                $item_type = ['RS', 'NRS', 'SERVICE', 'ASSET'];
            } elseif (!str($item_type)->contains(['Service'])) {
                $item_type = ['RS', 'NRS'];
            }
            // $whs = $user->userWhs->pluck('whs_code')->toArray();
        }



        if ($user->username == '88101989') {
            $item_type = [];
        }

        // if ($item_type == 'Asset') {
        //     $item_type = 'NRS';
        // }
        $items = $service->getMaterial($pages, $row_data, $itemCode, $itemGroup, $itemName, $item_type, $whs);
        if ($items) {
            if (!array_key_exists('DATA', $items)) {
                return response()->json([
                    'rows' => [],
                    "whs" => $request->WhsCode,
                    'item_type' => $item_type,
                    'message' => $items['MESSAGE']
                ]);
            }
            session(['totalItem' . $request->user()->id => $items['TOTAL_DATA']]);

            $data = $service->transformItemDataFromS4($items, $whs);

            $total = $items['TOTAL_DATA'];
        }

        $itemGroups = $service->getItemGroup(1, 100, null);
        $itemGroup = [];
        if ($itemGroups) {
            foreach ($itemGroups['DATA'] as $item) {
                $itemGroup[] = [
                    "U_ItmsGrpCod" => $item["MATTYPE"],
                    "ItmsGrpNam" => $item["MATTYPEDESC"],
                ];
            }
        }

        return response()->json([
            'rows' => $data,
            'item_type' => $item_type,
            // 'whs' => $service->getSloc(1, 100, null)['data'],
            "item_groups" => $itemGroup,
            'total' => $total,
            'totalRow' => session('totalItem' . $request->user()->id)
        ]);
    }

    public function getAssetMasterData(Request $request)
    {

        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $selectedItem = $request->itemGroups ?? null;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 10;
        $form = json_decode($request->form);
        $search = isset($request->search) ? (string) $request->search : "";
        $item_type = isset($form->ItemType) ? (string) $form->ItemType : "";
        $select_type = isset($request->searchType) ? (string) $request->searchType : null;
        $whs = (isset($form->WhsCode)) ? explode(' - ', $form->WhsCode)[0] : null;
        $offset = $pages;
        $result = array();

        $service = new SapS4Service();

        $service->login();
        $total = 0;
        $data = [];
        $assetCode = null;
        if ($select_type == 'Item Code') {
            $assetCode = $search;
        }
        $assetName = null;
        if ($select_type == 'Item Name') {
            $assetName = $search;
        }
        $items = $service->getAsset($pages, $row_data, $assetCode, $assetName);
        if ($items) {
            if (!array_key_exists('DATA', $items)) {
                return $this->error('Error', '422', [
                    $items
                ]);
            }

            foreach ($items['DATA'] as $index => $item) {
                $data[] = [
                    "Keys" => $index,
                    "ItemCode" => $item["ANLN1"],
                    "ItemName" => $item['TXT50'],
                    "OnHand" => 0,
                    "ItmsGrpCod" => null,
                    "ItmsGrpNam" => null,
                    "Available" => 0,
                    "InvntryUom" => $item['MEINS'],
                    "U_SubGroup" => null,
                    "SubGroupName" => null,
                    "InvntItem" => null,
                    "U_Department" => null,
                    "U_Period" => null,
                    "U_Category" => null,
                    "U_AppResBy" => null,
                    "MinLevel" => null,
                    "U_ItemType" => 'NRS'
                ];
            }

            $total = $items['TOTAL_DATA'];
        }

        return response()->json([
            'rows' => $data,
            'items' => $items,
            // 'whs' => $service->getSloc(1, 100, null)['data'],
            "item_groups" => [],
            'total' => $total
        ]);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getItemGroupCode(Request $request)
    {
        $service = new SapS4Service();
        $result = $service->getItemGroup(1, 100, null);
        $arr = [];
        if ($result) {
            foreach ($result['DATA'] as $item) {
                $arr[] = [
                    "item_group_code" => $item["MATTYPE"],
                    "item_group_name" => $item["MATTYPEDESC"],
                ];
            }
        }

        return $this->success([
            'rows' => $arr
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLatestRequest(Request $request)
    {
        $req_date = $request->ReqDate;
        $whs_code = $request->WhsCode;
        $item_code = $request->ItemCode;

        $last_request = DB::connection('sqlsrv')
            ->table("resv_details")
            ->leftJoin("resv_headers", "resv_details.U_DocEntry", "resv_headers.U_DocEntry")
            ->select("resv_details.ReqDate", "resv_details.ReqNotes", "resv_headers.Requester")
            ->whereNotIn("resv_headers.ApprovalStatus", ["-", "N", "W"])
            ->where("resv_details.ReqDate", "<", $req_date)
            ->where("resv_details.WhsCode", "=", $whs_code)
            ->where("resv_details.ItemCode", "=", $item_code)
            ->orderBy("resv_details.LineNum", "DESC")
            ->first();

        if ($last_request) {
            $user = ViewEmployee::where('Nik', '=', $last_request->Requester)
                ->first();

            $data = [
                'ReqDate' => $last_request->ReqDate,
                'ReqNotes' => $last_request->ReqNotes,
                'U_UserName' => ($user) ? $user->Name : '',
            ];
        } else {
            $data = [
                'ReqDate' => '',
                'ReqNotes' => '',
                'U_UserName' => '',
            ];
        }

        return response()->json([
            "rows" => $data
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getListRequest(Request $request)
    {
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $selectedItem = isset($request->itemGroups) ? $request->itemGroups : null;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 10;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "U_ItemCode";
        $order = isset($request->sortBy[0]) ? (string) $options->sortDesc[0] : "desc";
        $offset = $pages;

        $req_date = $request->reqDate;
        $whs_code = $request->whsCode;
        $item_code = $request->itemCode;

        $last_requests = DB::connection('sqlsrv')
            ->table("resv_details")
            ->leftJoin("resv_headers", "resv_details.U_DocEntry", "resv_headers.U_DocEntry")
            ->select(
                "resv_details.ReqDate",
                "resv_details.ReqQty",
                "resv_details.ReqNotes",
                "resv_headers.U_DocEntry",
                "resv_headers.DocNum",
                "resv_details.LineNum",
                "resv_headers.Requester"
            )
            ->whereNotIn("resv_headers.ApprovalStatus", ["-", "N", "W"])
            //->where("resv_details.ReqDate", "<", $req_date)
            ->where("resv_details.WhsCode", "=", $whs_code)
            ->where("resv_details.ItemCode", "=", $item_code)
            ->orderBy("resv_details.ReqDate", "DESC")
            ->offset($offset)
            ->limit($row_data);

        $data = [];
        foreach ($last_requests->get() as $item) {
            $user = ViewEmployee::where('Nik', '=', $item->Requester)
                ->first();

            $data[] = [
                'ReqDate' => $item->ReqDate,
                'ReqNotes' => $item->ReqNotes,
                'ReqQty' => $item->ReqQty,
                'U_DocEntry' => $item->U_DocEntry,
                'DocNum' => $item->DocNum,
                'LineNum' => $item->LineNum,
                'U_UserName' => ($user) ? $user->Name : '',
            ];
        }

        return response()->json([
            "total" => $last_requests->count(),
            "rows" => $data,
        ]);
    }
}
