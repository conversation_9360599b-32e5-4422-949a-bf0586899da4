<?php

namespace App\Services;


use App\Traits\AppConfig;
use Illuminate\Support\Facades\Http;

class ConvertDocxToPdfService
{
    use AppConfig;

    /**
     * Converts a DOCX file to a PDF file using the LibreOffice API.
     *
     * @param string $pathDocx The path to the DOCX file to be converted.
     * @param string $pathToSavingDirectory The path to the directory where the converted PDF file will be saved.
     * @param string $pdfFileName The name of the converted PDF file.
     * @throws \App\Exceptions\CustomException If an exception occurs during the conversion process.
     * @return string The filename of the converted PDF file.
     */
    public function convert(string $pathDocx, string $pathToSavingDirectory, string $pdfFileName)
    {
        try {
            $apiUrl = $this->getConfigByName('ApiDocumentConvert', 'CONVERT');
            // $request = Gotenberg::libreOffice($apiUrl)
            //     ->outputFilename($pdfFileName)
            //     ->convert(Stream::path($pathDocx));

            // $filename = Gotenberg::save($request, $pathToSavingDirectory);

            // return $filename;

            $response = Http::attach(
                'files',
                file_get_contents($pathDocx),
                basename($pathDocx)
            )->post($apiUrl);

            $pdfContent = $response->body();
            // info("response convert to pdf", [
            //     'status' => $response->status(),
            //     'successful' => $response->successful(),
            //     'body' => ($response->status() == 200) ? $response->json() : $response->body(),
            // ]);
            file_put_contents($pathToSavingDirectory . $pdfFileName, $pdfContent);
            return $pdfFileName;
        } catch (\Exception $exception) {
            // throw new \App\Exceptions\CustomException($exception->getMessage());
            throw new \Exception($exception->getMessage());
        }
    }
}
