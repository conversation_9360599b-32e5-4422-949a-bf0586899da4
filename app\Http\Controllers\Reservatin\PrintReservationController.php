<?php

namespace App\Http\Controllers\Reservatin;

use App\Http\Controllers\Controller;
use App\Services\ReservationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use PhpOffice\PhpWord\Exception\CopyFileException;
use PhpOffice\PhpWord\Exception\CreateTemporaryFileException;

class PrintReservationController extends Controller
{
    /**
     * Index function for handling the request to print reservation.
     *
     * @param Request $request The request object.
     * @throws CopyFileException
     * @throws CreateTemporaryFileException
     * @return JsonResponse Base64 string of files
     */
    public function index(Request $request): JsonResponse
    {
        $form = json_decode($request->form);
        $type = $request->type;


        $service = new ReservationService();

        $file_name = $service->printDocument($form, $type, auth()->user()->username, 'queue');

        $base64 = chunk_split(base64_encode(
            file_get_contents(
                public_path($file_name)
            )
        ));

        return $this->success([
            'base64' => $base64
        ]);
    }
}
