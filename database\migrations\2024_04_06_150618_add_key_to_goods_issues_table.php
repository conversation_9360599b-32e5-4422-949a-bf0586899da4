<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::rename('goods_issues', 'inventories');
        Schema::rename('goods_issue_details', 'inventory_details');

        Schema::table('inventories', function (Blueprint $table) {
            $table->string('doc_type', 50)->default('out')->after('doc_number');
            $table->dateTime('print_at');
            $table->index(['doc_number', 'doc_type', 'status', 'created_by', 'updated_by']);
        });

        Schema::table('inventory_details', function (Blueprint $table) {
            $table->renameColumn('master_item_id', 'resv_detail_id');
            $table->index(['resv_detail_id', 'header_id', 'item_code', 'created_by', 'updated_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Do nothing
    }
};
