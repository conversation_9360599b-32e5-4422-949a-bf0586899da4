<table style="width: 100%;font-size: 8px;border:1px solid black;border-collapse:collapse;">
  <thead>
  <tr><td colspan="{{ count($header) }}"></td></tr>
  <tr>
    <td colspan="{{ count($header) }}">
      <strong>{{ strtoupper($request['itemType']) }} {{ $request['dateFrom'] }} {{ $request['dateTo'] }}</strong>
    </td>
  </tr>
  <tr><td colspan="{{ count($header) }}"></td></tr>
  <tr>
    @for ($i = 0; $i < count($header); $i++)
      <th style="border:1px solid black;"><strong>{{ $header[$i] }}</strong></th>
    @endfor
  </tr>
  </thead>
  <tbody>
    @for ($i = 0; $i < count($rows) ; $i++)
      <tr>
        @for ($j = 0; $j < count($rows[$i]) ; $j++)
          <td style="border:1px solid black;">{{ preg_replace('/[^\P{C}\n]+/u', '', html_entity_decode($rows[$i][$j], ENT_QUOTES, 'UTF-8')) }}</td>
        @endfor
      </tr>
    @endfor
  </tbody>
</table>
