<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRegisterTokenToRegisterPriviesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('register_privies', function (Blueprint $table) {
            $table->string('register_token', 100)->after('identity')->nullable();
            $table->string('status', 100)->after('identity')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('register_privies', function (Blueprint $table) {
            //
        });
    }
}
