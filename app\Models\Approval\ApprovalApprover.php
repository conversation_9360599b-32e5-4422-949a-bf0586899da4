<?php

namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Approval\ApprovalApprover
 *
 * @property int $id
 * @property int $approval_id
 * @property int $user_id
 * @property int $sequence
 * @property string $types
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property string $is_specimen
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereApprovalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereIsSpecimen($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereSequence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereTypes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover whereUserId($value)
 * @mixin \Eloquent
 */
class ApprovalApprover extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $table = 'approval_approver';

    protected $casts = [
        'user_id' => 'integer',
        'approval_id' => 'integer',
    ];
}
