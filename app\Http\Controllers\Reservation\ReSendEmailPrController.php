<?php

namespace App\Http\Controllers\Reservation;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class ReSendEmailPrController extends Controller
{
    public function store(Request $request)
    {
        $document_id = $request->DocumentReferenceID;

        try {
            Artisan::call('pr:email', [
                'docnum' => $document_id
            ]);

            return response()->json([
                'message' => 'Success'
            ]);
        } catch (\Exception $th) {
            throw new \Exception($th->getMessage());
        }
    }
}
