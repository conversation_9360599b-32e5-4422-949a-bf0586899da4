<?php

namespace Database\Seeders;

use App\Models\Common\Application;
use App\Models\User;
use App\Models\User\UserApp;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = User::create([
            'name' => 'manager',
            'username' => 'manager',
            'email' => '<EMAIL>',
            'password' => bcrypt('*8Ultra')
        ]);

        $apps = Application::all();

        foreach ($apps as $app) {
            UserApp::create([
               'user_id' => $user->id,
                'app_id' => $app->id
            ]);
        }
    }
}
