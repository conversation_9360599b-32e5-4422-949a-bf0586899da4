<?php

namespace App\Services;

use App\Models\Common\SafetyData;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use App\Models\View\ViewEmployee;

class CherryApprovalService
{
    public function insertSafetyAndGaData($header, $details)
    {
        foreach ($details as $key => $value) {
            if ($value->U_AppResBy == 'HSE' || $value->U_AppResBy == 'GA') {
                $employee = ViewEmployee::where('Nik', $value->EmployeeId)->first();
                if ($employee) {
                    $data = [
                        'header_id' => $header['U_DocEntry'],
                        'detail_id' => $value->U_DocEntry,
                        'date_out' => $header['DocDate'],
                        // 'id_card' => $header['Requester'],
                        'id_card' => $employee->Nik,
                        'employee_name' => $employee->Name,
                        'item_code' => $value->ItemCode,
                        'item_name' => $value->ItemName,
                        'qty' => $value->ReqQty,
                        'uom' => $value->UoMCode,
                        'company' => $employee->Company,
                        'department' => $employee->Department,
                        'notes' => $value->ReqNotes,
                        'created_at' => date('Y-m-d'),
                        'updated_at' => date('Y-m-d'),
                    ];

                    // $brand = SafetyData::where('U_DocEntry', '=', $detail['U_DocEntry'])->first();
                } else {
                    $employee = ViewEmployee::where('Nik', $value->EmployeeId)->first();
                    $data = [
                        'header_id' => $header['U_DocEntry'],
                        'detail_id' => $value->U_DocEntry,
                        'date_out' => $header['DocDate'],
                        'id_card' => $header['Requester'],
                        'employee_name' => $header['RequesterName'],
                        'item_code' => $value->ItemCode,
                        'item_name' => $value->ItemName,
                        'qty' => $value->ReqQty,
                        'uom' => $value->UoMCode,
                        'company' => ($employee) ? $employee->Company : "",
                        'department' => ($employee) ? $employee->Department : "",
                        'notes' => $header['Memo'],
                        'created_at' => date('Y-m-d'),
                        'updated_at' => date('Y-m-d'),
                    ];
                }


                DB::connection('sqlsrv')
                    ->table('safety_data')
                    ->insert($data);
            }
        }
    }

    /**
     * @param $header
     * @param $details
     * @param $request
     * @return array
     */
    public function createSalesOrder($header, $details, $request): array
    {
        // Login To Service Layer
        // dd($this->loginServiceLayer($header['CompanyName']));
        $this->loginServiceLayer($header['CompanyName']);

        $customer = DB::connection('sqlsrv')
            ->table('OCRD')
            ->select('CardName', 'CardCode')
            ->where('CardType', 'C')
            ->where('frozenFor', 'N')
            ->where('CardName', $header['Customer'])
            ->first();

        $doc_date = date('Ymd', strtotime($header['DocDate']));

        // Get Latest DocNum
        // $docNum = $this->getLatestGoodsIssueRequest($header['CompanyName']);
        // Get Current User Login In Service Layer
        // $current_user = $this->getCurrentLoginUser($header['CompanyName'], "RESV");
        // Assign params
        $params_header = [
            //"RequestStatus" => "W", auto
            //"Creator" => "LOG14", auto
            "CardCode" => $customer['CardCode'],
            "DocDate" => $doc_date,
            "DocDueDate" => $header['RequiredDate'],
            "Comments" => $header['Memo'],
        ];

        $param_details = [];
        foreach ($details as $detail) {
            $param_details[] = [
                "ItemCode" => $detail->ItemCode,
                "WhsCode" => $detail->WhsCode,
                "Quantity" => $detail->ReqQty,
            ];
        }

        $array_all = array_merge($params_header, [
            "DocumentLines" => $param_details
        ]);
        // dd($array_all);
        return $this->insertSalesOrder($array_all, $header);
    }


    /**
     * @param $params
     * @param $header
     *
     * @return array
     */
    public function insertSalesOrder($params, $header): array
    {
        $sessionId = $this->session_id;
        $routeId = $this->route_id;
        $headers[] = "Accept: application/json";
        $headers[] = "Content-Type: application/json";
        $headers[] = "Cookie: B1SESSION=" . $sessionId . "; ROUTEID=" . $routeId . ";";

        $curl = curl_init();
        $url = config('app.hana_odbc_url') . ":50000/b1s/v1/Orders";
        // throw new \Exception($url, 1);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($curl, CURLOPT_VERBOSE, 1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        // dd($response);
        $response_text = json_decode($response);

        if (property_exists($response_text, 'error')) {
            if ($response_text->error) {
                throw new \Exception(json_encode($response_text), 1);
            }
        }

        $result = false;
        $result_data_api = [];
        if (!empty($response_text->error->code)) {//if Error
            $message = 'Data Not Saved!';
        } else {
            $message = 'Data Saved Successfully!';
            $result = true;
            $result_data_api = $response_text;
            DB::connection('sqlsrv')
                ->table('resv_headers')
                ->where('U_DocEntry', '=', $header->U_DocEntry)
                ->update([
                    'SAP_SONo' => $response_text->DocEntry
                ]);
        }
        return [
            'result' => $result,
            'message' => $message,
            'result_data_api' => $result_data_api
        ];
    }

    /**
     * @param $db_name
     * @return array|\Illuminate\Http\JsonResponse
     */
    protected function loginServiceLayer($db_name)
    {
        $curl = curl_init();

        $params = [
            "UserName" => "RESV",
            "Password" => "imip#1234",
            "CompanyDB" => $db_name,
        ];

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => config('app.hana_odbc_url') . ':50000/b1s/v1/Login',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($params),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Cookie: CompanyDB=IMIP_LIVE; B1SESSION=fd8a7a2e-1b66-11ec-8000-e4434b27ec88; ROUTEID=.node1'
                ),
            )
        );

        $response = curl_exec($curl);
        $response_text = json_decode($response);


        if (property_exists($response_text, "error")) {
            return response()->json($response_text);
        } else {
            $routeId = ".node1";
            curl_setopt($curl, CURLOPT_HEADERFUNCTION, function ($curl, $string) use (&$routeId) {
                $len = strlen($string);
                if (substr($string, 0, 10) == "Set-Cookie") {
                    preg_match("/ROUTEID=(.+);/", $string, $match);
                    if (count($match) == 2) {
                        $routeId = $match[1];
                    }
                }
                return $len;
            });

            curl_exec($curl);
            curl_close($curl);
            $this->session_id = $response_text->SessionId;
            // $this->session_id = $response_text->SessionId;
            $this->route_id = $routeId;
        }

        $sessionId = $this->session_id;
        $routeId = $this->route_id;
        $headers[] = "Cookie: B1SESSION=" . $sessionId . "; ROUTEID=" . $routeId . ";";
        return $headers;
    }
}
