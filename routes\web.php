<?php

use App\Http\Controllers\AttachmentController;
use App\Mail\ClinicNotification;
use App\Models\Common\Attachment;
use App\Models\Common\SafetyData;
use App\Models\Resv\ReqItem;
use App\Models\Resv\ReservationDetails;
use App\Models\Resv\ReservationHeader;
use App\Models\Resv\ResvDetail;
use App\Models\Resv\ResvHeader;
use App\Models\Resv\ResvReqItem;
use App\Models\Resv\SafetyData as ResvSafetyData;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/test', function () {
    return response()->json("test");
});

Route::get('Attachment/{file}', [AttachmentController::class, 'attachment'])->where('file', '.*');
Route::get('attachment/{file}', [AttachmentController::class, 'attachment'])->where('file', '.*');
Route::get('documents/{file}', [AttachmentController::class, 'documents'])->where('file', '.*');

Route::get('/', function () {
    return view('welcome');
});

Route::get('sync-item-safety', function () {
    DB::beginTransaction();
    $start = now();
    try {
        $items = SafetyData::all();
        foreach ($items as $key => $value) {
            ResvSafetyData::create([
                'date_out' => $value->date_out,
                'item_code' => $value->item_code,
                'uom' => $value->uom,
                'item_name' => $value->item_name,
                'id_card' => $value->id_card,
                'employee_name' => $value->employee_name,
                'company' => $value->company,
                'department' => $value->department,
                'created_by' => $value->created_by,
                'notes' => $value->notes,
                'qty' => $value->qty,
                'header_id' => $value->header_id,
                'detail_id' => $value->detail_id,
            ]);
        }

        DB::commit();
        $end = now();
        return response()->json("process in " . $end->diffForHumans($start));
    } catch (\Exception $th) {
        DB::rollback();
        return response()->json($th->getMessage());
        // Log::info('error', [
        //     'message' => $th->getMessage()
        // ]);
    }
});

Route::get('sync-item-req', function () {
    DB::beginTransaction();
    $start = now();
    try {
        $items = ReqItem::all();
        foreach ($items as $key => $value) {
            ResvReqItem::create([
                'U_Description' => $value->U_Description,
                'U_UoM' => $value->U_UoM,
                'U_Status' => $value->U_Status,
                'U_Remarks' => $value->U_Remarks,
                'U_Supporting' => $value->U_Supporting,
                'U_CreatedBy' => $value->U_CreatedBy,
                'U_Comments' => $value->U_Comments,
                'U_ItemType' => $value->U_ItemType,
                'U_CreatedAt' => substr($value->U_CreatedAt, 0, 19),
            ]);
        }

        DB::commit();
        $end = now();
        return response()->json("process in " . $end->diffForHumans($start));
    } catch (\Exception $th) {
        DB::rollback();
        return response()->json($th->getMessage());
        // Log::info('error', [
        //     'message' => $th->getMessage()
        // ]);
    }
});


Route::get('test-mail', function () {
    $email = new ClinicNotification('TIsna', 'test', 'test');
    Mail::to('<EMAIL>')
        // ->cc($this->cc)
        ->send($email);
});

Route::get('sync-resv-h', function () {
    DB::beginTransaction();
    $start = now();
    try {
        $resv = ReservationHeader::all();
        foreach ($resv as $key => $value) {
            $header = ResvHeader::create([
                'DocNum' => $value->DocNum,
                'DocDate' => $value->DocDate,
                'RequiredDate' => $value->RequiredDate,
                'Requester' => $value->Requester,
                'Division' => $value->Division,
                'Department' => $value->Department,
                'Company' => $value->Company,
                'Memo' => $value->Memo,
                'Canceled' => $value->Canceled,
                'DocStatus' => $value->DocStatus,
                'ApprovalStatus' => $value->ApprovalStatus,
                'ApprovalKey' => $value->ApprovalKey,
                'isConfirmed' => $value->isConfirmed,
                'ConfirmDate' => $value->ConfirmDate,
                'ConfirmBy' => $value->ConfirmBy,
                'SAP_GIRNo' => $value->SAP_GIRNo,
                'SAP_TrfNo' => $value->SAP_TrfNo,
                'SAP_PRNo' => $value->SAP_PRNo,
                'CreateDate' => $value->CreateDate,
                'CreateTime' => $value->CreateTime,
                'CreatedBy' => $value->CreatedBy,
                'UpdateDate' => $value->UpdateDate,
                'UpdateTime' => $value->UpdateTime,
                'UpdatedBy' => $value->UpdatedBy,
                'RequestType' => $value->RequestType,
                'U_NIK' => $value->U_NIK,
                'WhsCode' => $value->WhsCode,
                'WhTo' => $value->WhTo,
                'Token' => $value->Token,
                'CreatedName' => $value->CreatedName,
                'RequesterName' => $value->RequesterName,
                'UrgentReason' => $value->UrgentReason,
                'ItemType' => $value->ItemType,
                'Is_Urgent' => $value->Is_Urgent,
                'U_ATTACH' => $value->U_ATTACH,
                'CategoryType' => $value->CategoryType,
                'UsageFor' => $value->UsageFor,
                'VehicleNo' => $value->VehicleNo,
                'Mileage' => $value->Mileage,
                'Customer' => $value->Customer,
                'SAP_SONo' => $value->SAP_SONo,
                'Replacement' => $value->Replacement,
                'EmployeeType' => $value->EmployeeType,
                'WorkLocation' => $value->WorkLocation,
                'DocumentType' => $value->DocumentType,
                'CostType' => $value->CostType,
                'Usage' => $value->Usage,
                'SAP_GIRNoOld' => $value->SAP_GIRNoOld,
                'SAP_PRNoOld' => $value->SAP_PRNoOld,
            ]);

            if ($header) {
                $details = ReservationDetails::where("U_DocEntry", $value->U_DocEntry)->get();

                foreach ($details as $item) {
                    ResvDetail::create([
                        'U_DocEntry' => $header->U_DocEntry,
                        "LineNum" => $item->LineNum,
                        "ItemCode" => $item->ItemCode,
                        "ItemName" => mb_convert_encoding($item->ItemName, "UTF-8", "UTF-8"),
                        "WhsCode" => $item->WhsCode,
                        "UoMCode" => $item->UoMCode,
                        "UoMName" => $item->UoMName,
                        "ReqQty" => $item->ReqQty,
                        "ReqDate" => $item->ReqDate,
                        "ReqNotes" => mb_convert_encoding($item->ReqNotes, "UTF-8", "UTF-8"),
                        "OtherResvNo" => $item->OtherResvNo,
                        "RequestType" => $item->RequestType,
                        "QtyReadyIssue" => $item->QtyReadyIssue,
                        "LineStatus" => $item->LineStatus,
                        "SAP_GIRNo" => $item->SAP_GIRNo,
                        "SAP_TrfNo" => $item->SAP_TrfNo,
                        "SAP_PRNo" => $item->SAP_PRNo,
                        "ItemCategory" => $item->ItemCategory,
                        "OIGRDocNum" => $item->OIGRDocNum,
                        "InvntItem" => $item->InvntItem,
                        "U_ATTACH" => $item->U_ATTACH,
                        "AssetCode" => $item->AssetCode,
                        "AssetName" => $item->AssetName,
                        "ItemGroup" => $item->ItemGroup,
                        "SubGroup" => $item->SubGroup,
                        "AvailableQty" => $item->AvailableQty,
                        "AvailableQtyDate" => (isset($item->AvailableQtyDate)) ? date('Y-m-d H:i:s', strtotime($item->AvailableQtyDate)) : null,
                        "U_Department" => $item->U_Department,
                        "U_Period" => $item->U_Period,
                        "U_Category" => $item->U_Category,
                        "U_AppResBy" => $item->U_AppResBy,
                        "SAP_SONo" => $item->SAP_SONo,
                        "VehicleNo" => $item->VehicleNo,
                        "Mileage" => $item->Mileage,
                        "EmployeeName" => $item->EmployeeName,
                        "EmployeeId" => $item->EmployeeId,
                        "OrderId" => $item->OrderId,
                        "OnHand" => $item->OnHand,
                        "U_ItemType" => $item->U_ItemType,
                    ]);
                }
            }
        }
        DB::commit();
        $end = now();
        return response()->json("process in " . $end->diffForHumans($start));
    } catch (\Exception $th) {
        DB::rollback();
        return response()->json($th->getMessage());
        // Log::info('error', [
        //     'message' => $th->getMessage()
        // ]);
    }
});

Route::get('sync-attachment', function () {
    set_time_limit(16000); // 300 seconds (5 minutes)
    $connect = odbc_connect(
        'hanab1imipresv',
        'IMIP_ERESV',
        'Ereserve#1234',
        SQL_CUR_USE_ODBC
    );

    $db_name = "IMIP_ERESV_LIVE";
    $sql = '
        select
        B."LineEntry",
        B."LineNum",
        B."U_DocEntry",
        A."DocNum",
        A."CreatedBy"
        from ' . $db_name . '.RESV_H AS A
        left JOIN ' . $db_name . '.RESV_D as B on A."U_DocEntry" = B."U_DocEntry"
        order by A."DocNum" DESC
    ';

    $rs = odbc_exec($connect, $sql);

    if (!$rs) {
        exit("Error in SQL");
    }
    $arr = [];
    while (odbc_fetch_row($rs)) {
        $username = odbc_result($rs, "CreatedBy");
        $attachment = Attachment::where("source_id", odbc_result($rs, "LineEntry"))
            ->where("type", "reservation")
            ->where("created_by", DB::raw("(select id from users where username = '$username')"))
            ->where(DB::raw("convert(varchar, created_at, 23)"), "<", '2024-04-23')
            ->first();

        if ($attachment) {
            $resv = ResvHeader::leftJoin('resv_details', 'resv_details.U_DocEntry', 'resv_headers.U_DocEntry')
                ->where('resv_headers.DocNum', odbc_result($rs, "DocNum"))
                ->where('resv_details.LineNum', odbc_result($rs, "LineNum"))
                ->where('resv_headers.CreatedBy', $username)
                ->select("resv_details.LineEntry")
                ->first();
            if ($resv) {
                $attachment->update([
                    "source_id" => $resv->LineEntry
                ]);
            }
            // $arr[] = [
            //     "LineEntry" => odbc_result($rs, "LineEntry"),
            //     "LineNum" => odbc_result($rs, "LineNum"),
            //     "U_DocEntry" => odbc_result($rs, "U_DocEntry"),
            //     "DocNum" => odbc_result($rs, "DocNum"),
            //     "DocLineEntry" => $resv->LineEntry
            // ];
        }
    }

    return response()->json([
        'count' => count($arr),
        'data' => $arr,
    ]);
});


// Route::get('sync-attachment-item', function () {
//     set_time_limit(16000); // 300 seconds (5 minutes)
//     $connect = odbc_connect(
//         'hanab1imipresv',
//         'IMIP_ERESV',
//         'Ereserve#1234',
//         SQL_CUR_USE_ODBC
//     );

//     $db_name = "IMIP_ERESV_LIVE";
//     $sql = '
//         select
//             A."U_DocEntry"
//         from ' . $db_name . '.U_OITM AS A
//         order by A."U_DocEntry" DESC
//     ';

//     $rs = odbc_exec($connect, $sql);

//     if (!$rs) {
//         exit("Error in SQL");
//     }
//     $arr = [];
//     while (odbc_fetch_row($rs)) {
//         $attachment = Attachment::where("source_id", odbc_result($rs, "U_DocEntry"))
//             ->where("type", "item")
//             ->first();

//         if ($attachment) {
//             $resv = ResvReqItem::where('DocNum', odbc_result($rs, "U_DocEntry"))
//                 ->select("U_DocEntry")
//                 ->first();

//             if ($resv) {
//                 $attachment->update([
//                     "source_id" => $resv->U_DocEntry
//                 ]);
//             }

//             // $arr[] = [
//             //     "LineEntry" => odbc_result($rs, "LineEntry"),
//             //     "LineNum" => odbc_result($rs, "LineNum"),
//             //     "U_DocEntry" => odbc_result($rs, "U_DocEntry"),
//             //     "DocNum" => odbc_result($rs, "DocNum"),
//             //     "DocLineEntry" => $resv->LineEntry
//             // ];
//         }
//     }

//     return response()->json([
//         'count' => count($arr),
//         'data' => $arr,
//     ]);
// });
