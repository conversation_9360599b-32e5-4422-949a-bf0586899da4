<?php

namespace App\Traits;

use App\Models\Approval\ApprovalStage;
use App\Models\Document\Document;

trait DocumentApproval
{
    public function stages($status, $user)
    {
        $stages = ApprovalStage::whereNotNull('document_id')
            ->leftJoin('users', 'users.id', 'approval_stages.user_id')
            ->select('approval_stages.document_id')
            ->where('approval_stages.status', $status)
            ->where('users.username', $user)
            ->distinct()
            ->pluck('document_id');

        $query = Document::select(
            'documents.*',
            'document_number as paper_no',
            'customers.name as customer_name',
            'document_sub_types.name as document_sub_type_name',
            'users.name as user_name'
        )
            ->leftJoin('customers', 'customers.id', 'documents.customer_id')
            ->leftJoin('users', 'users.id', 'documents.created_by')
            ->leftJoin('document_sub_types', 'document_sub_types.id', 'documents.document_sub_type_id')
            ->with(['attachment', 'userCreate'])
            ->whereIn('documents.id', $stages)
            ->orderBy('documents.document_number', 'desc');

        $total = $query->count();

        $data = $query->get();

        return [
            'total' => $total,
            'data' => $data
        ];
    }
}
