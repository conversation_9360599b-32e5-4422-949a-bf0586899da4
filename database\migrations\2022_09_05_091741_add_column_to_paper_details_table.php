<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnToPaperDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('paper_details', function (Blueprint $table) {
            $table->string('status', 50)->nullable();
            $table->string('room_no', 50)->nullable();
            $table->string('alternative_room_no', 50)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('paper_details', function (Blueprint $table) {
            //
        });
    }
}
