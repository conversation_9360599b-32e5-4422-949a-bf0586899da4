<?php

namespace App\Http\Controllers;

use App\Models\Resv\ReservationDetails;
use App\Models\Resv\ReservationHeader;
use App\Services\ProcessPostSapS4Service;
use App\Services\SapS4Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MigrateSapDataController extends Controller
{
    // step 1 query result open and move sap GIR NO to GIR NO OLD and PR No to PR No OLD
    // step 2 uncommend for select gir is null and work location not jkt
    // step 3 hit update

    public function store(Request $request)
    {
        $date_from = $request->dateFrom;
        $date_to = $request->dateTo;


        $rows = $this->query($date_from, $date_to);

        // foreach ($rows as $row) {
        //     $dataHeader = ReservationHeader::where("U_DocEntry", "=", $row->U_DocEntry)->first();

        //     if (!$dataHeader->SAP_PRNoOld) {
        //         DB::connection('sqlsrv')
        //             ->table('resv_headers')
        //             ->where('U_DocEntry', '=', $dataHeader->U_DocEntry)
        //             ->update([
        //                 'SAP_PRNoOld' => $dataHeader->SAP_PRNo
        //             ]);

        //         DB::connection('sqlsrv')
        //             ->table('resv_headers')
        //             ->where('U_DocEntry', '=', $dataHeader->U_DocEntry)
        //             ->update([
        //                 'SAP_PRNo' => null
        //             ]);
        //     }

        //     if (!$dataHeader->SAP_GIRNoOld) {
        //         DB::connection('sqlsrv')
        //             ->table('resv_headers')
        //             ->where('U_DocEntry', '=', $dataHeader->U_DocEntry)
        //             ->update([
        //                 'SAP_GIRNoOld' => $dataHeader->SAP_GIRNo
        //             ]);

        //         DB::connection('sqlsrv')
        //             ->table('resv_headers')
        //             ->where('U_DocEntry', '=', $dataHeader->U_DocEntry)
        //             ->update([
        //                 'SAP_GIRNo' => null
        //             ]);
        //     }

        // }

        return response()->json([
            "count" => count($rows),
            'data' => $rows,
        ]);
    }

    // step 3
    public function update(Request $request)
    {
        $date_from = $request->dateFrom;
        $date_to = $request->dateTo;

        $rows = $this->query($date_from, $date_to);
        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();

        // initialize post service
        $postService = new ProcessPostSapS4Service();
        $response = [];
        foreach ($rows as $row) {
            $dataHeader = ReservationHeader::where("U_DocEntry", "=", $row->U_DocEntry)->first();
            $dataDetails = ReservationDetails::where("U_DocEntry", "=", $dataHeader->U_DocEntry)
                ->get();

            if (!$dataHeader->SAP_GIRNo || !$dataHeader->SAP_PRNo) {
                $response[] = $postService->store($dataHeader, $dataDetails);
            }
        }
        return $this->success([
            'response' => $response
        ], 'Success');
        try {
            //code...
        } catch (\Exception $th) {
            //throw $th;
            return $this->error($th->getMessage(), 422, [
                'trace' => json_decode($th->getMessage())
            ]);
        }
    }

    protected function query2($date_from, $date_to)
    {
        $schema = (config('app.db_schema') !== null) ? config('app.db_schema') : 'IMIP_ERESV_LIVE';
        $db_name = config('app.db_sap');

        $workLocation = 'IMIP JAKARTA';

        $rows = ReservationHeader::select(
            // "resv_headers.DocNum",
            // "resv_headers.DocDate",
            // "resv_headers.WhsCode",
            "resv_headers.WorkLocation",
            "resv_headers.Division",
            // DB::raw('resv_headers."RequestType" as "HeaderRequestType"'),
            // "resv_headers.ItemType",
            "resv_details.ItemCode",
            "resv_headers.WhsCode",
            // "resv_details.ItemName",
            // "resv_details.AssetCode",
            // "resv_details.AssetName",
            // "resv_details.UoMCode",
            // "resv_details.ReqQty",
            // "resv_details.ReqNotes",
            // "resv_details.RequestType",
            // "resv_headers.RequesterName",
            // "resv_headers.U_DocEntry"
        )
            ->leftJoin("resv_details", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            ->whereRaw("resv_headers.\"DocDate\" BETWEEN '${date_from}' AND '${date_to}' ")
            // ->whereRaw("resv_details.\"RequestType\" LIKE '%${request_type}%' ")
            // ->whereIn("ItemCode", [
            //     '645010006',
            //     '661010093',
            //     '661010134',
            //     '661010138',
            //     '661010170',
            //     '661010171',
            //     '661010224',
            //     '661010254',
            //     '661010306',
            //     '661010338',
            //     '661010463',
            //     '661010468',
            //     '661030059',
            //     '661030280',
            //     '661040001',
            //     '661040042',
            //     '661040043',
            //     '661030503',
            //     '661040085',
            //     '661040086',
            //     '662020154',
            // ])
            ->whereNotIn("ItemType", ['Service', 'Asset'])
            ->where('resv_headers.RequestType', '<>', 'Restock')
            ->whereRaw("resv_headers.\"WorkLocation\" <> '${workLocation}'")
            ->whereRaw("resv_headers.\"SAP_GIRNo\" is null")
            ->whereRaw('
            (
                SELECT G0."Status"
                    FROM ' . $db_name . '."ERESV_FLOW_DOC" G0
                    WHERE G0."ERESV_DOCNUM" = resv_headers."DocNum"

            ) = \'Open\'

        ')
            // ->orderBy("resv_headers.DocNum")
            ->distinct()
            ->get();

        return $rows;
    }

    protected function query($date_from, $date_to)
    {
        $schema = (config('app.db_schema') !== null) ? config('app.db_schema') : 'IMIP_ERESV_LIVE';
        $db_name = config('app.db_sap');

        $workLocation = 'IMIP JAKARTA';

        $apiResponse = DB::connection('sqlsrv')
            ->table('api_responses')
            ->where('message', '=', 'Success')
            ->select('reservation_number')
            ->distinct()
            ->pluck('reservation_number');

        $rows = ReservationHeader::select(
            "resv_headers.DocNum",
            "resv_headers.DocDate",
            "resv_headers.WhsCode",
            DB::raw('resv_headers."RequestType" as "HeaderRequestType"'),
            "resv_headers.ItemType",
            // "resv_details.ItemCode",
            // "resv_details.ItemName",
            // "resv_details.AssetCode",
            // "resv_details.AssetName",
            // "resv_details.UoMCode",
            // "resv_details.ReqQty",
            // "resv_details.ReqNotes",
            // "resv_details.RequestType",
            "resv_headers.RequesterName",
            "resv_headers.U_DocEntry"
        )
            // ->leftJoin("resv_details", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            ->whereRaw("resv_headers.\"DocDate\" BETWEEN '${date_from}' AND '${date_to}' ")
            // ->whereNotIn("resv_headers.ItemType", ['Asset', 'Service'])
            // ->where('resv_headers.RequestType', '<>', 'Restock')
            // ->whereRaw("resv_details.\"RequestType\" LIKE '%${request_type}%' ")
            // ->whereIn("ItemCode", [
            //     '645010006',
            //     '661010093',
            //     '661010134',
            //     '661010138',
            //     '661010170',
            //     '661010171',
            //     '661010224',
            //     '661010254',
            //     '661010306',
            //     '661010338',
            //     '661010463',
            //     '661010468',
            //     '661030059',
            //     '661030280',
            //     '661040001',
            //     '661040042',
            //     '661040043',
            //     '661030503',
            //     '661040085',
            //     '661040086',
            //     '662020154',
            // ])
            //  step 2 uncomment this 2 line below
            // ->whereRaw("resv_headers.\"WorkLocation\" <> '${workLocation}'")
            // ->whereRaw("resv_headers.\"SAP_GIRNo\" is null")
            // ->whereNotIn("resv_headers.DocNum", $apiResponse)
            ->whereRaw('
            (
                SELECT G0."Status"
                    FROM ' . $db_name . '."ERESV_FLOW_DOC" G0
                    WHERE G0."ERESV_DOCNUM" = resv_headers."DocNum"

            ) = \'Open\'

        ')->orderBy("resv_headers.DocNum")
            ->distinct()
            ->get();

        return $rows;
    }
}
