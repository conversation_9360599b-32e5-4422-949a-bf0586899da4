<?php

namespace App\Traits;

use App\Models\Master\Config;

trait AppConfig
{
    /**
     * @param $name
     * @param string $type
     * @return mixed
     */
    public function getConfigByName($name, string $type = 'PERURI', string $company = 'PT IMIP')
    {
        $config = Config::where('key', $name)
            ->where('type', $type)
            ->where('company', $company)
            ->first();

        return $config->value;
    }

    /**
     * @param $key
     * @param $value
     * @param $type
     * @return void
     */
    public function storeConfig($key, $value, $type)
    {
        Config::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
            ]
        );
    }

    public function getBase64OriginalLength($base64String)
    {
        // Remove any padding characters
        $base64String = rtrim($base64String, '=');

        // Calculate the length of the base64 string
        $base64Length = strlen($base64String);

        // Calculate the original length of the data
        $originalLength = ($base64Length * 3) / 4;

        return floor($originalLength);
    }
}
