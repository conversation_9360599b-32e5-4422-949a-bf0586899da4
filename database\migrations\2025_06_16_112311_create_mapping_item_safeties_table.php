<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mapping_item_safeties', function (Blueprint $table) {
            $table->id();
            $table->string('item_code', 50);
            $table->string('item_name', 240);
            $table->string('item_type', 50);
            $table->string('whs_code', 50);
            $table->string('uom', 50);
            $table->string('item_group', 50);
            $table->string('category_apd', 50);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mapping_item_safeties');
    }
};
