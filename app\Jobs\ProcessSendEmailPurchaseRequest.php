<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Mail\SendEmailPurchaseRequest;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use App\Services\ReservationService;
use Illuminate\Support\Facades\DB;
use App\Models\Resv\ResvDetail;

class ProcessSendEmailPurchaseRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $content;
    protected $receiver_name;
    protected $cc;
    protected $receiver = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($content, $receiver, $receiver_name, $cc)
    {
        $this->content = $content;
        $this->receiver = $receiver;
        $this->cc = $cc;
        $this->receiver_name = $receiver_name;

        $this->onQueue('documentPurchase');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        sleep(5);
        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');
        Log::info('process queue pR', [
            'receiver' =>  array_combine($this->receiver, $this->receiver),
            'cc' =>  array_combine($this->cc, $this->cc),
            'receiver_name' =>  $this->receiver_name,
            'content' => $this->content
        ]);

        $service = new ReservationService();
        $attachment = public_path($service->printDocument($this->content['header'], 'all', $this->content['header']->Requester, 'queue'));

        $attachments = [];

        $details = ResvDetail::where("U_DocEntry", "=", $this->content['header']->U_DocEntry)->get();
        foreach ($details as $key => $value) {
            $att = DB::connection('sqlsrv')
                ->table('attachments')
                ->where('source_id', '=', $value->LineEntry)
                ->where('type', '=', 'reservation')
                ->first();
                
            create_file_delete_job('/Attachment/docs/' . $att->file_name);

            // if ((filesize(public_path('/Attachment/docs/' . $att->file_name)) / 1024) < 2) {
            //     throw new \Exception('Failed get attachment for detail reservation', 1);
            // }

            $attachments[] = public_path('/Attachment/docs/' . $att->file_name);
        }
        $attachments[] = $attachment;

        $content = array_merge([
            'attachment' => $attachments
        ], $this->content);

        $email = new SendEmailPurchaseRequest($this->receiver_name, $content);
        Mail::to($this->receiver)
            ->cc(array_filter($this->cc, function ($value) {
                return $value !== null;
            }))
            ->send($email);
    }
}
