<?php

namespace App\Http\Controllers\Approval;

use App\Http\Controllers\Controller;
use App\Models\View\ViewEmployee;
use App\Traits\Approval;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class SapApprovalController extends Controller
{
    use Approval;

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvalFormPenolakan(Request $request)
    {
        try {
            // $header = $request->header;
            $items = $request->items;

            // $authUser = Http::post(config('app.cherry_service_token'), [
            //     'CommandName' => 'RequestToken',
            //     'ModelCode' => 'AppUserAccount',
            //     'UserName' => '********',
            //     'Password' => '1q2w3e4r5t6y',
            //     'ParameterData' => [],
            // ]);

            // $requester = $request->Requester;
            $requester = '********';
            $PurchaserCode = $request->PurchaserCode;
            $CostCenter = $request->CostCenter;
            $Type = $request->Type;
            $PurchaserName = $request->PurchaserName;
            $DocEntry = $request->DocEntry;
            $DocNum = $request->DocNum;
            $DocDate = $request->DocDate;
            $PONum = $request->PONum;
            $Reason = $request->Reason;

            // $userRequester = User::where('username', $requester)->first();
            $dataRequester = ViewEmployee::where('NIK', $requester)->first();
            if (!$dataRequester) {
                return $this->error('Requester cannot empty and must be employee ID CARD!');
            }


            $cherry_token = "dY_oF_LcCUOBjimMfA6jjA==";
            // $cherry_token = $authUser->collect()['Data']['Token'];
            $list_code = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'ExternalDocuments',
                'UserName' => $requester,
                'Token' => $cherry_token,
                'ParameterData' => [],
            ]);
            $userPurchaser = $PurchaserCode;

            $reservation_code = '';
            //return response()->json($list_code->collect()['Data'] );
            foreach ($list_code->collect()['Data'] as $datum) {
                if (Str::contains($datum['Name'], $userPurchaser)) {
                    $reservation_code = $datum['Code'];
                }
            }

            $username = $requester;
            // $company_code = $userRequester->company_code;
            $company_code = $dataRequester->CompanyCode;
            $employee_code = $dataRequester->EmployeeCode;


            // return $this->error('', 422,  [$company_code]);
            $dataPurchaser = ViewEmployee::where('NIK', $userPurchaser)->first();
            $document_content = view('email.approval_sap', [
                // 'header' => $header,
                'PurchaserCode' => $PurchaserCode,
                'CostCenter' => $CostCenter,
                'Type' => $Type,
                'PurchaserName' => $PurchaserName,
                'DocEntry' => $DocEntry,
                'DocNum' => $DocNum,
                'DocDate' => $DocDate,
                'PONum' => $PONum,
                'Reason' => $Reason,
                'items' => $items,
                'dataPurchaser' => $dataPurchaser
            ])->render();

            $response = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'Submit',
                'ModelCode' => 'GADocuments',
                'UserName' => $username,
                'Token' => $cherry_token,
                'ParameterData' => [],
                'ModelData' => [
                    'TypeCode' => $reservation_code,
                    'CompanyCode' => $company_code,
                    'Date' => date('m/d/Y'),
                    'EmployeeCode' => $employee_code,
                    'DocumentReferenceID' => $DocEntry,
                    'CallBackAccessToken' => config('app.access_token_1'),
                    'DocumentContent' => $document_content,
                    'Notes' => $Reason
                ]
            ]);

            if ($response['MessageType'] == 'error') {
                return $this->error($response->collect()['Message']);
            }
            return $this->success([], $response->collect()['Message']);
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 422, [
                'trace' => $e->getTrace()
            ]);
        }
    }

    /**
     * @param Request $request
     * @return array|\Illuminate\Http\JsonResponse
     */
    public function callback(Request $request)
    {
        try {
            $document_id = $request->DocumentReferenceID;
            $status = $request->StatusId;
            // $note = $this->getRemark($document_id);
            // $note = null;

            $cherry_token = "r93MUOungkmMUPw04SUyJA==";


            if ($status == 'Approved') {
                // $this->loginServiceLayer(config('app.db_sap'));

                $params = [
                    "Status" => 'C',
                    "DocEntry" => $document_id,
                    // "U_Remark" => $note,
                ];

                // $this->closeFormPenolakan($params, $document_id);
                sleep(10);
                $responseDocument = Http::post(config('app.cherry_service_req'), [
                    // $responseDocument = Http::post('http://testing.hris.imip.co.id/api/common/servicerequest', [
                    'CommandName' => 'GetList',
                    'ModelCode' => 'ApprovalRequests',
                    'UserName' => '********',
                    'Token' => $cherry_token,
                    'OrderBy' => 'InsertStamp',
                    'OrderDirection ' => 'desc',
                    'ParameterData' => [
                        [
                            'ParamKey' => 'ModelEntityCode',
                            'ParamValue' => $request->Code,
                            'Operator' => 'eq'
                        ],
                    ]
                ]);

                $note = $responseDocument->collect()['Data'][0]['ApprovalNotes'];

                $params = [
                    "Status" => 'C',
                    "DocEntry" => $document_id,
                    "U_Remark" => $note,
                ];
                return $this->success([
                    'note' => $responseDocument->collect()['Data'][0]['ApprovalNotes'],
                    "DocEntry" => $document_id,
                    'RESPONSEGADOCUMENT' => $request->all(),
                    'RESPONSEAPPROVAL' => $responseDocument->collect()['Data']
                ]);

                return $this->updateFormPenolakan($params, $document_id);
            // return $this->success([], 'Document approved');
            } else {
                return $this->success([], 'Document rejected');
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 422, [
                'trace' => $e->getTrace()
            ]);
        }
    }
}
