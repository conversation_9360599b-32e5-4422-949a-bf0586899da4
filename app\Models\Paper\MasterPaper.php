<?php

namespace App\Models\Paper;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Paper\MasterPaper
 *
 * @property int $id
 * @property string $name
 * @property string $alias
 * @property string|null $background
 * @property string $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper whereAlias($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper whereBackground($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterPaper whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterPaper extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
