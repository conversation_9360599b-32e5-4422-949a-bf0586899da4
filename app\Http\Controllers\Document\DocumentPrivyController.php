<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Models\Document\Document;
use App\Services\ApprovalPrivyService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Approval\BatchApprovalController;
use Illuminate\Http\Request;

class DocumentPrivyController extends Controller
{
    public function index(Request $request)
    {
        $bacthApprovalController = new BatchApprovalController();
        $id = Document::whereBetween('reference_number', ['ESIGN0677BDMINVJII25', 'ESIGN1053BDMINVJII25'])
            ->whereRaw("year(document_date)=2025")
            ->where("privy_status", "=", "completed")
            ->where("external_document_number", "LIKE", "%BDM%")
            ->select("id")->get()->pluck('id');

        foreach ($id as $key => $value) {
            // $this->show($value);
            $bacthApprovalController->reProcessBatch($request, $value);
        }

        return response()->json([
            "response" => "ok"
        ]);
    }

    public function show($id)
    {
        $row = Document::find($id);
        $service = new ApprovalPrivyService();
        $service->login();

        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('PrivyCheckDocumentRegister', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');
        $includeTokenInFileName = $this->getConfigByName('IncludeTokenInFileName', 'GENERAL');
        $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $this->signature($row, $timestamp, 'POST'),
                'Request-ID' => strtotime($timestamp),
            ])
            ->post($url, $this->params($row))
            ->throw(function ($response, $e) {
                throw new \Exception('E-DIGITAL SIGN: ' . json_decode($response->collect()), 1);
            });


        // Log::info('params check status', [
        //     'params' => $this->params($row),
        //     'url' => $url,
        //     'response' => $response->collect()->all()
        // ]);

        $row->privy_status = $response->collect()['data']['status'];

        info("check status privy " . $row->external_document_number, [
            "reference_number" => $row->reference_number,
            // "status" => $status,
            // "file length" => ($response['data']['signed_document']) ? $this->getBase64OriginalLength($response['data']['signed_document']) : 0,
        ]);

        if ($response->collect()['data']['status'] == 'completed') {
            $contents = base64_decode(str_replace('data:application/pdf;base64,', '', $response['data']['signed_document']));
            $file_name = $row->attachment->file_name;
            if ($includeTokenInFileName == 'Y' && Str::contains($row->external_document_number, ['IMIP/DN'])) {
                $file_name = strtoupper(Str::slug(str_replace('/', '-', $row->external_document_number))) . '.pdf';
            } else {
                $file_name = (Str::contains($file_name, '_token_')) ? substr($file_name, 0, -19) . '.pdf' : $file_name;
            }
            // Log::info('file name check :' . $file_name);
            // $path_download = public_path('documents/' . $file_name);
            // file_put_contents($path_download, $contents);

            custom_disk_put('documents/' . $file_name, $contents);
            $row->status = 'approved - finish';
        } else {
            $row->status = 'approved - ' . $response->collect()['data']['status'];
        }
        $row->save();


        return $this->success($response->collect());
    }

    public function params($document)
    {
        $channelId = $this->getConfigByName('PrivyChannelId', 'PRIVY', $document->company);
        return [
            "reference_number" => $document->reference_number,
            "channel_id" => $channelId,
            "document_token" => $document->document_token,
            "info" => ""
        ];
    }

    protected function signature($row, $timestamp, $httpVerb)
    {
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $body = $this->params($row);

        $strBody = json_encode($body);
        $strBody = str_replace(" ", "", $strBody);
        $method = $httpVerb;
        $body_md5 = base64_encode(md5($strBody, true));
        $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        $byte_key = utf8_encode($api_secret);
        $new_hmac = utf8_encode($hmac_signature);
        $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        $base64_message = base64_encode($hmac);
        $auth_string = $api_key . ":" . $base64_message;
        $signature = base64_encode($auth_string);
        return $signature;
    }
}
