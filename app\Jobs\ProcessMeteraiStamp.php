<?php

namespace App\Jobs;

use App\Models\Document\Document;
use App\Models\Document\DocumentCoordinate;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use App\Traits\DocumentHelper;
use App\Traits\MeteraiStampHelper;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessMeteraiStamp implements ShouldQueue
{
    public $timeout = 0;
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    use AppConfig;
    use ApiResponse;
    use DocumentHelper;
    use MeteraiStampHelper;

    protected $document;
    protected $fileName;
    protected $userId;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($document, $fileName, $userId)
    {
        $this->document = $document;
        $this->fileName = $fileName;
        $this->userId = $userId;
        $this->onQueue('processDocument');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        sleep(5);
        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');

        $document = $this->document;
        $document_id = $document->id;
        $fileName = $this->fileName;
        if ($document->sign_payment == '2') {
            if ($document->meterai_coordinate == 'Y') {
                $this->process($document, $document, 'ref_token', $fileName, 1);
            }
        } else if ($document->sign_payment == '3') {
            $coordinates = $document->coordinate;
            $count = $document->coordinate()->whereNotNull('serial_number')->count();
            foreach ($coordinates as $item) {
                $coordinate = DocumentCoordinate::find($item->id);
                Log::info('from process meterai stamp, stamp info', [
                    'serial' => $coordinate->meterai_coordinate,
                ]);
                $this->process($document, $coordinate, 'serial_number', $fileName, $count);
            }
        }
    }
    /**
     * Summary of process
     * @param mixed $document
     * @param mixed $serialTable
     * @param mixed $snColumn
     * @throws \Exception
     * @return void
     */
    public function process($document, $serialTable, $snColumn, $fileName, $count)
    {
        if ($serialTable->meterai_coordinate == 'Y') {
            $dataCheck = [
                'document_id' => $document->id,
                'name' => 'Stamp Meterai',
            ];
            try {
                $this->stamp($document, $dataCheck, $snColumn, $serialTable, $this->batch(), $fileName, $count);
            } catch (\Exception $exception) {
                $dataUpdate = [
                    'batch_id' => $this->batch()->id,
                    'status' => 'Processing ' . $this->batch()->progress() . '%',
                    'callback_message' => 'Failed stamp document: ' . $exception->getMessage(),
                    'callback_trace' => ''
                ];
                $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                $this->fail($exception);
                throw new \Exception('E-METERAI: ' . $exception->getMessage(), 1);
            }
        }
    }

    public function makeDocumentApproveFailed($id)
    {
        $document = Document::find($id);
        $document->status = 'approved - failed';
        $document->save();
    }
}
