<?php

namespace App\Traits;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

trait DigitalSignHelper
{
    use ApiResponse;

    public function getPdfFile() {}
    /**
     * Summary of digitalSign
     * @param mixed $document
     * @param mixed $fileName
     * @param mixed $dataCheck
     * @param mixed $serialTable
     * @param mixed $batch
     * @throws \Exception
     * @return void
     */
    public function digitalSign($document, $fileName, $dataCheck, $batch)
    {
        $reportAttachment = '/report/documents/' . $fileName->file_name;
        $docAttachment = '/Attachment/docs/' . $fileName->file_name;
        if ($document->sign_payment == '2') {
            if ($document->meterai_coordinate == 'Y') {
                create_file_delete_job($reportAttachment);
                $pdf_file = public_path($reportAttachment);
            } else {
                create_file_delete_job($docAttachment);
                $pdf_file = public_path($docAttachment);
            }
        } else if ($document->sign_payment == '3') {
            $coordinates = $document->coordinate()->pluck('meterai_coordinate');

            Log::info('from digital sign helper coordinate', [
                'coordinates' => $coordinates,
            ]);

            $collection = collect($coordinates);

            $check = $collection->contains(function ($value, $key) {
                return $value == 'Y';
            });

            if ($check) {
                create_file_delete_job($reportAttachment);
                $pdf_file = public_path($reportAttachment);
            } else {
                create_file_delete_job($docAttachment);
                $pdf_file = public_path($docAttachment);
            }
        }

        Log::info('from digital sign helper pdf_file', [
            'pdf_file' => $pdf_file,
        ]);

        if (!file_exists($pdf_file)) {
            throw new \Exception('E-DIGITALSIGN: ' . $pdf_file . ' not exist', 1);
        }

        $esign_user_id = $this->getConfigByName('UserIdEsign', 'ESIGN', $document->company);
        if (date('Y-m-d', strtotime($document->created_at)) < '2022-06-27') {
            $doc_id = Str::slug(strtoupper($document->document_number));
        } else {
            $doc_id = Str::slug(strtoupper($document->external_document_number));
        }
        $responseSubmitDigisign = $this->submitSign($pdf_file, $fileName, $document, $doc_id, $esign_user_id);
        // end submit digisign

        Log::info('from digital sign helper submitSign', [
            'response' => $responseSubmitDigisign->collect(),
        ]);
        if ($responseSubmitDigisign->failed()) {
            $dataUpdate = [
                'batch_id' => $batch->id,
                'status' => 'Processing ' . $batch->progress() . '%',
                'callback_message' => 'E-DIGITALSIGN: ',
                'callback_trace' => json_encode($responseSubmitDigisign->collect())
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            throw new \Exception('E-DIGITALSIGN: failed', 1);
        }

        Log::info('from digital sign helper digitalSign', [
            'JSONFile' => $responseSubmitDigisign->collect()['JSONFile'],
        ]);

        if ($responseSubmitDigisign->collect()['JSONFile']['result'] != '00') {
            // throw new \Exception(json_encode($responseSubmitDigisign->collect()['JSONFile']), 1);
            $this->errorSubmitSign($responseSubmitDigisign, $doc_id, $dataCheck, $batch);
        } else {
            $this->successSubmitSign($batch, $doc_id, $dataCheck, $document, $esign_user_id);
        }
        // event(new BatchProcessEvent($document->created_by, $document->id));
    }
    /**
     * Summary of successSubmitSign
     * @param mixed $batch
     * @param mixed $doc_id
     * @param mixed $dataCheck
     * @param mixed $document
     * @param mixed $esign_user_id
     * @return void
     */
    public function successSubmitSign($batch, $doc_id, $dataCheck, $document, $esign_user_id)
    {
        $dataUpdate = [
            'batch_id' => $batch->id,
            'status' => 'Processing ' . $batch->progress() . '%',
            'callback_message' => 'Success document_id: ' . $doc_id,
            'callback_trace' => 'Success Submit Digital Sign'
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);

        $this->createLog(
            $document->id,
            'document',
            'Digisign - Submit autosign document id: ' . $doc_id . ', userid: ' . $esign_user_id,
            $this->userId
        );
        return;
    }
    /**
     * Summary of errorSubmitSign
     * @param mixed $responseSubmitDigisign
     * @param mixed $doc_id
     * @param mixed $dataCheck
     * @throws \Exception
     * @return void
     */
    public function errorSubmitSign($responseSubmitDigisign, $doc_id, $dataCheck, $batch)
    {
        if ($responseSubmitDigisign->collect()['JSONFile']['notif'] != 'Document_id sudah digunakan') {
            $dataUpdate = [
                'batch_id' => $batch->id,
                'status' => 'Processing ' . $batch->progress() . '%',
                'callback_message' => 'Error document_id: ' . $doc_id . ' '
                    . $responseSubmitDigisign->collect()['JSONFile']['notif'],
                'callback_trace' => $responseSubmitDigisign->collect()['JSONFile']['notif']
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            throw new \Exception('E-DIGITALSIGN: ' . $responseSubmitDigisign->collect()['JSONFile']['notif'], 1);
        } else {
            $dataUpdate = [
                'batch_id' => $batch->id,
                'status' => 'Processing ' . $batch->progress() . '%',
                'callback_message' => 'Success document_id: ' . $doc_id,
                'callback_trace' => 'Success Submit Digital Sign'
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        }
    }
    /**
     * Summary of submitSign
     * @param mixed $pdf_file
     * @param mixed $fileName
     * @param mixed $serialTable
     * @param mixed $doc_id
     * @param mixed $esign_user_id
     * @return \Illuminate\Http\Client\Response
     */
    public function submitSign($pdf_file, $fileName, $document, $doc_id, $esign_user_id)
    {
        $url = $this->getConfigByName('SendDocument', 'ESIGN');
        $esign_user_name = $this->getConfigByName('UserNameEsign', 'ESIGN', $document->company);
        $esign_user_email = $this->getConfigByName('UserEmailEsign', 'ESIGN', $document->company);
        $esign_key_user = $this->getConfigByName('KeyUserEsign', 'ESIGN', $document->company);
        $token = $this->getConfigByName('TokenEsign', 'ESIGN', $document->company);

        Log::info('params esign new: ', [
            'params' => [
                'jsonfield' => json_encode([
                    'JSONFile' => [
                        'userid' => $esign_user_id,
                        'document_id' => $doc_id,
                        'payment' => $document->sign_payment,
                        'redirect' => true,
                        'send-to' => [
                            [
                                'name' => $esign_user_name,
                                'email' => $esign_user_email,
                            ],
                        ],
                        'req-sign' => $this->getRequestSign($document, $esign_user_name, $esign_user_email, $esign_key_user)
                    ],
                ])
            ],
            'pdf_file' => $pdf_file,
            'document_number' => $document->document_number
        ]);

        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');

        return Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->attach(
                'file',
                file_get_contents($pdf_file),
                $fileName->file_name
            )
            ->post($url, [
                'jsonfield' => json_encode([
                    'JSONFile' => [
                        'userid' => $esign_user_id,
                        'document_id' => $doc_id,
                        'payment' => $document->sign_payment,
                        'redirect' => true,
                        'send-to' => [
                            [
                                'name' => $esign_user_name,
                                'email' => $esign_user_email,
                            ],
                        ],
                        'req-sign' => $this->getRequestSign($document, $esign_user_name, $esign_user_email, $esign_key_user)
                    ],
                ]),
            ]);
    }
    /**
     * Summary of getRequestSign
     * @param mixed $document
     * @param mixed $esign_user_name
     * @param mixed $esign_user_email
     * @param mixed $esign_key_user
     * @return array<array>
     */
    public function getRequestSign($document, $esign_user_name, $esign_user_email, $esign_key_user)
    {
        if ($document->sign_payment == '2') {
            $vis_digisign_lly = (!empty($document->vis_digisign_lly)) ? $document->vis_digisign_lly : ($document->coordinate()->first()->vis_digisign_lly + 45);
            $vis_digisign_urx = (!empty($document->vis_digisign_urx)) ? $document->vis_digisign_urx : $document->coordinate()->first()->vis_digisign_urx;
            $vis_digisign_ury = (!empty($document->vis_digisign_ury)) ? $document->vis_digisign_ury : ($document->coordinate()->first()->vis_digisign_ury + 45);
            return [
                [
                    'name' => $esign_user_name,
                    'email' => $esign_user_email,
                    'aksi_ttd' => 'at',
                    'kuser' => $esign_key_user,
                    'user' => 'ttd1',
                    'page' => $document->sign_page,
                    'llx' => $document->vis_digisign_llx,
                    'lly' => ($document->meterai_coordinate == 'Y') ? $vis_digisign_lly : $vis_digisign_lly + 30,
                    'urx' => $vis_digisign_urx,
                    'ury' => ($document->meterai_coordinate == 'Y') ? $vis_digisign_ury : $vis_digisign_ury + 30,
                    'visible' => '1',
                ]
            ];
        } else {
            $coordinates = $document->coordinate;
            $req = [];
            foreach ($coordinates as $key => $coordinate) {
                $req[] = [
                    'name' => $esign_user_name,
                    'email' => $esign_user_email,
                    'aksi_ttd' => 'at',
                    'kuser' => $esign_key_user,
                    'user' => 'ttd' . ($key +  1),
                    'page' => $coordinate->sign_page,
                    'llx' => ($coordinate->meterai_coordinate == 'Y') ? $coordinate->vis_digisign_llx + 50 : $coordinate->vis_digisign_llx + 20,
                    'lly' => ($coordinate->meterai_coordinate == 'Y') ? $coordinate->vis_digisign_lly + 40 : $coordinate->vis_digisign_lly + 30,
                    'urx' => ($coordinate->meterai_coordinate == 'Y') ? $coordinate->vis_digisign_urx + 50 : $coordinate->vis_digisign_urx + 20,
                    'ury' => ($coordinate->meterai_coordinate == 'Y') ? $coordinate->vis_digisign_ury + 40 : $coordinate->vis_digisign_ury + 30,
                    'visible' => '1',
                ];
            }
            return $req;
        }
    }
}
