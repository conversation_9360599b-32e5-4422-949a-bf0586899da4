<?xml version="1.0" encoding="utf-8" ?>
<service>
    <id>laravel-backendcore-queue-document-sign</id>
    <name>Laravel Backendcore Queue Document Sign</name>
    <description>This service runs Laravel Queue.</description>
    <executable>D:\laragon-new\bin\php\php-8.1.10-Win32-vs16-x64\php.exe</executable>
    <arguments>D:\laragon-new\www\backendcore\artisan queue:work --tries=2 --memory=512 --timeout=7000  --daemon --queue=ticketMail,ticketFinish</arguments>
    <priority>RealTime</priority>
    <log mode="roll"></log>
    <startmode>Automatic</startmode>
</service>
