<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddExternalDocumentNumberToDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->string('customer_name')->nullable();
            $table->string('external_document_number')->nullable();
            $table->string('digisign_coordinate', 10)->nullable()->default('N');
            $table->string('digisign_top')->nullable();
            $table->string('digisign_left')->nullable();
            $table->string('digisign_width')->nullable();
            $table->string('digisign_height')->nullable();
            $table->string('meterai_coordinate', 10)->nullable()->default('N');
            $table->string('meterai_top')->nullable();
            $table->string('meterai_left')->nullable();
            $table->string('meterai_width')->nullable();
            $table->string('meterai_height')->nullable();
            $table->string('coordinate_document_path')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('documents', function (Blueprint $table) {
            //
        });
    }
}
