<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Models\Master\MappingItemSafety;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MappingItemSafetyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = MappingItemSafety::select(
            "id",
            "item_code",
            "item_name",
            "item_type",
            "whs_code",
            "uom",
            "item_group",
            "category_apd",
        )
            ->orderBy('item_name', 'desc')
            ->get();

        if (count($data) < 1) {
            $data = [
                [
                    "id" => null,
                    "item_code" => null,
                    "item_name" => null,
                    "item_type" => null,
                    "whs_code" => null,
                    "uom" => null,
                    "item_group" => null,
                    "category_apd" => null,
                ]
            ];
        }

        return $this->success([
            'rows' => $data,
            'columns' => [
                [
                    'data' => 'id',
                    'width' => 50,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'item_code',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'item_name',
                    'width' => 60,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'item_type',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'whs_code',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'uom',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'item_group',
                    'width' => 60,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'category_apd',
                    'width' => 60,
                    'wordWrap' => false,
                ],
            ],
            'header' => ['Id', 'ITEM CODE', 'ITEM NAME', 'ITEM TYPE', 'WHS CODE', "UOM", "ITEM GROUP", "CATEGORY APD"],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        try {
            foreach ($details as $detail) {
                $data = [
                    'item_code' => $detail['item_code'],
                    'item_name' => $detail['item_name'],
                    'item_type' => $detail['item_type'],
                    'whs_code' => $detail['whs_code'],
                    'uom' => $detail['uom'],
                    'item_group' => $detail['item_group'],
                    'category_apd' => $detail['category_apd'],
                    // 'created_by' => $request->user()->id,
                    'created_at' => date('Y-m-d'),
                    'updated_at' => date('Y-m-d'),
                ];

                $form = MappingItemSafety::where('id', '=', $detail['id'])->first();

                if (!$form) {
                    // $data = array_merge($data, ['created_by' => $request->user()->id]);
                    MappingItemSafety::create($data);
                } else {
                    if ($form->created_by == $request->user()->id) {
                        $form->update($data);
                    }
                }
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /* Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = MappingItemSafety::where('id', '=', $id)->first();
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param Config $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Config $productBrand)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            MappingItemSafety::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
