<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Models\Document\Document;
use App\Models\Document\DocumentCoordinate;
use App\Services\DocumentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DocumentCoordinateController extends Controller
{
    /**
     * Updates the coordinates of a document based on the given request.
     *
     * @param Request $request The HTTP request object containing the array of materai, array of e-sign, and ID.
     * @throws \Exception If an error occurs while updating the document with the new coordinates.
     * @return JsonResponse A JSON response indicating whether the data was successfully submitted.
     */
    public function updateCoordinate(Request $request): JsonResponse
    {
        try {
            $arrayMaterai = $request->arrayMaterai;
            $arrayEsign = $request->arrayEsign;
            $id = $request->id;

            $document = Document::find($id);
            $document->meterai_coordinate = null;
            $document->meterai_id = null;
            $document->meterai_stamp_type = null;
            $document->meterai_page_index = null;
            $document->meterai_top = null;
            $document->meterai_left = null;
            $document->meterai_width = null;
            $document->meterai_height = null;
            $document->materai_page = null;
            $document->vis_llx = null;
            $document->vis_lly = null;
            $document->vis_urx = null;
            $document->vis_ury = null;
            $document->digisign_coordinate = null;
            $document->digisign_id = null;
            $document->digisign_stamp_type = null;
            $document->digisign_page_index = null;
            $document->digisign_top = null;
            $document->digisign_left = null;
            $document->digisign_width = null;
            $document->digisign_height = null;
            $document->sign_page = null;
            $document->vis_digisign_llx = null;
            $document->vis_digisign_lly = null;
            $document->vis_digisign_urx = null;
            $document->vis_digisign_ury = null;
            $document->save();

            // if ($request->user()->username != '88101989') {
            $this->processCoordinate($arrayMaterai, $arrayEsign, $document);
            // }


            return $this->success('Data Submitted');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [$exception->getTrace()]);
        }
    }

    /**
     * Processes the coordinates of the given document and updates the relevant fields
     *
     * @param array $arrayMaterai Array of meterai objects
     * @param array $arrayEsign Array of esign objects
     * @param object $document Document object to be updated
     * @throws \Exception when given an invalid sign count
     */
    protected function processCoordinate($arrayMaterai, $arrayEsign, $document)
    {
        $document = Document::find($document->id);
        $totalMeterai = count($arrayMaterai) - 1;
        $totalSign = count($arrayEsign) - 1;
        $service = new DocumentService();
        // throw new \Exception($totalSign, 1);

        DocumentCoordinate::where("document_id", $document->id)->delete();
        if (count($arrayEsign) > 0) {
            if (count($arrayMaterai[0]) > 0) {
                $dataMeterai = ($document->document_type == 'invoice') ? $arrayMaterai[$totalMeterai][0] : $arrayMaterai[$totalMeterai][0];
            }
            $dataSign = ($document->document_type == 'invoice') ? $arrayEsign[$totalSign][0] : $arrayEsign[$totalSign][0];

            foreach ($arrayEsign as $key => $sign) {
                foreach ($sign as $value) {
                    DocumentCoordinate::updateOrCreate([
                        'document_id' => $document->id,
                        'sign_page' => ($value['pageIndex'] + 1),
                        'materai_page' => ($value['pageIndex'] + 1),
                    ], [
                        'digisign_coordinate' => $value['digisign_coordinate'],
                        'digisign_id' => $value['digisign_id'],
                        'digisign_stamp_type' => $value['digisign_stamp_type'],
                        'digisign_page_index' => $value['digisign_page_index'],
                        'digisign_top' => $value['digisign_top'],
                        'digisign_left' => $value['digisign_left'],
                        'digisign_width' => $value['digisign_width'],
                        'digisign_height' => $value['digisign_height'],
                        'vis_digisign_llx' => $value['digisign_left'],
                        'vis_digisign_lly' => (800 - $value['digisign_top'] - $value['digisign_height']),
                        'vis_digisign_urx' => ($dataSign['digisign_id'] == 'combine') ? ($service->pointToPixel($value['visLLX']) - 30) : $service->pointToPixel($value['visLLX']),
                        'vis_digisign_ury' => $service->pointToPixel($value['digisign_top']),
                    ]);
                }
            }

            if (array_key_exists('digisign_stamp_type', $dataSign)) {
                $document->digisign_coordinate = $dataSign['digisign_coordinate'];
                $document->digisign_id = $dataSign['digisign_id'];
                // $document->digisign_color = $dataSign['digisign_color'];
                $document->digisign_stamp_type = $dataSign['digisign_stamp_type'];
                $document->digisign_page_index = $dataSign['digisign_page_index'];
                $document->digisign_top = $dataSign['digisign_top'];
                $document->digisign_left = $dataSign['digisign_left'];
                $document->digisign_width = $dataSign['digisign_width'];
                $document->digisign_height = $dataSign['digisign_height'];
                $document->sign_page = $dataSign['pageIndex'] + 1;

                // $document->vis_digisign_llx = $dataSign['visLLX'];
                // $document->vis_digisign_lly = $dataSign['visLLY'];
                // $document->vis_digisign_urx = $dataSign['visLLX'];
                // $document->vis_digisign_ury = $dataSign['visLLY'];

                $document->vis_digisign_llx = $dataSign['visLLX'];

                if (count($arrayMaterai[0]) > 0) {
                    if ($totalMeterai == $totalSign) {
                        if ($dataMeterai['meterai_id'] == 'combine' || $dataSign['digisign_id'] == 'combine') {
                            $document->vis_digisign_lly = $dataSign['visLLY'];
                            $document->vis_digisign_urx = $service->pointToPixel($dataSign['visLLX'] - 30);
                            $document->vis_digisign_ury = $service->pointToPixel($dataSign['digisign_top']);
                        } else {
                            $document->vis_digisign_lly = $dataSign['visLLY'];
                            $document->vis_digisign_urx = $service->pointToPixel($dataSign['visLLX']);
                            $document->vis_digisign_ury = $service->pointToPixel($dataSign['digisign_top']);
                        }
                    } else {
                        $document->vis_digisign_lly = $dataSign['visLLY'] + 50;
                        $document->vis_digisign_urx = $service->pointToPixel($dataSign['visLLX']);
                        $document->vis_digisign_ury = $service->pointToPixel($dataSign['digisign_top']);
                    }
                } else {
                    $document->vis_digisign_lly = $dataSign['visLLY'];
                    $document->vis_digisign_urx = $service->pointToPixel($dataSign['visLLX']);
                    $document->vis_digisign_ury = $service->pointToPixel($dataSign['digisign_top']);
                }
            }
        }

        if (count($arrayMaterai[0]) > 0) {
            foreach ($arrayMaterai as $key => $meterai) {
                // throw new \Exception(json_encode($value[0]), 1);
                foreach ($meterai as $value) {
                    DocumentCoordinate::updateOrCreate([
                        'document_id' => $document->id,
                        // 'sign_page' => ($value['pageIndex'] + 1),
                        'materai_page' => ($value['pageIndex'] + 1),
                    ], [
                        'meterai_coordinate' => $value['meterai_coordinate'],
                        'meterai_id' => $value['meterai_id'],
                        'meterai_stamp_type' => $value['meterai_stamp_type'],
                        'meterai_page_index' => $value['meterai_page_index'],
                        'meterai_top' => $value['meterai_top'],
                        'meterai_left' => $value['meterai_left'],
                        'meterai_width' => $value['meterai_width'],
                        'meterai_height' => $value['meterai_height'],
                        'vis_llx' => $value['meterai_left'],
                        'vis_lly' => (800 - $value['meterai_top']),
                        'vis_urx' => $service->pointToPixel($value['meterai_left']),
                        'vis_ury' => $service->pointToPixel($value['meterai_top']),
                    ]);
                }
            }

            $dataMeterai = ($document->document_type == 'invoice') ? $arrayMaterai[$totalMeterai][0] : $arrayMaterai[$totalMeterai][0];
            if (count($arrayEsign[0]) > 0) {
                $dataSign = ($document->document_type == 'invoice') ? $arrayEsign[$totalSign][0] : $arrayEsign[$totalSign][0];
            }
            // throw new \Exception(json_encode($arrayMaterai[$totalMeterai][0]['meterai_stamp_type']), 1);
            if (array_key_exists('meterai_stamp_type', $dataMeterai)) {
                // code...
                $document->meterai_coordinate = $dataMeterai['meterai_coordinate'];
                $document->meterai_id = $dataMeterai['meterai_id'];
                // $document->meterai_color = $dataMeterai['meterai_color'];
                $document->meterai_stamp_type = $dataMeterai['meterai_stamp_type'];
                $document->meterai_page_index = $dataMeterai['meterai_page_index'];
                $document->meterai_top = $dataMeterai['meterai_top'];
                $document->meterai_left = $dataMeterai['meterai_left'];
                $document->meterai_width = $dataMeterai['meterai_width'];
                $document->meterai_height = $dataMeterai['meterai_height'];

                // $document->vis_llx = $dataMeterai['visLLX'];
                // $document->vis_lly = $dataMeterai['visLLY'];
                // $document->vis_urx = $dataMeterai['visLLX'];
                // $document->vis_ury = $dataMeterai['visLLY'];

                $document->materai_page = $dataMeterai['pageIndex'] + 1;

                $document->vis_llx = $dataMeterai['visLLX'];
                if (count($arrayEsign[0]) > 0) {
                    if ($totalMeterai == $totalSign) {
                        if ($dataMeterai['meterai_id'] == 'combine' || $dataSign['digisign_id'] == 'combine') {
                            $document->vis_lly = $dataMeterai['visLLY'];
                            $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                            $document->vis_ury = $service->pointToPixel($dataMeterai['meterai_top']);
                        } else {
                            $document->vis_lly = $dataMeterai['visLLY'];
                            $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                            $document->vis_ury = $service->pointToPixel($dataMeterai['meterai_top']);
                        }
                    } else {
                        $document->vis_lly = $dataMeterai['visLLY'] + 50;
                        $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                        $document->vis_ury = $service->pointToPixel($dataMeterai['meterai_top']);
                    }
                } else {
                    // $document->vis_lly = $dataMeterai['visLLY'] + 50;
                    // $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                    // $document->vis_ury = $service->pointToPixel($dataMeterai['visLLY'] + 50);

                    $document->vis_lly = $dataMeterai['visLLY'];
                    $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                    $document->vis_ury = $service->pointToPixel($dataMeterai['meterai_top']);
                }
            }
        }

        $document->save();
    }
}
