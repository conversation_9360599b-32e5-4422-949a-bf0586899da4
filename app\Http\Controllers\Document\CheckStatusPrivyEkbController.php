<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Services\ApprovalPrivyService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CheckStatusPrivyEkbController extends Controller
{
    public function index()
    {
        $batch = DB::connection('sqlsrv4')
            ->table('batch_approvals')
            ->where('status', 'uploaded')
            ->get();

        foreach ($batch as $key => $value) {
            $this->show($value->id);
        }
    }

    public function show($id)
    {
        $service = new ApprovalPrivyService();
        $service->login();


        $row = DB::connection('sqlsrv4')
            ->table('batch_approvals')
            ->where('id', $id)
            ->first();

        $form_draft = json_decode(json_decode($row->value));
        $file_name = $form_draft->file_names;
        $path_download = public_path('documents/' . $file_name);

        if (file_exists($path_download) && filesize($path_download) > 0) {
            // Log::info("file ekb exist");
            // return response()->json([
            //     'message' => 'file exist'
            // ]);
        }

        $tenant = DB::connection('sqlsrv4')
            ->table('T_MDOC')
            ->leftJoin("M_Tenant", "M_Tenant.DocEntry", "T_MDOC.Tenant_key")
            ->select("M_Tenant.ChannelId")
            ->where('T_MDOC.DocEntry', $row->document_id)
            ->first();

        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('PrivyCheckDocumentRegister', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');
        $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');

        // Log::info('params check document status privy ekb', [
        //     'params' => $this->params($row, $tenant)
        // ]);

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $this->signature($row, $timestamp, 'POST', $tenant),
                'Request-ID' => strtotime($timestamp),
            ])
            ->post($url, $this->params($row, $tenant))
            ->throw(function ($response, $e) {
                throw new \Exception('E-DIGITAL SIGN: ' . json_decode($response->collect()), 1);
            });

        $status = $response->collect()['data']['status'];

        // Log::info('data ekb privy', [
        //     'reference_number' => $row->reference_number,
        //     'status' => $response->collect()['data']['status'],
        //     'params' => $this->params($row, $tenant),
        //     'message' => $response->collect()['data']['message']
        // ]);

        if ($response->collect()['data']['status'] == 'completed') {
            $contents = base64_decode(str_replace('data:application/pdf;base64,', '', $response['data']['signed_document']));
            $form_draft = json_decode(json_decode($row->value));
            $file_name = $form_draft->file_names;
            // $file_name = $row->attachment->file_name;
            // $file_name = (Str::contains($file_name, '_token_')) ? substr($file_name, 0, -19) . '.pdf' : $file_name;

            // $path_download = public_path('documents/' . $file_name);
            // file_put_contents($path_download, $contents);

            custom_disk_put('documents/' . $file_name, $contents);
            $callback_message = 'Success reference number: ' . $row->reference_number;
        } else {
            $callback_message = 'reference number: ' . $row->reference_number . ' ' . $response->collect()['data']['message'];
        }
        DB::connection('sqlsrv4')
            ->table('batch_approvals')
            ->where('id', $id)
            ->update([
                'status' => ($status == 'completed') ?  'approved' : $status,
                'callback_message' => $callback_message
            ]);

        // return response()->json([
        //     'message' => 'File stored'
        // ]);

        // return response()->json([
        //     'data' => $response->collect(),
        //     'params' => $this->params($row, $tenant),
        //     'tenant' => $tenant,
        //     'url' => $url
        // ]);
    }

    public function params($document, $tenant)
    {
        $channelId = $tenant->ChannelId;
        return [
            "reference_number" => $document->reference_number,
            "channel_id" => $channelId,
            "document_token" => $document->document_token,
            "info" => ""
        ];
    }

    protected function signature($row, $timestamp, $httpVerb, $tenant)
    {
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $body = $this->params($row, $tenant);

        $strBody = json_encode($body);
        $strBody = str_replace(" ", "", $strBody);
        $method = $httpVerb;
        $body_md5 = base64_encode(md5($strBody, true));
        $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        $byte_key = utf8_encode($api_secret);
        $new_hmac = utf8_encode($hmac_signature);
        $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        $base64_message = base64_encode($hmac);
        $auth_string = $api_key . ":" . $base64_message;
        $signature = base64_encode($auth_string);
        return $signature;
    }
}
