<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\ApprovalRequester
 *
 * @property-read \App\Models\Resv\Stages|null $stages
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRequester newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRequester newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRequester query()
 * @mixin \Eloquent
 */
class ApprovalRequester extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $connection = 'sqlsrv2';
    protected $table = 'U_WTM1';
    protected $primaryKey = 'U_DocEntry';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function stages(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Stages::class, "U_WstCode", "U_WstCode");
    }
}
