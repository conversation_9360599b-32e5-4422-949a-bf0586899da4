<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Mail\ClinicNotification;
use Illuminate\Support\Facades\Mail;

class SendClinicNotificationEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $subject;
    protected $content;
    protected $receiver_name;
    protected $cc;
    protected $receiver = [];

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($subject, $content, $receiver, $receiver_name, $cc)
    {
        $this->subject = $subject;
        $this->content = $content;
        $this->receiver = $receiver;
        $this->cc = $cc;
        $this->receiver_name = $receiver_name;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $email = new ClinicNotification($this->receiver_name, $this->content, $this->subject);
        Mail::to($this->receiver)
            ->cc($this->cc)
            ->send($email);
    }
}
