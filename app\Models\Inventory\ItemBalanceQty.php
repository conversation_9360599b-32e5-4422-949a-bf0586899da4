<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Inventory\ItemBalanceQty
 *
 * @property int $id
 * @property int $master_item_id
 * @property string $balance_date
 * @property float $balance_qty
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty query()
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty whereBalanceDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty whereBalanceQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty whereMasterItemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ItemBalanceQty whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ItemBalanceQty extends Model
{
    use HasFactory;

    protected $guarded = [];
    public $table = 'item_balance_qty';
}
