<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRegisterPriviesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('register_privies', function (Blueprint $table) {
            $table->id();
            $table->string('reference_number', 15)->unique();
            $table->string('channel_id', 32);
            $table->string('info', 100)->nullable();
            $table->string('email', 100);
            $table->string('phone', 20);
            $table->string('nik', 16)->nullable();
            $table->string('name', 100)->nullable();
            $table->date('dob')->nullable();
            $table->text('selfie')->nullable();
            $table->text('identity')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('register_privies');
    }
}
