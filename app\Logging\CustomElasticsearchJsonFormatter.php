<?php

namespace App\Logging;

use Monolog\Formatter\ElasticsearchFormatter;

class CustomElasticsearchJsonFormatter
{
   public function __invoke(array $record)
    {
        $record['context'] = $this->convertKeysToLowercase($record['context']);
        $record['extra'] = $this->convertKeysToLowercase($record['extra']);

        return $record;
    }

    private function convertKeysToLowercase(array $array)
    {
        $lowercasedArray = [];

        foreach ($array as $key => $value) {
            $lowercasedKey = is_string($key) ? strtolower($key) : $key;
            if (is_array($value)) {
                $lowercasedArray[$lowercasedKey] = $this->convertKeysToLowercase($value);
            } else {
                $lowercasedArray[$lowercasedKey] = $value;
            }
        }

        return $lowercasedArray;
    }
}