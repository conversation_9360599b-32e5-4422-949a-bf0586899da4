<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;

class ApprovalMeteraiRequest extends Notification implements ShouldQueue
{
    use Queueable;

    public $emailData = [];

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($emailData)
    {
        $this->emailData = $emailData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        sleep(5);
        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');

        $message = new MailMessage();
        $message->cc($this->emailData['cc'])
            ->subject($this->emailData['subject'])
            ->greeting($this->emailData['greeting'])
            ->line($this->emailData['body']);
        // ->line($this->emailData['inv']);

        if (array_key_exists('inv', $this->emailData)) {
            // throw new \Exception(json_encode($this->emailData['inv']), 1);
            if ($this->emailData['inv']) {
                foreach ($this->emailData['inv'] as $key => $value) {
                    $message->line($value);
                }
            }
        }
        return $message;
        // return (new MailMessage)
        //             // ->to($this->emailData['to'])
        //             ->cc($this->emailData['cc'])
        //             ->subject($this->emailData['subject'])
        //             ->greeting($this->emailData['greeting'])
        //             ->line($this->emailData['body'])
        //             ->line($this->emailData['inv']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $status = $this->emailData['status'];
        return [
            'documentType' => 'E-Sign',
            'transactionType' => 'E-Sign Invoice Approval',
            'documentId' => 0,
            'documentNumber' => 0,
            'title' =>  $this->emailData['subject'],
        ];
    }

    /**
     * Determine which queues should be used for each notification channel.
     *
     * @return array
     */
    public function viaQueues()
    {
        return [
            'mail' => 'document',
        ];
    }
}
