<?php

namespace App\Services\Reports;

use App\Models\Resv\ResvHeader;

class ReportViewDataItService
{
    public function show($date_from, $date_to, $request_type)
    {
        $rows = ResvHeader::select(
            "resv_headers.DocNum",
            "resv_headers.DocDate",
            "resv_headers.Division",
            "resv_headers.WhsCode",
            "resv_details.ItemCode",
            "resv_details.ItemName",
            "resv_details.UoMCode",
            "resv_details.ReqQty",
            "resv_details.ReqNotes",
            "resv_details.LineNum",
            "resv_headers.RequesterName",
            "resv_headers.WorkLocation",
            "resv_headers.DocStatus",


        )
            ->leftJoin("resv_details", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            ->whereRaw("resv_headers.\"DocDate\" BETWEEN '${date_from}' AND '${date_to}' ")
            // ->whereRaw("resv_headers.\"ItemType\" LIKE '%${item_type}%'")
            ->whereRaw("resv_details.\"RequestType\" LIKE '%${request_type}%' ")
            // ->whereIn("resv_headers.WhsCode", ["MW-IT", "MW-ZZ", "IG03"])
            ->whereIn("resv_details.SubGroup", ['66101', '66102', '66104'])
            ->whereIn("resv_headers.ApprovalStatus", ["Y"])
            // ->orWhereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
            ->orderBy("resv_headers.DocNum")
            ->orderBy("resv_details.LineNum")
            ->get();
        $service = new ReportService();
        return $service->getS4Docs($rows);
    }

    public function header()
    {
        return [
            'DocNum',
            'Doc Date',
            'Department',
            'WH',
            'Item Code',
            'Item Name',
            'UoM',
            'Request Qty',
            'Notes',
            'Requester',
            'GIR NO',
            'PR NO',
            'PO NO',
            'GRPO NO',
            'GI NO',
            'Document Status'
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "DocNum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Division",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "WhsCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "UoMCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ReqQty",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ReqNotes",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "RequesterName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_GIRNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_PRNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            // [
            //     'data' => "LineStatus",
            //     'width' => 100,
            //     'wordWrap' => false,
            //     'readOnly' => true,
            // ],
            [
                'data' => "PONum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "GRPONum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value->DocNum,
                $value->DocDate,
                $value->Department,
                $value->WhsCode,
                $value->ItemCode,
                $value->ItemName,
                $value->UoMCode,
                $value->ReqQty,
                $value->ReqNotes,
                $value->RequesterName,
                $value->SAP_GIRNo,
                $value->SAP_PRNo,
                $value->LineStatus,
                $value->PONum,
                $value->GRPONum,
            ];
        }
        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
