<?php

namespace App\Http\Controllers\Common;

use App\Models\Resv\NewEmployee;
use App\Models\Common\SafetyData;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NewEmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = NewEmployee::select(
            "U_DocEntry",
            "employee_id",
            "employee_name",
            "department",
            "reference",
            "created_at",
        )
            ->orderBy('employee_name', 'desc')
            ->where('department', '=', $request->user()->department)
            ->get();

        if (count($data) < 1) {
            $data = [
                [
                    "U_DocEntry" => null,
                    "employee_id" => null,
                    "employee_name" => null,
                    "department" => null,
                    "reference" => null,
                    "created_at" => null,
                ]
            ];
        }

        return $this->success([
            'rows' => $data,
            'columns' => [
                [
                    'data' => 'U_DocEntry',
                    'width' => 50,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'employee_id',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'employee_name',
                    'width' => 60,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'department',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'reference',
                    'width' => 20,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'created_at',
                    'width' => 40,
                    'wordWrap' => false,
                ],
            ],
            'header' => ['Id', 'NIK', 'EMPLOYEE NAME', 'DEPARTMENT', 'REFERENCE', 'CREATED AT'],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        try {
            foreach ($details as $detail) {
                $data = [
                    'employee_id' => $detail['employee_id'],
                    'employee_name' => $detail['employee_name'],
                    'department' => $detail['department'],
                    'reference' => $detail['reference'],
                    // 'created_by' => $request->user()->id,
                    // 'created_at' => date('Y-m-d'),
                    'updated_at' => date('Y-m-d'),
                ];

                $form = NewEmployee::where('U_DocEntry', '=', $detail['U_DocEntry'])->first();

                if (!$form) {
                    $data = array_merge($data, ['created_by' => $request->user()->id]);
                    DB::connection('sqlsrv')
                        ->table('new_employee')
                        ->insert($data);
                } else {
                    DB::connection('sqlsrv')
                        ->table('new_employee')
                        ->where('U_DocEntry', '=', $detail['U_DocEntry'])
                        ->update($data);

                    if (isset($detail['employee_id']) && isset($detail['employee_name'])) {
                        $header = DB::connection('sqlsrv')
                            ->table('resv_headers')
                            ->where('DocNum', '=', $detail['reference'])
                            ->first();

                        $dataDetail = DB::connection('sqlsrv')
                            ->table('resv_details')
                            ->where('LineEntry', '=', $form->detail_id)
                            ->first();

                        $data = [
                            'date_out' => date('Y-m-d', strtotime($header['DocDate'])),
                            'id_card' => $detail['employee_id'],
                            'employee_name' => $detail['employee_name'],
                            'item_code' => $dataDetail['ItemCode'],
                            'item_name' => $dataDetail['ItemName'],
                            'qty' => 1,
                            'uom' => $dataDetail['UoMCode'],
                            'company' => auth()->user()->company,
                            'department' => $dataDetail['U_Department'],
                            'notes' => $dataDetail['ReqNotes'],
                            'created_at' => date('Y-m-d'),
                            'updated_at' => date('Y-m-d'),
                        ];

                        $brand = SafetyData::where('item_code', '=', $dataDetail['ItemCode'])
                            ->where('id_card', '=', $detail['employee_id'])
                            ->where('date_out', '=', date('Y-m-d', strtotime($header['DocDate'])), )
                            ->first();

                        if (!$brand) {
                            DB::connection('sqlsrv')
                                ->table('safety_data')
                                ->insert($data);
                        } else {
                            DB::connection('sqlsrv')
                                ->table('safety_data')
                                ->where('U_DocEntry', '=', $detail['U_DocEntry'])
                                ->update($data);
                        }
                    }
                }
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /* Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = NewEmployee::where('category', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param Config $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Config $productBrand)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {

    }
}
