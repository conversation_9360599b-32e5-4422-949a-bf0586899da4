<?php

namespace App\Services;

use App\Models\Common\SafetyData;
use App\Models\Master\MappingItemSafety;
use App\Models\Resv\ReservationDetails;
use App\Models\Resv\ResvHeader;
use App\Models\User;
use App\Models\View\ViewEmployee;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class ReservationValidateDataService
{

    /**
     * Check the availability of an item in the specified warehouse.
     *
     * @param string $itemCode The code of the item to check.
     * @param string $whs The warehouse to check the item availability in.
     * @param string $item_itm The item to check.
     * @param array $items The items to check.
     * @return int The available quantity of the item.
     */
    public function checkItem(string $itemCode, string $whs, string $item_itm, array $items): int
    {
        $service = new SapS4Service();
        $service->login();

        $data = $service->getMaterial(1, 100, $itemCode, $item_itm, null, $items['ItemCategory'], $whs);

        $available = 0;
        if (array_key_exists('DATA', $data)) {
            foreach ($data as $value) {
                $available = $value['LABST'];
            }
        }

        return $available;
    }

    protected function validateSalesAndFuel($form)
    {
        if ($form['RequestType'] == 'Sales') {
            if ($form['UsageFor'] == 'External') {
                if (empty($form['Customer'])) {
                    throw new \App\Exceptions\CustomException("Untuk reservasi external customer tidak boleh kosong!");
                }
            }
        }

        if ($form['CategoryType'] == 'Fuel') {
            if (empty($form['VehicleNo'])) {
                throw new \App\Exceptions\CustomException("Nomer kendaraan tidak boleh kosong!");
            }

            if (empty($form['Mileage'])) {
                throw new \App\Exceptions\CustomException("Jarak Tempuh kendaraan tidak boleh kosong!");
            }
        }
    }

    /**
     * Validates the general reservation details.
     *
     * @param Collection $details The reservation details to be validated.
     * @param array $form The reservation form data.
     * @param object $request The request object.
     * @throws \Exception If any validation fails.
     * @return void
     */
    protected function validateGeneralReservation($details, $form, $request)
    {
        // $requester = User::where('username', $form['U_NIK'])->first();
        $requester = ViewEmployee::where('Nik', $form['U_NIK'])->first();
        foreach ($details as $index => $items) {
            $line = ($index + 1);
            if ($form['WhsCode'] == 'IG04' && $form['RequestType'] == 'Normal' && $form['CategoryType'] != 'APD' && $form['ItemType'] == 'Ready Stock') {
                throw new \App\Exceptions\CustomException("Line $line: Category type must be APD!");
            }

            if ($form["ItemType"] == "Service") {
                if (!empty($items["AssetCode"]) || !empty($items["AssetName"])) {
                    throw new \App\Exceptions\CustomException("Line $line: Item type service cannot have asset code!");
                }
            }


            if ($form['CategoryType'] == 'APD') {
                if ($request->user()->hasAnyRole(['HRD Morowali', 'Admin E-RESERVATION'])) {
                    // throw new \App\Exceptions\CustomException($items['EmployeeId']);
                    if (!array_key_exists('EmployeeId', $items)) {
                        throw new \App\Exceptions\CustomException("Line $line: EmployeeId cannot empty!!");
                    }
                    if (empty($items['EmployeeId'])) {
                        throw new \App\Exceptions\CustomException("Line $line: EmployeeId cannot empty!");
                    }
                }
            }

            if (empty($items['ItemCode'])) {
                throw new \App\Exceptions\CustomException("Line $line: Item Code cannot empty!");
            }

            if ($items['ItemGroup'] == 'ZITS') {
                $item_code = $items['ItemCode'];
                if (str($requester->WorkLocation)->contains(['IMIP MOROWALI', 'SECURITY MOROWALI', 'BDT MOROWALI'])) {
                    if ($form['WhsCode'] == 'IG03') {
                        $whs = "('MW-GE')";
                        $item_itm = 'ZITS';
                        $check_in_ge = $this->checkItem($item_code, $whs, $item_itm, $items);
                        if ($check_in_ge > 0) {
                            throw new \App\Exceptions\CustomException("Item Code ${item_code} masih ada stok di warehouse MW-GE,
                            silahkan refresh lalu ulangi input dengan memilih warehouse MW-GE!");
                        }
                    }
                }
            }

            if (empty($items['WhsCode'])) {
                throw new \App\Exceptions\CustomException("Line $line: WhsCode cannot empty!");
            }

            if (empty($items['ReqNotes'])) {
                throw new \App\Exceptions\CustomException("Line $line: Req Notes cannot empty!");
            }

            if (empty($items['ReqQty'])) {
                throw new \App\Exceptions\CustomException("Line $line: ReqQty cannot empty!");
            }

            if ($items['ItemGroup'] == 'ZAST') {
                if (empty($items['AssetCode']) || empty($items['AssetName'])) {
                    throw new \App\Exceptions\CustomException("Line $line: Asset Code and Aseet Name Cannot
                    Empty for Item Group ASSET( PURCHASE)!");
                }
            }

            if ($form['ItemType'] == 'Asset') {
                if (empty($items['AssetCode']) || empty($items['AssetName'])) {
                    throw new \App\Exceptions\CustomException("Line $line: Asset Code and Aseet Name Cannot
                    Empty for Item Group ASSET( PURCHASE)!");
                }
            }

            $itemTypeHeader = $form['ItemType'];
            if ($itemTypeHeader == 'Ready Stock') {
                $itemType = 'RS';
            } elseif ('Non Ready Stock') {
                $itemType = 'NRS';
            } elseif ('Project') {
                $itemType = 'NRS';
            } else {
                $itemType = $itemTypeHeader;
            }

            if ($form['CategoryType'] == 'Triwulan' && $items['NPB'] == 'Y') {
                throw new \App\Exceptions\CustomException("Line $line: silahkan pilih SPB untuk item triwulan!");
            }


            if (str($requester->WorkLocation)->contains(['IMIP MOROWALI', 'SECURITY MOROWALI', 'BDT MOROWALI'])) {
                if ($items['ItemGroup'] == 'ZITS') {
                    if ($itemType != $items['ItemCategory']) {
                        throw new \App\Exceptions\CustomException("Line $line: Item Category Must Same With Header!");
                    }
                }
                if ($itemTypeHeader != 'Ready Stock') {
                    if ($form["RequestType"] != 'Project') {
                        if ($items["AvailableQty"] > 0 && $items['SPB'] == 'Y') {
                            if ($items["ReqQty"] <= $items["AvailableQty"]) {
                                throw new \App\Exceptions\CustomException("Line $line: silahkan pilih NPB karena stock barang tersedia!");
                            }
                        }
                    }
                }
            }

            if ($items["ItemCategory"] == 'RS') {
                if ($form['RequestType'] == 'Normal') {
                    if ($items['NPB'] == 'Y') {
                        if (isset($items['OtherResvNo'])) {
                            throw new \App\Exceptions\CustomException("Line $line: Cannot insert OtherResvNo!");
                        }

                        if ($items['ItemGroup'] != '102') {
                            if ($items["ReqQty"] > $items["AvailableQty"]) {
                                if ($requester->WorkLocation == 'BDM MOROWALI') {
                                    if ($items['ItemGroup'] != 'ZITS' || ($items['ItemGroup'] == 'ZAST' && Str::contains($items['SubGroup'], ['89112', '89102']))) {
                                        throw new \App\Exceptions\CustomException("Line $line: Request Qty Cannot Greater Than Available Qty!");
                                    }
                                } else {
                                    throw new \App\Exceptions\CustomException("Line $line: Request Qty Cannot Greater Than Available Qty!");
                                }
                            }
                        }
                    } else {
                        if ($requester->WorkLocation != 'BDM MOROWALI') {
                            throw new \App\Exceptions\CustomException("Line $line: NPB field cannot empty!");
                        }
                    }
                } else {
                    if ($items['SPB'] != 'Y') {
                        throw new \App\Exceptions\CustomException("Line $line: SPB field cannot empty!");
                    }
                }
            } elseif ($items["ItemCategory"] != 'RS') {
                if ($items['NPB'] == 'Y') {
                    if ($items['ItemGroup'] != '102') {
                        if ($requester->WorkLocation == 'BDM MOROWALI') {
                            if ($items['ItemGroup'] != 'ZITS' || ($items['ItemGroup'] == 'ZAST' && Str::contains($items['SubGroup'], ['89112', '89102']))) {
                                if ($items["ReqQty"] > $items["AvailableQty"] && !isset($items['OtherResvNo'])) {
                                    throw new \App\Exceptions\CustomException("Line $line: Request Qty Cannot Greater Than Available Qty!");
                                }
                            }
                        } else {
                            if ($items["ReqQty"] > $items["AvailableQty"] && !isset($items['OtherResvNo'])) {
                                throw new \App\Exceptions\CustomException("Line $line: Request Qty Cannot Greater Than Available Qty!");
                            }
                        }
                    }

                    if ($requester->WorkLocation != 'BDM MOROWALI') {
                        if ($items["OnHand"] < $items["ReqQty"] && !isset($items['OtherResvNo'])) {
                            throw new \App\Exceptions\CustomException("Line $line: On Hand Qty Cannot Greater Than Available Qty!");
                        }
                    }

                }

                if (($items['NPB'] == 'Y' && ($form['RequestType'] == 'Normal' || $form['RequestType'] == 'For Restock SubWH') && $requester->WorkLocation != 'BDM MOROWALI')) {
                    if ($items["AvailableQty"] <= 0) {
                        if (empty($items['OtherResvNo'])) {
                            throw new \App\Exceptions\CustomException("Line $line: Other Reservation No is required!");
                        }
                    } else {
                        $otherResvNo = (!empty($items['OtherResvNo'])) ? $items['OtherResvNo'] : null;
                        $check_docnum = ResvHeader::where("DocNum", "=", $otherResvNo)->first();
                        if ($check_docnum) {
                            $check_details = ReservationDetails::where("U_DocEntry", "=", $check_docnum->U_DocEntry)
                                ->where("ItemCode", "=", $items['ItemCode'])
                                ->first();
                            if (!$check_details) {
                                throw new \App\Exceptions\CustomException("Line $line: Other Reservation No with this
                                itemcode is not valid!");
                            }
                        } elseif ($items["AvailableQty"] <= 0) {
                            throw new \App\Exceptions\CustomException("Line $line: Other Reservation No is not valid!");
                        }
                    }
                }
            }
        }
    }

    /**
     * Validates the item SPB and NPB.
     *
     * @param Collection $details The details of the item.
     * @param array $items The form data.
     * @throws \Exception If the validation fails.
     */
    public function validateItemSpbAndNpb($details, $form)
    {
        $item_check_npb = [];
        $count_item_npb = 0;
        $count_item_spb = 0;
        // $requester = ViewEmployee::where('Nik', $form['U_NIK'])->first();
        $user = User::where('id', auth()->user()->id)->first();
        $service = new SapS4Service();
        $service->login();
        $check_first = ($details[0]['NPB'] == 'Y') ? 'NPB' : 'SPB';
        foreach ($details as $index => $items) {
            // $mappingItemSafety =
            if ($items['NPB'] == 'N') {
                continue;
            }
            if (array_key_exists('U_AppResBy', $items)) {
                if ($form['EmployeeType'] == 'Normal' && $form['Replacement'] == 'Normal') {
                    if ($items['U_AppResBy'] == 'GA' || $items['U_AppResBy'] == 'HSE') {
                        $checkMapper = MappingItemSafety::where('item_code', $items['ItemCode'])->first();

                        $validate_requests = SafetyData::where('safety_data.id_card', $items['EmployeeId'])
                            ->leftJoin("mapping_item_safeties", "mapping_item_safeties.item_code", "safety_data.item_code")
                            ->select([
                                "safety_data.U_DocEntry",
                                "safety_data.item_code",
                                "safety_data.date_out",
                                "mapping_item_safeties.category_apd",
                            ])
                            ->orderBy('safety_data.U_DocEntry', 'desc')
                            ->get();

                        foreach ($validate_requests as $validate_request) {
                            if ($validate_request && !str($user->location)->contains(["BDM"])) {
                                $row_data = $service->getMaterial(1, 100, $items['ItemCode']);

                                if (!array_key_exists('DATA', $row_data)) {
                                    throw new \App\Exceptions\CustomException($row_data['MESSAGE']);
                                }

                                $data = $service->transformItemDataFromS4($row_data);

                                foreach ($data as $k => $value) {
                                    if ($checkMapper) {
                                        if ($checkMapper->category_apd == $validate_request->category_apd) {
                                            $date_out = Carbon::parse($validate_request->date_out);
                                            $request_date = Carbon::parse($form['DocDate']);
                                            $diff_in_month = $request_date->diffInMonths($date_out);

                                            if (intval($items['U_Period']) != 0) {
                                                // throw new \Exception(intval($diff_in_month));
                                                if (abs(intval($diff_in_month)) < intval($items['U_Period'])) {
                                                    throw new \App\Exceptions\CustomException("Pengambilan " . $items['ItemName'] . " minimal " . $items['U_Period'] . " bulan dari pengambilan sebelumnya \n" . $date_out->format('d F Y'), 1);
                                                }
                                            }
                                        }
                                    } else {
                                        // check if check mapper is null
                                         if ($validate_request->item_code == $value["ItemCode"]) {
                                            if ($value["U_Category"] == $items['U_Category']) {
                                                $date_out = Carbon::parse($validate_request->date_out);
                                                $request_date = Carbon::parse($items['DocDate']);
                                                $diff_in_month = $request_date->diffInMonths($date_out);

                                                if ($items['Replacement'] == 'Normal' && $items['RequestType'] == 'Normal' && !$user->hasAnyRole(['Admin E-RESEVATION SAFETY', 'Admin E-RESEVATION Not Validate Item Safety'])) {
                                                    if (intval($items['U_Period']) != 0) {
                                                        // throw new \Exception(intval($diff_in_month));
                                                        if (abs(intval($diff_in_month)) < intval($items['U_Period'])) {
                                                            throw new \App\Exceptions\CustomException("Pengambilan " . $items['ItemName'] . " minimal " . $items['U_Period'] . " bulan dari pengambilan sebelumnya \n" . $date_out->format('d F Y'), 1);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    }
                }
            }


            $item_check_npb[] = $items['ItemGroup'];
            if ($items['NPB'] == 'Y') {
                $count_item_npb++;
            }

            if ($items['SPB'] == 'Y') {
                $count_item_spb++;
            }

            if ($user->hasAnyRole(['Admin E-RESERVATION BDM']) && $items['SPB'] == 'Y') {
                if ($items['ItemGroup'] == 'ZITS') {
                    throw new \App\Exceptions\CustomException("Row  " . (++$index) . " : User tidak ada authorisasi untuk request SPB!");
                }
            }
        }

        if ($check_first == 'NPB') {
            if (count($item_check_npb) != $count_item_npb) {
                throw new \App\Exceptions\CustomException("Request barang SPB dan NPB tidak boleh dicampur!");
            }
        }

        if ($check_first == 'SPB') {
            if (count($item_check_npb) != $count_item_spb) {
                throw new \App\Exceptions\CustomException("Request barang SPB dan NPB tidak boleh dicampur!!!!");
            }
        }
    }

    /**
     * Validates the item ITRACC.
     *
     * @param Collection $details The details of the item.
     * @param array $form The form data.
     * @throws \Exception If the item code still has stock in MW-HE.
     */
    protected function validateItemItracc($details, $form)
    {
        // validasi item ITRACC
        $count_item_itract = 0;
        $item_group_ittracc = [];
        foreach ($details as $index => $items) {
            $item_group_ittracc[] = $items['ItemGroup'];
            if (Str::contains($items['ItemGroup'], ['ZTRD', 'ZTRS', 'ZTRU'])) {
                $count_item_itract++;
            }
        }

        if ($count_item_itract > 0) {
            if ($form['ItemType'] == 'Non Ready Stock') {
                if ($items['SPB'] == 'Y') {
                    if (Str::contains($items['ItemGroup'], ['ZTRD', 'ZTRS', 'ZTRU'])) {
                        $item_code = $items['ItemCode'];
                        if ($form['WhsCode'] == 'MW-HE') {
                            $whs = "('MW-HE')";
                            $item_itm = "'ZTRD','ZTRS','ZTRU'";
                            $check_in_ge = $this->checkItem($item_code, $whs, $item_itm, $items);
                            if ($check_in_ge > 0) {
                                throw new \App\Exceptions\CustomException("Item Code ${item_code} masih ada stok di warehouse MW-HE,
                                Kolom NPB mohon dicentang!");
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Validates the IT items in the given array of details.
     *
     * @param Collection $details The array of details containing the items to validate.
     * @throws \Exception If the request contains mixed IT and non-IT items, or if the request contains multiple IT items with different sub-groups.
     */
    protected function validateItemIt($details, $form)
    {
        // $requester = User::where('username', $form['U_NIK'])->first();
        $requester = ViewEmployee::where('Nik', $form['U_NIK'])->first();
        // validasi barang IT
        $item_groups = [];
        $count_sub_group_network = 0;
        $count_item_it = 0;
        $count_sub_group_other = 0;

        foreach ($details as $index => $items) {
            $item_groups[] = $items['ItemGroup'];
            if ($items['ItemGroup'] == 'ZITS') {
                if (auth()->user()->username != '88101989') {
                    if (str($requester->WorkLocation)->contains(['IMIP MOROWALI', 'SECURITY MOROWALI', 'BDT MOROWALI'])) {
                        if ($items['WhsCode'] != 'IG03') {
                            throw new \App\Exceptions\CustomException("Row " . ($index + 1) . ": Request barang IT harus memilih warehouse IG03!");
                        }
                        $count_item_it++;
                    }
                }
            }
            if (array_key_exists('SubGroup', $items)) {
                if ($items['SubGroup'] == '89112') {
                    $count_sub_group_other++;
                }
                if ($items['SubGroup'] == '66103') {
                    $count_sub_group_network++;
                } else {
                    $count_sub_group_other++;
                }
            }
        }

        if ($count_item_it > 0) {
            if ($count_item_it != count($item_groups)) {
                throw new \App\Exceptions\CustomException("Request barang IT tidak boleh di campur dengan barang yang lain!");
            }

            if ($count_sub_group_network > 0) {
                if ($count_sub_group_network != count($item_groups)) {
                    throw new \App\Exceptions\CustomException("Request barang IT Network tidak boleh beda sub group!");
                }
            }

            if ($count_sub_group_other > 0) {
                if ($count_sub_group_other != count($item_groups)) {
                    throw new \App\Exceptions\CustomException("Request barang IT tidak boleh beda sub group!");
                }
            }
        }
    }

    /**
     * Validates the item GA safety.
     *
     * @param Collection $details The details of the item GA.
     * @throws \Exception If the request barang GA is mixed with other items.
     * @throws \Exception If the request barang GA APD is mixed with other items.
     * @throws \Exception If the request barang GA in Safety is mixed with other items.
     * @throws \Exception If the request barang Safety is mixed with other items.
     */
    protected function validateItemGaSafety($details, $form)
    {
        // validasi barang GA sub group STATIONARY ( ALAT TULIS )

        $item_groups_ga = [];
        $item_groups_ga_apd = [];
        $count_item_ga = 0;
        $count_item_ga_seragam = 0;

        $item_groups_safety = [];
        $count_item_safety = 0;

        $item_groups_ga_in_safety = [];
        $count_item_ga_in_safety = 0;

        // $requester = User::where('username', $form['U_NIK'])->first();
        $requester = ViewEmployee::where('Nik', $form['U_NIK'])->first();
        $item_item_ga_safety = [];
        $item_item_ga_seragam = [];
        $item_item_ga = [];
        $item_item_safety = [];

        foreach ($details as $index => $items) {
            $item_groups_ga[] = $items['SubGroup'];
            if (str($requester->WorkLocation)->contains(['IMIP MOROWALI'])) {
                if ($items['SubGroup'] == '66201') {
                    // add new validation
                    if ($items['SPB'] == 'Y' && $items['WhsCode'] != 'IG01') {
                        throw new \App\Exceptions\CustomException("Row " . ($index + 1) . ": Request barang GA STATIONARY ( ALAT TULIS ) harus memilih warehouse IG01!");
                    }
                    $count_item_ga++;
                    $item_item_ga[] = $items['ItemCode'];
                }
            }
            $item_groups_ga_apd[] = $items['SubGroup'];
            if ($items['SubGroup'] == '67104' && $items["ItemCategory"] == 'RS' && $items['U_AppResBy'] == 'GA') {
                $count_item_ga_seragam++;
                $item_item_ga_seragam[] = $items['ItemCode'];
            }

            if ($items['SubGroup'] == '66202' && $items["ItemCategory"] == 'RS') {
                $count_item_ga++;
            }

            if (array_key_exists('U_AppResBy', $items)) {
                // valiate item GA yang ada di item group safety
                $item_groups_ga_in_safety[] = $items['U_AppResBy'];
                if ($items['U_AppResBy'] == 'GA') {
                    $item_item_ga_safety[] = $items['ItemCode'];
                    $count_item_ga_in_safety++;
                }

                // validate item safety
                $item_groups_safety[] = $items['U_AppResBy'];
                if ($items['U_AppResBy'] == 'HSE') {
                    $count_item_safety++;
                    $item_item_safety[] = $items['ItemCode'];
                }
            }
        }

        if (str($requester->WorkLocation)->contains(['IMIP MOROWALI'])) {
            if ($count_item_ga > 0) {
                if ($count_item_ga != count($item_groups_ga)) {
                    throw new \App\Exceptions\CustomException(
                        "Request barang GA tidak boleh di campur dengan barang yang lain! \n" . implode(", ", $item_item_ga)
                    );
                }
            }

            if ($count_item_ga_seragam > 0) {
                if ($count_item_ga_seragam != count($item_groups_ga_apd)) {
                    throw new \App\Exceptions\CustomException(
                        "Request barang GA APD tidak boleh di campur dengan barang yang lain! \n" . implode(", ", $item_item_ga_seragam)
                    );
                }
            }

            if ($count_item_ga_in_safety > 0) {
                if ($count_item_ga_in_safety != count($item_groups_ga_in_safety)) {
                    throw new \App\Exceptions\CustomException(
                        "Request barang GA in Safety tidak boleh di campur dengan barang yang lain! \n" . implode(", ", $item_item_ga_safety)
                    );
                }
            }

            if ($count_item_safety > 0) {
                if ($count_item_safety != count($item_groups_safety)) {
                    throw new \App\Exceptions\CustomException(
                        "Request barang Safety tidak boleh di campur dengan barang yang lain! \n" . implode(", ", $item_item_safety)
                    );
                }
            }
        }
    }

    public function validateWarehouse($form)
    {
        if (str($form["CompanyName"])->contains(["IMIP"])) {
            $companyHeader = "PT IMIP";
        } elseif (str($form["CompanyName"])->contains(["BDM"])) {
            $companyHeader = "PT BDM";
        } elseif (str($form["CompanyName"])->contains(["BDW"])) {
            $companyHeader = "PT BDW";
        }
        $user_id = auth()->user()->id;
        $service = new SapS4Service();
        $service->login();

        $user_wh = $form["WhsCode"];
        $result = [];

        $plant = $service->getPlanByCompany($companyHeader);

        $company = auth()->user()->company;
        $user = auth()->user();
        $resultWh = $service->getSloc(1, 100, null, $company, $user, $companyHeader);

        $arr = [];
        if ($resultWh) {
            foreach ($resultWh['DATA'] as $itemWh) {
                // if (Str::contains($itemWh['LGORT'], (array)$user_wh)) {
                // }
                $arr[] = [
                    "name" => $itemWh['LGORT'] . ' - ' . $itemWh['LGOBE'],
                    "whs_name" => $itemWh['LGOBE'],
                    "whs_code" => $itemWh['LGORT'],
                    "code" => $itemWh['LGORT'],
                    "plant" => $itemWh['WERKS'],
                ];
            }
        }

        $filtered = collect($arr);
        $resultUserWh = $filtered->whereIn('whs_code', $user_wh)->whereIn('plant', $plant);
        if (count(collect($resultUserWh)->values()->toArray()) < 1) {
            throw new \App\Exceptions\CustomException("Warehouse yang dipilih ${user_wh} tidak berada di ${companyHeader}. Silahkan ulangi memilih warehouse!");
        }
    }

    /**
     * Validates the details of the request.
     *
     * @param Collection $details The details of the request.
     * @param array $form The form data of the request.
     * @param \Illuminate\Http\Request $request The request object.
     * @throws \Exception If the request contains mixed IT and non-IT items, or if the request contains multiple IT items with different sub-groups.
     */
    public function validateDetails($details, $form, $request): void
    {
        $this->validateWarehouse($form);
        $this->validateSalesAndFuel($form);

        // $this->validateItemGroupBDM($details, $form);

        // if ((Str::contains($form['ItemType'], ['Ready Stock', 'Non Ready Stock']))) {
        if (Str::contains($request->user()->location, ['JAKARTA'])) {
            if ((Str::contains($form['ItemType'], ['Ready Stock', 'Non Ready Stock']))) {
                if (empty($form['CostType'])) {
                    throw new \App\Exceptions\CustomException('Cost Type cannot empty!');
                }
            }
        }

        if (empty($form['DocumentType'])) {
            throw new \App\Exceptions\CustomException("Document Type cannot empty!");
        }

        if ($form['DocumentType'] != 'Item') {
            if (count($details) > 0) {
                foreach ($details as $index => $items) {
                    $line = ($index + 1);

                    if (empty($items['ItemName'])) {
                        throw new \App\Exceptions\CustomException("Line $line: Service Name cannot empty!");
                    }

                    if (empty($items['UoMCode'])) {
                        throw new \App\Exceptions\CustomException("Line $line: UoM cannot empty!");
                    }
                }
            } else {
                throw new \App\Exceptions\CustomException("Document must have details!");
            }
        } else {
            if (count($details) > 0) {
                $this->validateGeneralReservation($details, $form, $request);

                $this->validateItemSpbAndNpb($details, $form);

                $this->validateItemItracc($details, $form);

                $this->validateItemIt($details, $form);

                $this->validateItemGaSafety($details, $form);
            } else {
                throw new \App\Exceptions\CustomException("Document must have details!");
            }
        }
    }

    public function validateItemGroupBDM($details, $form): void
    {
        $item_group_bdm = [];
        $count_item_bdm = 0;
        $include_item = [];
        foreach ($details as $index => $items) {
            if (array_key_exists("ItemGroup", $items)) {
                $item_group_bdm[] = $items['ItemGroup'];
                if (array_key_exists('SubGroup', $items)) {
                    if (str($items['SubGroup'])->contains($this->itemGroupSapBDM())) {
                        $count_item_bdm++;
                        $include_item[] = $items["ItemName"];
                    }
                }
            }
        }

        if (str($form["CompanyName"])->contains(["BDM", "BDW"])) {
            if ($count_item_bdm > 0) {
                if ($count_item_bdm != count($item_group_bdm)) {
                    throw new \App\Exceptions\CustomException("Request Item " . implode(", ", $include_item) . " tidak boleh dicampur dengan barang yang lain!");
                }
            }
        }
    }

    public function itemGroupSapBDM(): array
    {
        return [
            "61101",
            "61102",
            "61103",
            "61104",
            "61105",
            "61106",
            "61107",
            "61201",
            "61202",
            "61203",
            "61204",
            "61205",
            "61301",
            "61302",
            "61303",
            "61304",
            "61305",
            "61306",
            "62101",
            "62102",
            "62103",
            "62104",
            "62201",
            "62202",
            "63101",
            "63102",
            "63103",
            "63104",
            "63201",
            "63202",
            "63203",
            "64201",
            "64202",
            "64203",
            "64301",
            "64302",
            "64501",
            "64502",
            "65101",
            "65102",
            "65103",
            "65104",
            "65201",
            "65202",
            "65203",
            "65204",
            "65205",
            "65301",
            "65302",
            "65303",
            "66101",
            "66102",
            "66103",
            "66104",
            "66201",
            "66202",
            "67101",
            "67102",
            "67103",
            "67104",
            "67105",
            "67106",
            "67201",
            "67202",
            "67203",
            "68101",
            "68102",
            "68103",
            "68104",
            "68105",
            "68106",
            "69101",
            "69102",
            "89101",
            "89102",
            "89103",
            "89104",
            "89105",
            "89106",
            "89107",
            "89108",
            "89109",
            "89110",
            "89111",
            "89112",
            "89201",
            "89202",
            "89203",
            "89204",
            "89301",
            "89302",
            "89401",
            "89402",
            "89403",
            "89404",
            "89405",
            "89406",
            "99998",
            "99999",
        ];
    }
}
