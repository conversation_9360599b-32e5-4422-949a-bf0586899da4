<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\Stages
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Stages newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Stages newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Stages query()
 * @mixin \Eloquent
 */
class Stages extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $connection = 'sqlsrv2';
    protected $table = 'U_OWST';
    protected $primaryKey = 'U_WstCode';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
