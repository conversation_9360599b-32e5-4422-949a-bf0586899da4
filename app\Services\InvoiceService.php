<?php

namespace App\Services;

use App\Models\Document\Invoice;
use App\Models\Document\InvoiceItem;
use Illuminate\Support\Arr;
use App\Traits\ApiResponse;
use Carbon\Carbon;

class InvoiceService
{
    use ApiResponse;

	public function index($request)
	{
		// code...
	}

    /**
     * @return array
     */
    public function getForm()
    {
        $form = $this->form('invoices');
        $form['line_items'] = [];
        $form['company'] = 'PT IMIP';

        return $form;
    }

    /**
     * @param $request
     * @param $type
     * @param null $id
     * @return array
     */
    public function formData($request, $type): array
    {
        $request->request->remove('attachment');
        $request->request->remove('user_create');
        $request->request->remove('default_currency_code');
        $request->request->remove('default_currency_symbol');
        $request->request->remove('line_items');
        $request->request->remove('id');
        $request->request->remove('paper_no');
        $request->request->remove('name');
        $request->request->remove('document_type');

        $data = $request->all();
        $data['customer'] = is_array($data['customer']) ? $data['customer']['CardName'] : $data['customer'];
        $data['notes'] = (isset($data['notes'])) ? $data['notes'] : '';

        Arr::forget($data, 'line_items');
        Arr::forget($data, 'document');

        if ($type == 'store') {
            $data['created_by'] = auth()->user()->id;
            $data['created_at'] = Carbon::now();
        } else {
            $data['created_at'] = Carbon::parse($request->created_at);
            $data['updated_at'] = Carbon::now();
        }

        return $data;
    }

    public function processDetails($document, $line_items)
    {
    	foreach ($line_items as $key => $value) {
    		$id = array_key_exists('id', $value) ? $value['id'] : 0;
    		$item = InvoiceItem::find($id);
    		if ($item) {
    			$item->invoice_id = $document->id;
    			$item->description = $value['description'];
    			$item->currency = $value['currency'];
    			$item->amount = $value['amount'];
    			$item->save();
    		} else {
    			$item = InvoiceItem::create([
    				'invoice_id' => $document->id,
    				'description' => $value['description'],
    				'currency' => $value['currency'],
    				'amount' => $value['amount'],
                    'created_by' => auth()->user()->id
    			]);
    		}
    	}
    }


    public function validateDetails($line_items)
    {
    	foreach ($line_items as $key => $value) {
    		if (empty($value['description'])) {
    			throw new \Exception('Description cannot empty!');
    		}

    		if (empty($value['currency'])) {
    			throw new \Exception('Currency cannot empty!');
    		}

    		if (empty($value['amount'])) {
    			throw new \Exception('Amount cannot empty!');
    		}
    	}
    }
}
