<?php

namespace App\Models\Enviro;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Enviro\EnviroActivity
 *
 * @property int $id
 * @property int $sub_role_id
 * @property int $created_by
 * @property int|null $updated_by
 * @property string|null $act_date
 * @property string|null $problem_date
 * @property string|null $solution_date
 * @property string|null $problem_desc
 * @property string|null $solution_desc
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity query()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereActDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereProblemDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereProblemDesc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereSolutionDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereSolutionDesc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereSubRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroActivity whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class EnviroActivity extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
