<?php

namespace App\Jobs;

use App\Models\Common\Attachment;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use App\Traits\DigitalSignHelper;
use App\Traits\DocumentHelper;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class ProcessSubmitDigitalSign implements ShouldQueue
{
    // public $timeout = 0;

    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use AppConfig;
    use ApiResponse;
    use DocumentHelper;
    use DigitalSignHelper;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 1;
    protected $document;
    protected $fileName;
    protected $userId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($document, $fileName, $userId)
    {
        $this->document = $document;
        $this->fileName = $fileName;
        $this->userId = $userId;
        $this->onQueue('processDocument');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        sleep(5);
        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');

        $document = $this->document;
        if ($document->digisign_coordinate == 'Y') {
            // $fileName = $this->fileName;
            $fileName = Attachment::where('source_id', $document->id)
                ->where('type', 'peruri')
                ->first();

            $document_id = $document->id;

            $dataCheck = [
                'document_id' => $document->id,
                'name' => 'Submit Digital Sign',
            ];
            try {
                // $service = new ApprovalPrivyService();
                // $service->login();
                // $service->digitalSign($document, $fileName, $dataCheck, $this->batch());
                $this->digitalSign($document, $fileName, $dataCheck, $this->batch());
            } catch (\Exception $exception) {
                $dataUpdate = [
                    'batch_id' => $this->batch()->id,
                    'status' => 'Processing ' . $this->batch()->progress() . '%',
                    'callback_message' => 'Failed sign document: ' . $exception->getMessage(),
                    'callback_trace' => ''
                ];
                Log::info('error digital sign', [
                    'trace' => $exception->getTraceAsString()
                ]);
                $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                $this->fail($exception);
                throw new \Exception('E-DIGITALSIGN: ' . $exception->getMessage(), 1);
            }
        }
    }
}
