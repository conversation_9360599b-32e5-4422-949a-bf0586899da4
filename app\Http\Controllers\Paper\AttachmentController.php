<?php

namespace App\Http\Controllers\Paper;

use App\Http\Controllers\Controller;
use App\Models\Common\Attachment;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class AttachmentController extends Controller
{
    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $modelData = (object) $request->ModelData;
        $type = $modelData->type;
        if ($type == 'eform') {
            $attachment = Attachment::where('str_url', '=', $modelData->source_id);
        } else {
            $attachment = Attachment::where('source_id', '=', (int)$modelData->source_id);
        }
        return $this->success([
            'rows' => $attachment->where('type', $type)->get(),
            'total' => $attachment->where('type', $type)->count()
        ]);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // $validator = Validator::make($request->all(), [
        //     'file.*' => 'required|mimes:pdf,docx,docx,png,jpg,jpeg|max:8048',
        // ]);

        // if ($validator->fails()) {
        //     return $this->error($validator->errors(), '422');
        // }
        $modelData = (object) $request->ModelData;
        try {
            if ($modelData->type == 'reservation' || $modelData->type == 'reservation_header') {
                $attachment = Attachment::where('type', '=', $modelData->type)
                    ->where('source_id', '=', $modelData->source_id)
                    ->first();
                if ($attachment) {
                    return $this->error('Row must have 1 attachment!');
                }
            }
            $data_file = $request->file('file');

            $extension = $data_file->getClientOriginalExtension();

            // $destination_path = public_path('/Attachment/docs');

            // if (!file_exists($destination_path)) {
            //     if (!mkdir($destination_path, 0777, true) && !is_dir($destination_path)) {
            //         throw new \RuntimeException(
            //             sprintf(
            //                 'Directory "%s" was not created',
            //                 $destination_path
            //             )
            //         );
            //     }
            // }

            $origin_name = $data_file->getClientOriginalName();
            $name_no_ext = strtoupper(Str::slug(pathinfo($origin_name, PATHINFO_FILENAME))) . time();
            $file_name = $name_no_ext . '.' . $extension;
            $destination_path = custom_disk_path("/Attachment/docs", "sftp");
            $data_file->storeAs($destination_path, $file_name, 'sftp');
            // $data_file->move($destination_path, $file_name);

            $data = [
                'file_name' => $file_name,
                'file_path' => config('app.url') . '/Attachment/docs/' . $file_name,
                'source_id' => (int)$request->source_id,
                'str_url' => $request->source_id,
                'created_by' => $request->user()->id,
                'type' => $request->type
            ];

            $attach = Attachment::create($data);

            $count_attachment = Attachment::where('type', '=', $request->type)
                ->where('source_id', '=', (int)$request->source_id)
                ->count();

            return $this->success([
                'count' => $count_attachment
            ], 'Document Uploaded!');
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), '422');
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request)
    {
        try {
            $attachment = Attachment::where('id', '=', $request->id)
                ->first();

            if ($attachment) {
                if ($attachment->created_by != $request->user()->id) {
                    return $this->error('Not authorized to delete this file!');
                }

                $file = '/Attachment/docs/' . $attachment->file_name;
                custom_disk_delete($file);
                // unlink(public_path() . $file);
                Attachment::where('id', '=', $attachment->id)
                    ->delete();

                return $this->success('', 'File deleted!');
            } else {
                return $this->error('File not found', 422);
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 422);
        }
    }
}
