<?php

namespace App\Http\Controllers\Master;

use App\Models\Document\DocumentSubType;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\DocumentService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DocumentSubTypeController extends Controller
{
    public function index(Request $request)
    {
        $service  = new DocumentService();
        $service->peruriLogin();
        $data = DocumentSubType::select('id', 'document_type', 'name')
            ->orderBy('document_type')
            ->get();
        $docment_type = collect($service->getDocumentType())->pluck('nama');
        
        return $this->success([
            'rows' => $data,
            'document_type' => $docment_type,
            'header' => ['Id', 'Document Type', 'Document Sub Type'],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        try {
            foreach ($details as $detail) {
                if (empty($detail['document_type'])) {
                    return $this->error('document type cannot empty', '422');
                }
                $id = (array_key_exists('id', $detail)) ? $detail['id'] : null;
                $brand = DocumentSubType::where('id', '=', $id)->first();
                if (!$brand) {
                    $brand = new DocumentSubType();
                }
                $brand->document_type = $detail['document_type'];
                $brand->name = $detail['name'];
                $brand->save();
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            DocumentSubType::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
