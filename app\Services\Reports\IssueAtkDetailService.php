<?php

namespace App\Services\Reports;

class IssueAtkDetailService
{
    public function header()
    {
        return [
            'Tanggal GI',
            'Item Code',
            'Item Name',
            'Qty',
            'UoM',
            'Department',
            'Notes'
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "DocDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Dscription",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Quantity",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "unitMsr",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Department",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Comments",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                // $value['DocNum'],
                $value['DocDate'],
                $value['ItemCode'],
                $value['Dscription'],
                // $value['WhsCode'],
                $value['Quantity'],
                $value['unitMsr'],
                // $value['U_REQ_NO'],
                $value['Department'],
                $value['Comments'],
                // $value['ResvDocNum'],
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
