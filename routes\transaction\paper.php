<?php

use App\Http\Controllers\Paper\PaperController;
use Illuminate\Support\Facades\Route;

Route::get('paper/print', [PaperController::class, 'print']);
Route::get('reference-no/{username}', [PaperController::class, 'referenceNo']);

Route::apiResource('paper', PaperController::class)->names([
    'index' => 'data-paper.index',
    'store' => 'data-paper.store',
    'show' => 'data-paper.show',
    'update' => 'data-paper.update',
    'destroy' => 'data-paper.destroy'
]);