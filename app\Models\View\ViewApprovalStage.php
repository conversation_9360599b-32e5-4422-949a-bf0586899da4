<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\ViewApprovalStage
 *
 * @property-read \App\Models\View\ViewEmployee|null $employee
 * @method static \Illuminate\Database\Eloquent\Builder|ViewApprovalStage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewApprovalStage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewApprovalStage query()
 * @mixin \Eloquent
 */
class ViewApprovalStage extends Model
{
    protected $connection = 'sqlsrv2';
    protected $table = 'vw_approver_gadocuments';

    public function employee()
    {
        return $this->belongsTo(ViewEmployee::class, 'ApproverCode', 'EmployeeCode');
    }
}
