<?php

namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Approval\BatchApproval
 *
 * @property int $id
 * @property int $document_id
 * @property string $batch_id
 * @property string $name
 * @property string $status
 * @property string $callback_message
 * @property string $callback_trace
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval query()
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereCallbackMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereCallbackTrace($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|BatchApproval whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class BatchApproval extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
}
