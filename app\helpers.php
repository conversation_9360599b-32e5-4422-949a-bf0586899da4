<?php

use App\Jobs\RemoveAttachment;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


if (!function_exists('getMonthYearRange')) {
    function getMonthYearRange($dateFrom, $dateTo)
    {
        // Parse the dates
        $startDate = Carbon::parse($dateFrom);
        $endDate = Carbon::parse($dateTo);

        // Generate the period
        $period = CarbonPeriod::create($startDate, '1 month', $endDate);

        // Collect month and year strings
        $months = [];
        foreach ($period as $date) {
            $months[] = $date->format('F Y');
        }

        return $months;
    }
}

if (!function_exists('custom_disk_path')) {
    /**
     * Generates the custom disk path for a given path and disk name.
     *
     * @param string $path The path to generate the disk path for.
     * @param string $diskName The name of the disk. Defaults to 'sftp'.
     * @return string The generated disk path.
     */
    function custom_disk_path($path, $diskName = 'sftp')
    {
        return Storage::disk($diskName)->path($path);
    }
}

if (!function_exists('create_file_delete_job')) {
    /**
     * Create a job to delete a file.
     *
     * @param string $filePath The path of the file to be deleted
     * @param string $diskName The name of the disk where the file is located (default: 'sftp')
     */
    function create_file_delete_job($filePath, $diskName = 'sftp', $destinationPath = null)
    {
        if ($destinationPath) {
            // Get the directory path excluding the file name
            $directoryPath = dirname(public_path($destinationPath));
        } else {
            // Get the directory path excluding the file name
            $directoryPath = dirname(public_path($filePath));
        }

        // Create the directory if it doesn't exist
        if (!file_exists($directoryPath)) {
            mkdir($directoryPath, 0777, true); // Third argument ensures recursive directory creation
        }
        if ($destinationPath) {
            file_put_contents(public_path($destinationPath), custom_disk_get($filePath));
            RemoveAttachment::dispatch(public_path($destinationPath))->delay(now()->addMinutes(15));
        } else {
            file_put_contents(public_path($filePath), custom_disk_get($filePath));
            RemoveAttachment::dispatch(public_path($filePath))->delay(now()->addMinutes(20));
        }
    }
}

if (!function_exists('custom_disk_put')) {
    /**
     * Writes the given contents to the specified path on the custom disk.
     *
     * @param string $path The path on the disk where the contents will be written.
     * @param mixed $contents The contents to be written to the disk.
     * @param string $diskName The name of the custom disk. Defaults to 'sftp'.
     * @return mixed The result of the put operation on the disk.
     */
    function custom_disk_put($path, $contents, $diskName = 'sftp')
    {
        if ($diskName == 'sftp') {
            return Storage::disk($diskName)->put(config('filesystems.disks.sftp.root'). $path, $contents);
        }
        return Storage::disk($diskName)->put($path, $contents);
    }
}

if (!function_exists('custom_disk_relative_path')) {
    /**
     * Generates a custom disk relative path for the given path and disk name.
     *
     * @param string $path The full path to the file.
     * @param string $diskName The name of the disk. Defaults to 'sftp'.
     * @return string The custom disk relative path.
     */
    function custom_disk_relative_path($path, $diskName = 'sftp')
    {
        // Get the root path of the sftp bucket (it will be empty string as sftp doesn't have a local root path)
        $rootPath = '';

        // Extract the relative path by removing the root path from the full path
        $relativePath = Str::after($path, $rootPath);
        return Storage::disk($diskName)->path($path);
    }
}

if (!function_exists('custom_disk_check')) {
    /**
     * Checks if a file exists in a custom disk.
     *
     * @param string $path The path of the file to check.
     * @param string $diskName The name of the disk to use. Defaults to 'sftp'.
     * @return bool Returns true if the file exists, false otherwise.
     */
    function custom_disk_check($path, $diskName = 'sftp')
    {
        if($diskName == 'sftp') {
            return Storage::disk($diskName)->exists(config('filesystems.disks.sftp.root').  $path);
        }
        return Storage::disk($diskName)->exists($path);
    }
}

if (!function_exists('custom_disk_files')) {
    /**
     * A function that retrieves files from a custom disk.
     *
     * @param string $directory The directory to retrieve files from.
     * @param string $diskName The name of the disk to use (default is 'sftp').
     * @return array The list of files in the specified directory.
     */
    function custom_disk_files($directory, $diskName = 'sftp')
    {
        return Storage::disk($diskName)->files($directory);
    }
}

if (!function_exists('custom_disk_get')) {
    /**
     * Retrieves the contents of a file from a custom disk.
     *
     * @param string $path The path to the file.
     * @param string $diskName The name of the disk. Defaults to 'sftp'.
     * @throws Exception If the file does not exist or an error occurs.
     * @return string The contents of the file.
     */
    function custom_disk_get($path, $diskName = 'sftp')
    {
        // return Storage::disk($diskName)->get($path);
        if($diskName == 'sftp') {
            return Storage::disk($diskName)->get(config('filesystems.disks.sftp.root'). $path);
        }
        return Storage::disk($diskName)->get($path);
    }
}

if (!function_exists('custom_disk_make_dir')) {
    /**
     * Creates a directory at the specified path using the specified disk.
     *
     * @param string $path The path where the directory should be created.
     * @param string $diskName The name of the disk to use. Defaults to 'sftp'.
     * @return bool Returns true if the directory was created successfully, false otherwise.
     */
    function custom_disk_make_dir($path, $diskName = 'sftp')
    {
        return Storage::disk($diskName)->makeDirectory($path);
    }
}

if (!function_exists('custom_disk_delete')) {
    /**
     * custom_disk_delete function deletes a file from the specified disk.
     *
     * @param string $path The path of the file to be deleted.
     * @param string $diskName The name of the disk where the file is located. Default is 'sftp'.
     * @return bool True on success, false on failure.
     */
    function custom_disk_delete($path, $diskName = 'sftp')
    {
        if($diskName == 'sftp') {
            return Storage::disk($diskName)->delete(config('filesystems.disks.sftp.root') . $path);
        }
        return Storage::disk($diskName)->delete($path);
    }
}

if (!function_exists('custom_disk_move')) {
    /**
     * A function to move a file from one location to another using a specific disk.
     *
     * @param mixed $from The source location of the file.
     * @param mixed $to The destination location of the file.
     * @param string $diskName The name of the disk to use for the file operation. Default is 'sftp'.
     * @return bool Returns true on success, false on failure.
     */
    function custom_disk_move($from, $to, $diskName = 'sftp')
    {
        return Storage::disk($diskName)->move($from, $to);
    }
}

if (!function_exists('custom_disk_delete_dir')) {
    /**
     * Deletes a directory and its contents from the specified disk.
     *
     * @param string $path The path of the directory to be deleted.
     * @param string $diskName The name of the disk to use. Defaults to 'sftp'.
     * @throws Exception If the directory cannot be deleted.
     * @return bool True if the directory and its contents were successfully deleted, false otherwise.
     */
    function custom_disk_delete_dir($path, $diskName = 'sftp')
    {
        return Storage::disk($diskName)->deleteDirectory($path);
    }
}
if (!function_exists('custom_disk_download')) {
    /**
     * Performs a custom disk download using the specified disk.
     *
     * @param mixed $path The file path to download.
     * @param mixed|null $name The name to use for the downloaded file.
     * @param array $headers Additional headers to include in the download.
     * @param string $diskName The name of the disk to use for the download.
     * @return mixed
     */
    function custom_disk_download($path, $name = null, array $headers = [], $diskName = 'sftp')
    {
        return Storage::disk($diskName)->download($path, $name = null, $headers);
    }
}


if (!function_exists('custom_disk_url')) {
    /**
     * Get the URL for the specified file path on the custom disk.
     *
     * @param string $path The path of the file
     * @param string $diskName The name of the disk (default: 'sftp')
     * @return string The URL of the file
     */
    function custom_disk_url($path,  $diskName = 'sftp')
    {
        return Storage::disk($diskName)->url($path);
    }
}
