<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Jobs\RemoveAttachment;
use App\Models\Approval\Approval;
use App\Models\Approval\ApprovalApprover;
use App\Models\Approval\ApprovalRule;
use App\Models\Common\Attachment;
use App\Models\Document\Customer;
use App\Models\Document\Document;
use App\Models\Document\DocumentCoordinate;
use App\Models\Document\DocumentSubType;
use App\Models\View\ViewEmployee;
use App\Notifications\ApprovalMeteraiRequest;
use App\Services\ApprovalService;
use App\Services\DocumentDataService;
use App\Services\DocumentService;
use App\Traits\AppConfig;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Madnest\Madzipper\Madzipper as Zipper;
use Illuminate\Support\Str;
use RuntimeException;

class DocumentController extends Controller
{
    use AppConfig;

    public DocumentService $service;

    public function __construct(DocumentService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $documentData = new DocumentDataService();

        $status = isset($request->searchStatus) ? (string) $request->searchStatus : 'Draft';
        $status = $documentData->documentStatus($status);

        $date_filter = $documentData->dataFilter();

        // $startOfYear = $now->startOfYear();
        $startOfYear = Carbon::create(date('Y'), '01')->startOfMonth()->format('Y-m-d');
        $now = Carbon::now();
        $endOfYear = $now->endOfYear()->format('Y-m-d');

        // $this->service->peruriLogin();

        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;

        $sorts = isset($request->sortBy[0]) ? json_decode($request->sortBy[0])->key : 'document_date';
        $order = isset($request->sortBy[0]) ? json_decode($request->sortBy[0])->order : 'desc';

        $search_item = isset($request->searchItem) ? (string) $request->searchItem : '';
        $search = isset($request->searchValue) ? (string) $request->searchValue : '';
        $type = isset($request->type) ? (string) $request->type : '';
        $dateFrom = isset($request->dateFrom) ? (string) $request->dateFrom : $startOfYear;
        $dateTo = isset($request->dateTo) ? (string) $request->dateTo : $endOfYear;
        $documentType = isset($request->documentType) ? (string) $request->documentType : 'invoice';
        $offset = $pages;

        $query = Document::select(
            'documents.*',
            'document_number as paper_no',
            'document_sub_types.name as document_sub_type_name'
        )
            ->leftJoin('customers', 'customers.id', 'documents.customer_id')
            ->leftJoin('document_sub_types', 'document_sub_types.id', 'documents.document_sub_type_id')
            ->when($request, function ($query) use ($request, $dateFrom, $dateTo) {
                if (isset($request->dateFrom)) {
                    $query->whereBetween('document_date', [$dateFrom, $dateTo]);
                }
            })
            // ->where('status', 'LIKE', '%' . $status . '%')
            ->whereIn('status', $status)
            ->where('documents.document_type', $documentType)
            ->orderBY($sorts, $order)
            ->with(['attachment', 'userCreate', 'coordinate', 'approver.user']);

        if ($sorts != 'external_document_number') {
            $query = $query->orderBY('external_document_number', $order);
        }


        if (!$request->user()->hasAnyRole(['Superuser'])) {
            $contains = ['6071', '850', '699', '4321', '15385', '15679', '16862'];
            $contains2 = ['4799', '854']; // davina naga(88104590), jusup kristy(88101360)
            // $contains3 = ['4799', '13125', '13129']; // davina naga(88104590), ADITYAS BELLA OCKTAVIANA(88104954), NANDYA ANDILA AGUSTIN (88105172)
            $contains3 = ['4799', '13125', '13129']; // davina naga(88104590), ADITYAS BELLA OCKTAVIANA(88104954), NANDYA ANDILA AGUSTIN (88105172)
            $contains4 = ['7204', '6385']; // Ritalia, juliana
            if (Str::contains($request->user()->id, $contains)) {
                $query = $query->whereIn('created_by', $contains);
            } elseif (Str::contains($request->user()->id, $contains4)) {
                $query = $query->whereIn('created_by', $contains4);
            } else if ($request->user()->id == '854') { // jusup kristy(88101360) can see davina naga(88104590) & jusup kristy(88101360)
                $query = $query->whereIn('created_by', $contains2);
            }
            // else if (Str::contains($request->user()->id , $contains3)) {
            else if ($request->user()->id == '4799') {
                $query = $query->whereIn('created_by', $contains3);
            } else {
                $query = $query->where('created_by', $request->user()->id);
            }
        }

        if ($search_item == 'Document Number') {
            $query = $query->where('document_number', 'LIKE', '%' . $search . '%');
        } elseif ($search_item == 'Document Type') {
            $query = $query->where('type', 'LIKE', '%' . $search . '%');
        } elseif ($search_item == 'External Document Number') {
            $query = $query->where('external_document_number', 'LIKE', '%' . $search . '%');
        } elseif ($search_item == 'Created By') {
            $query = $query->where('created_name', 'LIKE', '%' . $search . '%');
        } elseif ($search_item == 'Remark') {
            $query = $query->where('remark', 'LIKE', '%' . $search . '%');
        } elseif ($search_item == 'Reference Number') {
            $query = $query->where('reference_number', 'LIKE', '%' . $search . '%');
        }

        $total = $query->count();

        $data = $query
            ->paginate($row_data)
            ->items();

        // $document_status = array_merge(['All'], );
        $document_status = Document::select('status')->distinct()->pluck('status');
        $collect = collect($document_status);
        $collect->push('all');

        $document_type = $this->service->getDocumentType();
        // $document_type = session('document_type');

        $customer = $documentData->customer();

        $sign_payment = $documentData->signPayment();

        return $this->success([
            'rows' => $data,
            'total' => $total,
            'form' => $this->service->getForm($documentType),
            'document_type' => $document_type,
            'date_filter' => $date_filter,
            'sign_payment' => $sign_payment,
            'company' => ['PT IMIP', 'PT SMI', 'PT MMM', 'PT BDM'],
            'signer' => ['HAMID MINA', 'ADWIN SUDARNI', 'HASRAT', 'MIKHAEL SE', 'WILLIAM THEO'],
            'document_status' => [
                ucfirst('all'),
                ucfirst('draft'),
                ucfirst('pending'),
                ucfirst('approved'),
                ucfirst('canceled / rejected'),
            ],
            'customer' => $customer,
            'search_item' => ['Document Number', 'Document Type', 'External Document Number', 'Remark', 'Reference Number'],
        ]);
    }

    /**
     * Check if an external document exists and return its status and message as a JSON response.
     *
     * @param Request $request The HTTP request containing the document number to check.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the status and message.
     */
    public function checkExternalDocument(Request $request)
    {
        $external_document_number = $request->document;

        $document = Document::where('external_document_number', $external_document_number)
            ->whereNotNull('external_document_number')
            ->first();

        // return response()->json($document);

        if ($document) {
            return response()->json([
                'status' => true,
                'message' => 'Document exists!'
            ]);
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Document not found!'
            ]);
        }
    }

    /**
     * @param $type
     * @return JsonResponse
     */
    public function getSubType($type): JsonResponse
    {
        $data = DocumentSubType::where('document_type', $type)
            ->select('id', 'name')
            ->get();

        return $this->success($data);
    }
    /**
     * Validates and stores a document with attachments.
     *
     * @param Request $request The HTTP request.
     * @throws Exception If an error occurs while processing the request.
     * @return JsonResponse The result of the operation.
     */

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'files.*' => 'required|mimetypes:application/pdf|max:9048',
            'data' => 'required'
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors());
        }
        // $request->validate([
        //     'files' => 'required|mimetypes:application/pdf|max:9048',
        //     'data' => 'required'
        // ]);

        if (!$request->hasFile('files')) {
            return $this->error('File is required!');
        }
        $files = $request->file('files');


        DB::beginTransaction();
        try {
            // return $this->error('', 422, collect(json_decode($request->data))['batch_id']);

            $count_success = 0;
            foreach ($files as $file) {
                if (empty($request->data)) {
                    return $this->error('Data is required!');
                }

                $data = collect(json_decode($request->data));

                if (empty($data['type'])) {
                    return $this->error('Type is required!');
                }

                if ($data->has('digisign_id') && $data->has('vis_digisign_llx')) {
                    if (!empty($data['digisign_id']) && !empty($data['vis_digisign_llx'])) {
                        $data["vis_digisign_urx"] = ($data['digisign_id'] == 'combine') ? ($this->service->pointToPixel($data['vis_digisign_llx']) - 40) : $this->service->pointToPixel($data['vis_digisign_llx']);
                    }
                }

                if ($data->has('digisign_top')) {
                    if (!empty($data['digisign_top'])) {
                        $data["vis_digisign_ury"] = $this->service->pointToPixel($data['digisign_top']);
                    }
                }

                if ($data->has('meterai_left')) {
                    if (!empty($data['meterai_left'])) {
                        $data["vis_urx"] = $this->service->pointToPixel($data['meterai_left']);
                    }
                }

                if ($data->has('meterai_top')) {
                    if (!empty($data['meterai_top'])) {
                        $data["vis_ury"] = $this->service->pointToPixel($data['meterai_top']);
                    }
                }



                // if (empty($data['customer_name'])) {
                //     return $this->error('Customer is required!');
                // }

                if ($request->user()->hasAnyRole(['Admin E-Meterai'])) {
                    if (empty($data['document_sub_type_id']) && $data['internal_document'] == 'N') {
                        if (Str::of($data['type'])->contains(['Dokumen Transaksi'])) {
                            return $this->error('Sub Type is required!');
                        }
                    }
                }

                $filtered = $data->except([
                    'default_currency_code',
                    'default_currency_symbol',
                    'id',
                    'paper_no',
                    'document_sub_type_name',
                ]);
                // save document

                $origin_name = $file->getClientOriginalName();

                $external_document_number = $data['external_document_number'];
                if (empty($data['external_document_number'])) {
                    $external_document_number = pathinfo($origin_name, PATHINFO_FILENAME);
                }

                // return $this->error($data['external_document_number']);

                $check_document = Document::where('external_document_number', $external_document_number)
                    ->whereNotIn('status', ['canceled', 'rejected'])
                    ->count();

                if ($check_document > 0) {
                    return $this->error('Nama file sudah pernah dipakai! ' . $external_document_number);
                }

                $filtered = $filtered->merge(['external_document_number' => $external_document_number]);

                // return $this->error('', 422, $this->service->formData($filtered, 'store'));
                $document = Document::create($this->service->formData($filtered, 'store'));

                // process attachment
                $extension = $file->getClientOriginalExtension();

                // $destination_path = public_path('/Attachment/docs');

                // check if destination path exist, if not create one
                // if (!file_exists($destination_path)) {
                //     if (!mkdir($destination_path, 0777, true) && !is_dir($destination_path)) {
                //         throw new RuntimeException(
                //             sprintf(
                //                 'Directory "%s" was not created',
                //                 $destination_path
                //             )
                //         );
                //     }
                // }

                $name_no_ext = strtoupper(Str::slug(pathinfo($origin_name, PATHINFO_FILENAME))) . '_token_' . Str::random(8);
                $file_name = $name_no_ext . '.' . $extension;

                $destination_path = custom_disk_path("/Attachment/docs", "sftp");
                $data_file = "/Attachment/docs/" . $file_name;
                if (custom_disk_check("/Attachment/docs/" . $file_name)) {
                    custom_disk_delete($data_file);
                }
                $file->storeAs($destination_path, $file_name, 'sftp');


                $url = 'https://beckendcore-api.imip.co.id';

                // $data_file = public_path('Attachment/docs/' . $file_name);
                // if (file_exists($data_file)) {
                //     unlink($data_file);
                // }

                // $file->move($destination_path, $file_name);

                // data attachment
                $data = [
                    'file_name' => $file_name,
                    'file_path' => $url . '/Attachment/docs/' . $file_name,
                    'source_id' => (int) $document->id,
                    'str_url' => $document->id,
                    'created_by' => $request->user()->id,
                    'type' => 'peruri'
                ];

                // save attachment
                $attach = Attachment::create($data);

                $count_success++;
            }

            DB::commit();
            if ($count_success > 0) {
                // Artisan::call('cache:clear');
                // Artisan::call('config:cache');
                return $this->success([
                    'document' => $document
                ], 'Document saved!');
            }

            // Log::info('count success', [
            //     $count_success
            // ]);
            return $this->error('There are no files to upload!');
        } catch (Exception $exception) {
            DB::rollBack();
            // Log::info('error submit e-meterai', [
            //     'message' => $exception->getMessage(),
            //     'trace' => $exception->getTrace()
            // ]);
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateCoordinate(Request $request): JsonResponse
    {
        try {
            $arrayMaterai = $request->arrayMaterai;
            $arrayEsign = $request->arrayEsign;
            $id = $request->id;
            $document = Document::find($id);
            $document->meterai_coordinate = null;
            $document->meterai_id = null;
            $document->meterai_stamp_type = null;
            $document->meterai_page_index = null;
            $document->meterai_top = null;
            $document->meterai_left = null;
            $document->meterai_width = null;
            $document->meterai_height = null;
            $document->materai_page = null;
            $document->vis_llx = null;
            $document->vis_lly = null;
            $document->vis_urx = null;
            $document->vis_ury = null;
            $document->digisign_coordinate = null;
            $document->digisign_id = null;
            $document->digisign_stamp_type = null;
            $document->digisign_page_index = null;
            $document->digisign_top = null;
            $document->digisign_left = null;
            $document->digisign_width = null;
            $document->digisign_height = null;
            $document->sign_page = null;
            $document->vis_digisign_llx = null;
            $document->vis_digisign_lly = null;
            $document->vis_digisign_urx = null;
            $document->vis_digisign_ury = null;
            $document->save();

            $this->processCoordinate($arrayMaterai, $arrayEsign, $document);

            return $this->success('Data Submitted');
        } catch (Exception $exception) {
            return $this->error($exception->getMessage(), '422', [$exception->getTrace()]);
        }
    }

    /**
     * @param $arrayMaterai
     * @param $arrayEsign
     * @param $document
     * @return void
     */
    protected function processCoordinate($arrayMaterai, $arrayEsign, $document)
    {
        $service = new DocumentService();
        $document = Document::find($document->id);
        $totalMeterai = count($arrayMaterai) - 1;
        $totalSign = count($arrayEsign) - 1;
        // throw new \Exception($totalSign, 1);
        $pageHeight = $service->pointToPixel(841.7);
        if (count($arrayEsign) > 0) {
            $dataMeterai = ($document->document_type == 'invoice') ? $arrayMaterai[$totalMeterai][0] : $arrayMaterai[$totalMeterai][0];
            if (count($arrayMaterai[0]) > 0) {
                $dataMeterai = ($document->document_type == 'invoice') ? $arrayMaterai[$totalMeterai][0] : $arrayMaterai[$totalMeterai][0];
            }
            $dataSign = ($document->document_type == 'invoice') ? $arrayEsign[$totalSign][0] : $arrayEsign[$totalSign][0];

            foreach ($arrayEsign as $key => $sign) {
                foreach ($sign as $value) {
                    DocumentCoordinate::updateOrCreate([
                        'document_id' => $document->id,
                        'sign_page' => ($value['pageIndex'] + 1),
                        'materai_page' => ($value['pageIndex'] + 1),
                    ], [
                        'digisign_coordinate' => $value['digisign_coordinate'],
                        'digisign_id' => $value['digisign_id'],
                        'digisign_stamp_type' => $value['digisign_stamp_type'],
                        'digisign_page_index' => $value['digisign_page_index'],
                        'digisign_top' => $value['digisign_top'],
                        'digisign_left' => $value['digisign_left'],
                        'digisign_width' => $value['digisign_width'],
                        'digisign_height' => $value['digisign_height'],
                        'vis_digisign_llx' => $value['digisign_left'],
                        'vis_digisign_lly' => (841.7 - $value['digisign_top'] - $value['digisign_height']),
                        // 'vis_digisign_urx' => ($value['digisign_left'] + $value['digisign_width']),
                        // 'vis_digisign_ury' => (841.7 - $value['digisign_top']),
                        'vis_digisign_urx' => ($dataSign['digisign_id'] == 'combine') ? ($service->pointToPixel($value['visLLX'] - 30)) : $service->pointToPixel($value['visLLX']),
                        'vis_digisign_ury' => $service->pointToPixel($value['digisign_top']),
                    ]);
                }
            }

            if (array_key_exists('digisign_stamp_type', $dataSign)) {
                $document->digisign_coordinate = $dataSign['digisign_coordinate'];
                $document->digisign_id = $dataSign['digisign_id'];
                // $document->digisign_color = $dataSign['digisign_color'];
                $document->digisign_stamp_type = $dataSign['digisign_stamp_type'];
                $document->digisign_page_index = $dataSign['digisign_page_index'];
                $document->digisign_top = $dataSign['digisign_top'];
                $document->digisign_left = $dataSign['digisign_left'];
                $document->digisign_width = $dataSign['digisign_width'];
                $document->digisign_height = $dataSign['digisign_height'];
                $document->sign_page = $dataSign['pageIndex'] + 1;

                // $document->vis_digisign_llx = $dataSign['visLLX'];
                // $document->vis_digisign_lly = $dataSign['visLLY'];
                // $document->vis_digisign_urx = $dataSign['visLLX'];
                // $document->vis_digisign_ury = $dataSign['visLLY'];

                $document->vis_digisign_llx = $dataSign['visLLX'];

                if (count($arrayMaterai[0]) > 0) {
                    if ($totalMeterai == $totalSign) {
                        if ($dataMeterai['meterai_id'] == 'combine' || $dataSign['digisign_id'] == 'combine') {
                            $document->vis_digisign_lly = $dataSign['visLLY'];
                            $document->vis_digisign_urx = $service->pointToPixel($dataSign['visLLX'] - 30);
                            $document->vis_digisign_ury = $service->pointToPixel($dataSign['digisign_top']);
                        } else {
                            $document->vis_digisign_lly = $dataSign['visLLY'];
                            $document->vis_digisign_urx = $service->pointToPixel($dataSign['visLLX']);
                            $document->vis_digisign_ury = $service->pointToPixel($dataSign['digisign_top']);
                        }
                    } else {
                        $document->vis_digisign_lly = $dataSign['visLLY'] + 50;
                        $document->vis_digisign_urx = $service->pointToPixel($dataSign['visLLX']);
                        $document->vis_digisign_ury = $service->pointToPixel($dataSign['digisign_top']);
                    }
                } else {
                    $document->vis_digisign_lly = $dataSign['visLLY'];
                    $document->vis_digisign_urx = $service->pointToPixel($dataSign['visLLX']);
                    $document->vis_digisign_ury = $service->pointToPixel($dataSign['digisign_top']);
                }
            }
        }

        if (count($arrayMaterai[0]) > 0) {
            foreach ($arrayMaterai as $key => $meterai) {
                // throw new \Exception(json_encode($value[0]), 1);
                foreach ($meterai as $value) {
                    DocumentCoordinate::updateOrCreate([
                        'document_id' => $document->id,
                        // 'sign_page' => ($value['pageIndex'] + 1),
                        'materai_page' => ($value['pageIndex'] + 1),
                    ], [
                        'meterai_coordinate' => $value['meterai_coordinate'],
                        'meterai_id' => $value['meterai_id'],
                        'meterai_stamp_type' => $value['meterai_stamp_type'],
                        'meterai_page_index' => $value['meterai_page_index'],
                        'meterai_top' => $value['meterai_top'],
                        'meterai_left' => $value['meterai_left'],
                        'meterai_width' => $value['meterai_width'],
                        'meterai_height' => $value['meterai_height'],
                        'vis_llx' => $value['meterai_left'],
                        'vis_lly' => (800 - $value['meterai_top']),
                        'vis_urx' => $service->pointToPixel($value['meterai_left']),
                        'vis_ury' => $service->pointToPixel($value['meterai_top']),
                    ]);
                }
            }

            $dataMeterai = ($document->document_type == 'invoice') ? $arrayMaterai[$totalMeterai][0] : $arrayMaterai[$totalMeterai][0];
            if (count($arrayEsign[0]) > 0) {
                $dataSign = ($document->document_type == 'invoice') ? $arrayEsign[$totalSign][0] : $arrayEsign[$totalSign][0];
            }
            // throw new \Exception(json_encode($arrayMaterai[$totalMeterai][0]['meterai_stamp_type']), 1);
            if (array_key_exists('meterai_stamp_type', $dataMeterai)) {
                // code...
                $document->meterai_coordinate = $dataMeterai['meterai_coordinate'];
                $document->meterai_id = $dataMeterai['meterai_id'];
                // $document->meterai_color = $dataMeterai['meterai_color'];
                $document->meterai_stamp_type = $dataMeterai['meterai_stamp_type'];
                $document->meterai_page_index = $dataMeterai['meterai_page_index'];
                $document->meterai_top = $dataMeterai['meterai_top'];
                $document->meterai_left = $dataMeterai['meterai_left'];
                $document->meterai_width = $dataMeterai['meterai_width'];
                $document->meterai_height = $dataMeterai['meterai_height'];

                // $document->vis_llx = $dataMeterai['visLLX'];
                // $document->vis_lly = $dataMeterai['visLLY'];
                // $document->vis_urx = $dataMeterai['visLLX'];
                // $document->vis_ury = $dataMeterai['visLLY'];

                $document->materai_page = $dataMeterai['pageIndex'] + 1;

                $document->vis_llx = $dataMeterai['visLLX'];
                if (count($arrayEsign[0]) > 0) {
                    if ($totalMeterai == $totalSign) {
                        if ($dataMeterai['meterai_id'] == 'combine' || $dataSign['digisign_id'] == 'combine') {
                            $document->vis_lly = $dataMeterai['visLLY'];
                            $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                            $document->vis_ury = $service->pointToPixel($dataMeterai['meterai_top']);
                        } else {
                            $document->vis_lly = $dataMeterai['visLLY'];
                            $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                            $document->vis_ury = $service->pointToPixel($dataMeterai['meterai_top']);
                        }
                    } else {
                        $document->vis_lly = $dataMeterai['visLLY'] + 50;
                        $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                        $document->vis_ury = $service->pointToPixel($dataMeterai['meterai_top']);
                    }
                } else {
                    // $document->vis_lly = $dataMeterai['visLLY'] + 50;
                    // $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                    // $document->vis_ury = $service->pointToPixel($dataMeterai['visLLY']) + 50;

                    $document->vis_lly = $dataMeterai['visLLY'];
                    $document->vis_urx = $service->pointToPixel($dataMeterai['visLLX']);
                    $document->vis_ury = $service->pointToPixel($dataMeterai['meterai_top']);
                }
            }
        }

        $document->save();
    }



    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function batchSubmitApproval(Request $request): JsonResponse
    {
        try {
            $count_document = 0;
            $list_doc = [];
            $cc_email = [];
            foreach ($request->all() as $key => $item) {
                $count_document++;
            }

            foreach ($request->all() as $key => $item) {
                $document = $this->processSubmitApproval((object) $item, $request, 'batch');

                if ($document['error']) {
                    return $this->error($document['message']);
                }

                $itms = (object) $item;
                $id = $itms->id;
                $doc = Document::find($id);
                $list_doc[] = $doc->external_document_number;
                // $list_doc .= $doc->external_document_number .', ';
                $requester = ViewEmployee::where('Nik', $doc->userCreate->username)->first();
                $cc_email[] = $requester->OfficeEmailAddress;
            }

            $rules = ApprovalRule::all();
            foreach ($rules as $key => $rule) {
                if ($rule->name == 'Document Type') {
                    $array_value = $rule->value;
                    $approval = Approval::find($rule->approval_id);
                    if ($approval) {
                        $approvers = ApprovalApprover::leftJoin('users', 'users.id', 'approval_approver.user_id')
                            ->where('approval_id', $approval->id)
                            ->orderBy('approval_approver.sequence')
                            ->get();

                        foreach ($approvers as $index => $approver) {
                            // $requester = ViewEmployee::where('Nik', $document->userCreate->username)->first();
                            $project = [
                                'greeting' => 'Dear ' . $approver->name . ',',
                                'body' => $count_document . ': Invoices waitting for approval',
                                'subject' => $count_document . ': Invoices waitting for approval',
                                'inv' => $list_doc,
                                'to' => '<EMAIL>',
                                'cc' => array_unique($cc_email)
                            ];

                            Notification::route('mail', '<EMAIL>')
                                ->notify(new ApprovalMeteraiRequest($project));
                            // Notification::route('mail', '<EMAIL>')
                        }
                    }
                }
            }


            return $this->success('Data Submitted');
        } catch (Exception $exception) {
            return $this->error($exception->getMessage(), '422', [$exception->getTraceAsString()]);
        }
    }

    /**
     * @param $requestData
     * @param $request
     * @return array|false[]
     */
    protected function processSubmitApproval($requestData, $request, $type): array
    {
        $service = new ApprovalService();
        $id = $requestData->id;
        $document = Document::find($id);
        // $arrayMaterai = $request->arrayMaterai;
        // $arrayEsign = $request->arrayEsign;

        // return $this->error('', 422, [$document->attachment()->exists()]);

        if (!$document->attachment()->exists()) {
            return [
                'error' => true,
                'message' => 'Document number ' . $document->document_number . ': Attachment cannot empty!'
            ];
        }

        if ($document->meterai_id == 'sign' && empty($document->vis_digisign_llx)) {
            return [
                'error' => true,
                'message' => 'Document number ' . $document->document_number . ': sign Coordinate cannot empty!!'
            ];
        }

        if ($document->meterai_id == 'materai' && empty($document->vis_llx)) {
            return [
                'error' => true,
                'message' => 'Document number ' . $document->document_number . ': meterai Coordinate cannot empty!'
            ];
        }

        $service->submitApproval('Document Type', $document, $type, $requestData);

        $document->status = 'pending';
        $document->save();

        return [
            'error' => false
        ];
    }

    /**
     * @param Request $request
     * @return array|JsonResponse
     */
    public function submitApproval(Request $request)
    {
        try {
            $document = $this->processSubmitApproval($request, $request, 'single');

            if ($document['error']) {
                return $this->error($document['message']);
            } else {
                return $this->success('Data Submitted');
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage(), '422', [$exception->getTraceAsString()]);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws FileNotFoundException
     */
    public function downloadDocument(Request $request)
    {
        try {
            $doc_id = (array) $request->id;
            $type = $request->type;
            foreach ($doc_id as $key => $id) {
                $document = Document::find($id);
                // return $this->error($document->ref_token);
                // $document->ref_token = $response['result']['sn'];
                // $document->save();
                $file_name = $document->attachment->file_name;

                if ($document->internal_document != 'Y') {
                    if ($document->digisign_coordinate == 'Y') {
                        if (date('Y-m-d', strtotime($document->created_at)) < '2022-06-27') {
                            $doc_id = Str::slug(strtoupper($document->document_number));
                        } else {
                            $doc_id = Str::slug(strtoupper($document->external_document_number));
                        }

                        // there are no digisign error message, next step process the download file
                        $esign_user_id = $this->getConfigByName('UserIdEsign', 'ESIGN', $document->company);
                        $token = $this->getConfigByName('TokenEsign', 'ESIGN', $document->company);

                        $url = $this->getConfigByName('DownloadBase64', 'ESIGN', $document->company);
                        $options = [
                            'jsonfield' => json_encode(
                                [
                                    'JSONFile' => [
                                        // "userid" => $user_id,
                                        'userid' => $esign_user_id,
                                        'document_id' => $doc_id,
                                        // "document_id" => "SNI7",
                                    ],
                                ]
                            ),
                        ];

                        $headers[] = "Authorization: Bearer ${token}";
                        $curl = curl_init();
                        curl_setopt($curl, CURLOPT_HEADER, false);
                        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
                        curl_setopt($curl, CURLOPT_URL, $url);
                        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                        // curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
                        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
                        curl_setopt($curl, CURLOPT_VERBOSE, 1);
                        curl_setopt($curl, CURLOPT_POST, 1);
                        curl_setopt($curl, CURLOPT_TIMEOUT, 95);
                        // timeout in seconds
                        curl_setopt($curl, CURLOPT_POSTFIELDS, $options);

                        // Set max retries:
                        $MAX_RETRIES = 5;
                        for ($i = 0; $i < $MAX_RETRIES; $i++) {

                            // Send the request and save response to $response
                            $response = curl_exec($curl);

                            // Stop if fails
                            if (!$response) {

                                // Log::info('result from download document failed', [
                                //     'result' => curl_error($curl) . '" - Code: ' . curl_errno($curl)
                                // ]);
                                die('Error: "' . curl_error($curl) . '" - Code: ' . curl_errno($curl));
                            }

                            $status_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                            // If it's a successful request (200 or 404 status code):
                            if (in_array($status_code, array(200, 404))) {
                                // $response = curl_exec($curl);
                                curl_close($curl);
                                // dd($options);
                                // dd(config('app.sign_url_download'));
                                $response_text = json_decode($response);
                                // return $this->error('', 422, $response);
                                // dd($response);
                                // Log::info('response download digisign', [
                                //     $response_text
                                // ]);

                                $collect_response = collect($response_text);

                                if (!isset($collect_response['JSONFile']->file)) {
                                    throw new Exception(json_encode($collect_response), 1);
                                }


                                // $contents = base64_decode($response->collect()['JSONFile']['file']);
                                $contents = base64_decode($collect_response['JSONFile']->file);
                                // $file_name = $doc_id . '_DOWNLOAD.pdf';

                                // $path_download = public_path('documents/' . $file_name);
                                // file_put_contents($path_download, $contents);

                                custom_disk_put('documents/' . $file_name, $contents);

                                // Remove Attachment
                                // RemoveAttachment::dispatch($path_download)->delay(now()->addMinutes(30));
                                // echo 'Response Body: ' . $response . PHP_EOL;
                                break;
                            }
                        }


                        $this->createLog(
                            $document->id,
                            'document',
                            'Digisign - Download document ID ' . $doc_id . ', userid : ' . $esign_user_id,
                            $request->user()->id
                        );
                    }
                }


                if ($type == 'batch') {
                    if ($document->internal_document == 'Y') {
                        create_file_delete_job('documents/' . $file_name);
                        $all_pdf[] = public_path('documents/' . $file_name);
                    } else {
                        if ($document->digisign_coordinate == 'Y') {
                            create_file_delete_job('documents/' . $file_name);
                            $all_pdf[] = public_path('documents/' . $file_name);
                        } else {
                            $fileNames = Attachment::where('source_id', $document->id)
                                ->where('type', 'peruri')
                                ->first();
                            create_file_delete_job('documents/' . $fileNames->file_name);
                            $all_pdf[] = public_path('report/documents/' . $fileNames->file_name);
                        }
                    }
                } else {
                    $headers = [
                        'Content-Type' => 'application/pdf',
                        'Content-Disposition' => 'attachment; filename="' . $file_name . '"',
                    ];

                    if ($document->internal_document == 'Y') {
                        return Response::make(Storage::disk('sftp')
                            ->get('/documents/' . $file_name), 200, $headers);
                    } else {
                        if ($document->digisign_coordinate == 'Y') {
                            return Response::make(Storage::disk('sftp')
                                ->get('/documents/' . $file_name), 200, $headers);
                        } else {
                            $fileNames = Attachment::where('source_id', $document->id)
                                ->where('type', 'peruri')
                                ->first();
                            // $file_name = public_path('documents/' . $file_name);
                            return Response::make(Storage::disk('sftp')
                                ->get('/documents/' . $fileNames->file_name), 200, $headers);
                        }
                    }
                }
            }

            $zipper = new Zipper();
            $zip_path = public_path() . '/documents/';
            if (!file_exists($zip_path)) {
                if (!mkdir($zip_path, 0777, true) && !is_dir($zip_path)) {
                    throw new RuntimeException(
                        sprintf(
                            'Directory "%s" was not created',
                            $zip_path
                        )
                    );
                }
            }
            $file_name = 'E-MATERAI-BATCH' . date('YmdHis') . '.zip';
            $data_file = public_path('documents/' . $file_name);
            if (file_exists($data_file)) {
                unlink($data_file);
            }
            $zipper->make($data_file)->add($all_pdf);
            $zipper->close();
            $headers = [
                'Content-Type: application/zip',
                'Content-Disposition' => 'attachment; filename="' . $file_name . '"'
            ];

            RemoveAttachment::dispatch($data_file)->delay(now()->addMinutes(30));

            return Response::make(Storage::disk('app_documents')
                ->get('/documents/' . $file_name), 200, $headers);
        } catch (Exception $exception) {
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTraceAsString()
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     */
    public function show(Request $request, $id): JsonResponse
    {
        $category = $request->category;
        $brand = Document::where('category', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param \App\Models\Document\Document $productBrand
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        DB::beginTransaction();
        try {
            Document::where('id', $id)
                ->update($this->service->formData($request, 'update'));
            DB::commit();
            return $this->success('Document saved!');
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required',
            'identity_type' => 'required',
            'identity_number' => 'required',
            'identity_name' => 'required',
            'document_date' => 'required',
            'location' => 'required',
        ]);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return JsonResponse
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            $documents = Document::whereIn('id', $id)->get();
            foreach ($documents as $key => $document) {
                $file = Attachment::where('source_id', $document->id)
                    ->where('type', 'peruri')
                    ->first();

                $data_file = 'Attachment/docs/' . $file->file_name;
                // if (file_exists($data_file)) {
                //     unlink($data_file);
                // }
                if (custom_disk_check($data_file)) {
                    custom_disk_delete($data_file);
                }
            }
            Document::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /**
     * @param $subType
     * @param $type
     * @return mixed
     */
    protected function processSubType($subType, $type)
    {
        if (!is_int($subType)) {
            if (is_string($subType)) {
                DocumentSubType::updateOrCreate([
                    'document_type' => $type,
                    'name' => $subType
                ]);
                $data = DocumentSubType::where('name', 'LIKE', '%' . $subType . '%')->first();
            } else {
                DocumentSubType::updateOrCreate([
                    'document_type' => $type,
                    'name' => $subType->name
                ]);
                $data = DocumentSubType::where('name', 'LIKE', '%' . $subType->name . '%')->first();
            }
        }
        return $data->id;
    }

    /**
     * @param $name
     * @return mixed
     */
    protected function processCustomer($name)
    {
        if (!is_int($name)) {
            if (is_string($name)) {
                Customer::updateOrCreate([
                    'name' => strtoupper($name)
                ]);
                $data = Customer::where('name', 'LIKE', '%' . $name . '%')->first();
            } else {
                Customer::updateOrCreate([
                    'name' => strtoupper($name->name)
                ]);
                $data = Customer::where('name', 'LIKE', '%' . $name->name . '%')->first();
            }
        }
        return $data->id;
    }
}
