<?php

namespace App\Services\Reports;

class ReceiptApdService
{
    public function header()
    {
        return [
            'WhsCode',
            'DocDate',
            'GRPO NO',
            'Item Code',
            'Item Name',
            'Qty',
            'UoM',
            'Notes',
            'PO NO',
            'PR NO',
            'RESV NO'
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "WhsCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "GRPONo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Dscription",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "unitMsr",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Comments",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "PO-NO",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "PR-NO",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "U_ERESV_DOCNUM",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value['WhsCode'],
                $value['DocDate'],
                $value['GRPONo'],
                $value['ItemCode'],
                $value['Dscription'],
                $value['Quantity'],
                $value['unitMsr'],
                $value['Comments'],
                $value['PO-NO'],
                $value['PR-NO'],
                $value['U_ERESV_DOCNUM'],
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
