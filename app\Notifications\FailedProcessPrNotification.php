<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class FailedProcessPrNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public $document;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($document)
    {
        $this->document = $document;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->error()
            ->subject('Failed process E-Reservation: ' . $this->document->DocNum)
            ->greeting('Dear, ' . $notifiable->name)
            ->line('Your E-Reservation ' . $this->document->DocNum . ' Failed to send to purchasing. Please contact IT E-Reservation Admin')
            ->line("Thanks");
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'title' => 'Failed process E-Reservation: ' . $this->document->DocNum,
            'documentType' => 'E-Reservation',
            'transactionType' => 'E-Reservation',
            'documentId' => $this->document->U_DocEntry,
            'documentNumber' => $this->document->DocNum,
            'content' => $this->document
        ];
    }
}
