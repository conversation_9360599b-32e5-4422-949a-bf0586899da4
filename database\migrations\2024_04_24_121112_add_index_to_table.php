<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attachments', function (Blueprint $table) {
            $table->index([
                'source_id',
                'type',
            ]);
        });

        Schema::table('resv_headers', function (Blueprint $table) {
            $table->unique('DocNum');
            $table->index([
                'Company',
                'RequesterName',
                'CreatedName',
                'RequestType',
                'RequiredDate',
                'ApprovalStatus',
                'CreatedBy',
                'Token',
            ]);
        });
        Schema::table('resv_details', function (Blueprint $table) {
            $table->index([
                'EmployeeId',
                'AssetCode',
                'AssetName',
                'SubGroup',
                'ItemGroup',
                'AvailableQty',
            ]);

            $table->index([
                'ItemCode',
                'LineNum',
                'WhsCode',
                'UoMCode',
                'U_ATTACH',
                'EmployeeName',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('table', function (Blueprint $table) {
            //
        });
    }
};
