<?php

namespace App\Services;

use App\Jobs\RemoveAttachment;
use App\Jobs\SendClinicNotificationEmail;
use App\Models\Master\Airport;
use App\Models\Paper\MasterPaper;
use App\Models\Paper\Paper;
use App\Models\Paper\PaperDetails;
use App\Models\View\ViewEmployee;
use App\Models\View\ViewApprovalStage;
use App\Services\ConvertDocxToPdfService;
use App\Traits\AppConfig;
use App\Traits\ApiResponse;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\SimpleType\TblWidth;
use PhpOffice\PhpWord\TemplateProcessor;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Webklex\PDFMerger\Facades\PDFMergerFacade as PDFMerger;
use PhpOffice\PhpWord\Exception\CopyFileException;
use PhpOffice\PhpWord\Exception\CreateTemporaryFileException;

class PaperService
{
    use AppConfig;
    use ApiResponse;

    public function saveDataApi($paper, $modelData, $type, $parameterData, $request)
    {
        $for_paper = array_key_exists('for_self', $modelData->form) ? $modelData->form['for_self'] : null;

        $master_paper = $this->getMasterPaper($parameterData->FormType);
        $username = $request->UserName;

        if ($type === 'post') {
            $paper->paper_no = $this->generateDocNum(date('Y-m-d'), $parameterData->FormType, $master_paper->id);
            $paper->str_url = array_key_exists('str_url', $modelData->form) ? $modelData->form['str_url'] : Str::random(40);
            // $paper->str_url = (!empty($request->modelData['str_url'])) ? $request->modelData['str_url'] : Str::random(40);
            $paper->user_id = $username;
            $paper->created_by = $username;
            $paper->created_name = $request->created_name;
            $paper->for_self = $for_paper;
        } else {
            $paper->updated_by = Auth::user()->username;
            $paper->updated_name = Auth::user()->name;
        }

        $paper->master_paper_id = $master_paper->id;
        $paper->created_at = Carbon::now();
        $paper->status = array_key_exists('status', $modelData->form) ? (($modelData->form['status']) ? $modelData->form['status'] : 'pending') : 'pending';
        $paper->leave_from_to = array_key_exists('leave_from_to', $modelData->form)
            ? $modelData->form['leave_from_to'] : null;
        $paper->reference_number = array_key_exists('reference_number', $modelData->form)
            ? $modelData->form['reference_number'] : null;
        $paper->flight_date_approve = array_key_exists('flight_date_approve', $modelData->form) ? $modelData->form['flight_date_approve'] : null;
        $paper->user_name = array_key_exists('user_name', $modelData->form) ? $modelData->form['user_name'] : null;
        $paper->reference_no = array_key_exists('reference_no', $modelData->form) ? $modelData->form['reference_no'] : null;
        $paper->address = array_key_exists('address', $modelData->form) ? $modelData->form['address'] : null;
        $paper->no_hp = array_key_exists('no_hp', $modelData->form) ? $modelData->form['no_hp'] : null;
        $paper->ktp = array_key_exists('ktp', $modelData->form) ? $modelData->form['ktp'] : null;
        $paper->id_card = array_key_exists('id_card', $modelData->form) ? $modelData->form['id_card'] : null;
        $paper->department = array_key_exists('department', $modelData->form) ? $modelData->form['department'] : null;
        $paper->company = array_key_exists('company', $modelData->form) ? $modelData->form['company'] : null;
        $paper->payment = array_key_exists('payment', $modelData->form) ? $modelData->form['payment'] : null;
        $paper->clinic_response = array_key_exists('clinic_response', $modelData->form) ? $modelData->form['clinic_response'] : null;
        $paper->company = array_key_exists('company', $modelData->form) ? $modelData->form['company'] : null;
        $paper->occupation = array_key_exists('occupation', $modelData->form) ? $modelData->form['occupation'] : null;
        $paper->reason = array_key_exists('reason', $modelData->form) ? $modelData->form['reason'] : null;
        $paper->date_out = array_key_exists('date_out', $modelData->form) ? $modelData->form['date_out'] : null;
        $paper->date_in = array_key_exists('date_in', $modelData->form) ? $modelData->form['date_in'] : null;
        $paper->period_stay = array_key_exists('period_stay', $modelData->form) ? $modelData->form['period_stay'] : null;
        $paper->destination = array_key_exists('destination', $modelData->form) ? $modelData->form['destination'] : null;
        $paper->transportation = array_key_exists('transportation', $modelData->form) ?
            $modelData->form['transportation'] : null;
        $paper->route = array_key_exists('route', $modelData->form) ? $modelData->form['route'] : null;
        $paper->name_boss = array_key_exists('name_boss', $modelData->form) ? $modelData->form['name_boss'] : null;
        $paper->position_boss = array_key_exists('position_boss', $modelData->form) ?
            $modelData->form['position_boss'] : null;
        $paper->nik_boss = array_key_exists('nik_boss', $modelData->form) ? $modelData->form['nik_boss'] : null;
        $paper->paper_date = array_key_exists('paper_date', $modelData->form) ? $modelData->form['paper_date'] : null;
        $paper->swab_date = array_key_exists('swab_date', $modelData->form) ? $modelData->form['swab_date'] : null;
        $paper->reason_swab = array_key_exists('reason_swab', $modelData->form) ? $modelData->form['reason_swab'] : null;
        $paper->is_complete = array_key_exists('is_complete', $modelData->form) ? $modelData->form['is_complete'] : 'N';
        $paper->resv_for = array_key_exists('resv_for', $modelData->form) ? $modelData->form['resv_for'] : null;
        $paper->resv_for = array_key_exists('resv_for', $modelData->form) ? $modelData->form['resv_for'] : null;
        $paper->travel_purpose = array_key_exists('travel_purpose', $modelData->form) ? $modelData->form['travel_purpose'] : null;
        $paper->reason_purpose = array_key_exists('reason_purpose', $modelData->form) ? $modelData->form['reason_purpose'] : null;
        $paper->cost_cover = array_key_exists('cost_cover', $modelData->form) ? $modelData->form['cost_cover'] : null;
        $paper->work_location = array_key_exists('work_location', $modelData->form) ? $modelData->form['work_location'] : null;
        $paper->total_seat = array_key_exists('total_seat', $modelData->form) ? $modelData->form['total_seat'] : null;
        $paper->request_date = array_key_exists('request_date', $modelData->form) ? $modelData->form['request_date'] : null;

        $req_flight_origin = array_key_exists('flight_origin', $modelData->form) ? $modelData->form['flight_origin'] : null;
        if (is_string($req_flight_origin)) {
            $origin = Airport::where('name', '=', $req_flight_origin)->first();
            $req_flight_origin = $origin->id;
        }

        $paper->flight_origin = $req_flight_origin;

        $req_flight_origin_approve = array_key_exists('flight_origin_approve', $modelData->form) ? $modelData->form['flight_origin_approve'] : null;
        if (is_string($req_flight_origin_approve)) {
            $origin = Airport::where('name', '=', $req_flight_origin_approve)->first();
            $req_flight_origin_approve = $origin->id;
        }

        $paper->flight_origin_approve = $req_flight_origin_approve;

        $req_flight_destination = array_key_exists('flight_destination', $modelData->form) ? $modelData->form['flight_destination'] : null;
        if (is_string($req_flight_destination)) {
            $origin = Airport::where('name', '=', $req_flight_destination)->first();
            $req_flight_destination = $origin->id;
        }
        $paper->flight_destination = $req_flight_destination;

        $req_flight_destination_approve = array_key_exists('flight_destination_approve', $modelData->form) ? $modelData->form['flight_destination_approve'] : null;
        if (is_string($req_flight_destination_approve)) {
            $origin = Airport::where('name', '=', $req_flight_destination_approve)->first();
            $req_flight_destination_approve = $origin->id;
        }
        $paper->flight_destination_approve = $req_flight_destination_approve;

        $paper->seat_available = array_key_exists('seat_available', $modelData->form) ? $modelData->form['seat_available'] : null;
        $paper->total_assigned = array_key_exists('total_assigned', $modelData->form) ? $modelData->form['total_assigned'] : null;
        $paper->notes = array_key_exists('notes', $modelData->form) ? $modelData->form['notes'] : null;
        $paper->email = array_key_exists('email', $modelData->form) ? $modelData->form['email'] : null;
        $paper->flight_no = array_key_exists('flight_no', $modelData->form) ? $modelData->form['flight_no'] : null;
        $paper->host_company = array_key_exists('host_company', $modelData->form) ? $modelData->form['host_company'] : null;
        $paper->visitor_company = array_key_exists('visitor_company', $modelData->form) ? $modelData->form['visitor_company'] : null;
        $paper->company_officer = array_key_exists('company_officer', $modelData->form) ? $modelData->form['company_officer'] : null;
        $paper->visitor_officer = array_key_exists('visitor_officer', $modelData->form) ? $modelData->form['visitor_officer'] : null;
        $paper->visitor_no_hp = array_key_exists('visitor_no_hp', $modelData->form) ? $modelData->form['visitor_no_hp'] : null;
        $paper->visitor_address = array_key_exists('visitor_address', $modelData->form) ? $modelData->form['visitor_address'] : null;
        $paper->company_email = array_key_exists('company_email', $modelData->form) ? $modelData->form['company_email'] : null;
        $paper->visitor_email = array_key_exists('visitor_email', $modelData->form) ? $modelData->form['visitor_email'] : null;
        $paper->plan_visit_area = array_key_exists('plan_visit_area', $modelData->form) ? $modelData->form['plan_visit_area'] : null;
        $paper->purpose_visit = array_key_exists('purpose_visit', $modelData->form) ? $modelData->form['purpose_visit'] : null;
        $paper->total_guest = array_key_exists('total_guest', $modelData->form) ? $modelData->form['total_guest'] : null;
        $paper->facilities = array_key_exists('facilities', $modelData->form) ? $modelData->form['facilities'] : null;
        $paper->paper_place = array_key_exists('paper_place', $modelData->form) ? $modelData->form['paper_place'] : null;
        $paper->other_facilities = array_key_exists('other_facilities', $modelData->form) ? $modelData->form['other_facilities'] : null;
        // $paper->driver = array_key_exists('driver', $modelData->form) ? $modelData->form['driver'] : null;
        // $paper->vehicle = array_key_exists('vehicle', $modelData->form) ? $modelData->form['vehicle'] : null;
        // $paper->vehicle_no = array_key_exists('vehicle_no', $modelData->form) ? $modelData->form['vehicle_no'] : null;
        // $paper->issue_date = array_key_exists('issue_date', $modelData->form) ? date('Y-m-d H:i:s', strtotime($modelData->form['issue_date'])) : null;
        $paper->save();

        return $paper;
    }

    /**
     * @param $paper
     * @param $request
     * @param $type
     *
     * @return mixed
     */
    public function saveData($paper, $request, $type)
    {
        $for_paper = array_key_exists('for_self', $request->form) ? $request->form['for_self'] : null;

        $master_paper = $this->getMasterPaper($request->alias);
        $username = $request->username;

        if ($type === 'post') {
            $paper->paper_no = $this->generateDocNum(date('Y-m-d'), $request->alias, $master_paper->id);
            $paper->str_url = array_key_exists('str_url', $request->form) ? $request->form['str_url'] : Str::random(40);
            $paper->user_id = $username;
            $paper->created_by = $username;
            $paper->created_name = $request->created_name;
            $paper->for_self = $for_paper;
        } else {
            $paper->updated_by = $request->user()->username;
            $paper->updated_name = $request->user()->name;
        }

        $paper->master_paper_id = $master_paper->id;
        $paper->created_at = Carbon::now();
        $paper->status = array_key_exists('status', $request->form) ? $request->form['status'] : 'pending';
        $paper->leave_from_to = array_key_exists('leave_from_to', $request->form)
            ? $request->form['leave_from_to'] : null;
        $paper->reference_number = array_key_exists('reference_number', $request->form)
            ? $request->form['reference_number'] : null;
        $paper->flight_date_approve = array_key_exists('flight_date_approve', $request->form) ? $request->form['flight_date_approve'] : null;
        $paper->user_name = array_key_exists('user_name', $request->form) ? $request->form['user_name'] : null;
        $paper->reference_no = array_key_exists('reference_no', $request->form) ? $request->form['reference_no'] : null;
        $paper->address = array_key_exists('address', $request->form) ? $request->form['address'] : null;
        $paper->no_hp = array_key_exists('no_hp', $request->form) ? $request->form['no_hp'] : null;
        $paper->ktp = array_key_exists('ktp', $request->form) ? $request->form['ktp'] : null;
        $paper->id_card = array_key_exists('id_card', $request->form) ? $request->form['id_card'] : null;
        $paper->department = array_key_exists('department', $request->form) ? $request->form['department'] : null;
        $paper->company = array_key_exists('company', $request->form) ? $request->form['company'] : null;
        $paper->payment = array_key_exists('payment', $request->form) ? $request->form['payment'] : null;
        $paper->clinic_response = array_key_exists('clinic_response', $request->form) ? $request->form['clinic_response'] : null;
        $paper->company = array_key_exists('company', $request->form) ? $request->form['company'] : null;
        $paper->occupation = array_key_exists('occupation', $request->form) ? $request->form['occupation'] : null;
        $paper->reason = array_key_exists('reason', $request->form) ? $request->form['reason'] : null;
        $paper->date_out = array_key_exists('date_out', $request->form) ? $request->form['date_out'] : null;
        $paper->date_in = array_key_exists('date_in', $request->form) ? $request->form['date_in'] : null;
        $paper->period_stay = array_key_exists('period_stay', $request->form) ? $request->form['period_stay'] : null;
        $paper->destination = array_key_exists('destination', $request->form) ? $request->form['destination'] : null;
        $paper->transportation = array_key_exists('transportation', $request->form) ?
            $request->form['transportation'] : null;
        $paper->route = array_key_exists('route', $request->form) ? $request->form['route'] : null;
        $paper->name_boss = array_key_exists('name_boss', $request->form) ? $request->form['name_boss'] : null;
        $paper->position_boss = array_key_exists('position_boss', $request->form) ?
            $request->form['position_boss'] : null;
        $paper->nik_boss = array_key_exists('nik_boss', $request->form) ? $request->form['nik_boss'] : null;
        $paper->paper_date = array_key_exists('paper_date', $request->form) ? $request->form['paper_date'] : null;
        $paper->swab_date = array_key_exists('swab_date', $request->form) ? $request->form['swab_date'] : null;
        $paper->reason_swab = array_key_exists('reason_swab', $request->form) ? $request->form['reason_swab'] : null;
        $paper->is_complete = array_key_exists('is_complete', $request->form) ? $request->form['is_complete'] : 'N';
        $paper->resv_for = array_key_exists('resv_for', $request->form) ? $request->form['resv_for'] : null;
        $paper->resv_for = array_key_exists('resv_for', $request->form) ? $request->form['resv_for'] : null;
        $paper->travel_purpose = array_key_exists('travel_purpose', $request->form) ? $request->form['travel_purpose'] : null;
        $paper->reason_purpose = array_key_exists('reason_purpose', $request->form) ? $request->form['reason_purpose'] : null;
        $paper->cost_cover = array_key_exists('cost_cover', $request->form) ? $request->form['cost_cover'] : null;
        $paper->work_location = array_key_exists('work_location', $request->form) ? $request->form['work_location'] : null;
        $paper->total_seat = array_key_exists('total_seat', $request->form) ? $request->form['total_seat'] : null;
        $paper->request_date = array_key_exists('request_date', $request->form) ? $request->form['request_date'] : null;
        $paper->swab_type = array_key_exists('swab_type', $request->form) ? $request->form['swab_type'] : null;

        $req_flight_origin = array_key_exists('flight_origin', $request->form) ? $request->form['flight_origin'] : null;
        if (is_string($req_flight_origin)) {
            $origin = Airport::where('name', '=', $req_flight_origin)->first();
            $req_flight_origin = $origin->id;
        }

        $paper->flight_origin = $req_flight_origin;

        $req_flight_origin_approve = array_key_exists('flight_origin_approve', $request->form) ? $request->form['flight_origin_approve'] : null;
        if (is_string($req_flight_origin_approve)) {
            $origin = Airport::where('name', '=', $req_flight_origin_approve)->first();
            $req_flight_origin_approve = $origin->id;
        }

        $paper->flight_origin_approve = $req_flight_origin_approve;

        $req_flight_destination = array_key_exists('flight_destination', $request->form) ? $request->form['flight_destination'] : null;
        if (is_string($req_flight_destination)) {
            $origin = Airport::where('name', '=', $req_flight_destination)->first();
            $req_flight_destination = $origin->id;
        }
        $paper->flight_destination = $req_flight_destination;

        $req_flight_destination_approve = array_key_exists('flight_destination_approve', $request->form) ? $request->form['flight_destination_approve'] : null;
        if (is_string($req_flight_destination_approve)) {
            $origin = Airport::where('name', '=', $req_flight_destination_approve)->first();
            $req_flight_destination_approve = $origin->id;
        }
        $paper->flight_destination_approve = $req_flight_destination_approve;

        $paper->seat_available = array_key_exists('seat_available', $request->form) ? $request->form['seat_available'] : null;
        $paper->total_assigned = array_key_exists('total_assigned', $request->form) ? $request->form['total_assigned'] : null;
        $paper->notes = array_key_exists('notes', $request->form) ? $request->form['notes'] : null;
        $paper->email = array_key_exists('email', $request->form) ? $request->form['email'] : null;
        $paper->flight_no = array_key_exists('flight_no', $request->form) ? $request->form['flight_no'] : null;
        $paper->host_company = array_key_exists('host_company', $request->form) ? $request->form['host_company'] : null;
        $paper->visitor_company = array_key_exists('visitor_company', $request->form) ? $request->form['visitor_company'] : null;
        $paper->company_officer = array_key_exists('company_officer', $request->form) ? $request->form['company_officer'] : null;
        $paper->visitor_officer = array_key_exists('visitor_officer', $request->form) ? $request->form['visitor_officer'] : null;
        $paper->visitor_no_hp = array_key_exists('visitor_no_hp', $request->form) ? $request->form['visitor_no_hp'] : null;
        $paper->visitor_address = array_key_exists('visitor_address', $request->form) ? $request->form['visitor_address'] : null;
        $paper->company_email = array_key_exists('company_email', $request->form) ? $request->form['company_email'] : null;
        $paper->visitor_email = array_key_exists('visitor_email', $request->form) ? $request->form['visitor_email'] : null;
        $paper->plan_visit_area = array_key_exists('plan_visit_area', $request->form) ? $request->form['plan_visit_area'] : null;
        $paper->purpose_visit = array_key_exists('purpose_visit', $request->form) ? $request->form['purpose_visit'] : null;
        $paper->total_guest = array_key_exists('total_guest', $request->form) ? $request->form['total_guest'] : null;
        $paper->facilities = array_key_exists('facilities', $request->form) ? $request->form['facilities'] : null;
        $paper->paper_place = array_key_exists('paper_place', $request->form) ? $request->form['paper_place'] : null;
        $paper->other_facilities = array_key_exists('other_facilities', $request->form) ? $request->form['other_facilities'] : null;
        // $paper->driver = array_key_exists('driver', $request->form) ? $request->form['driver'] : null;
        // $paper->vehicle = array_key_exists('vehicle', $request->form) ? $request->form['vehicle'] : null;
        // $paper->vehicle_no = array_key_exists('vehicle_no', $request->form) ? $request->form['vehicle_no'] : null;
        // $paper->issue_date = array_key_exists('issue_date', $request->form) ? date('Y-m-d H:i:s', strtotime($request->form['issue_date'])) : null;
        $paper->save();

        return $paper;
    }



    /**
     * @param $detail
     * @param $paper
     * @param $status
     * @param $paper_id
     * @return mixed
     */
    public function saveDetails($detail, $paper, $status, $paper_id)
    {
        $paper->name_title = array_key_exists('name_title', $detail) ? $detail['name_title'] : null;
        $paper->name = array_key_exists('name', $detail) ? $detail['name'] : null;
        $paper->position = array_key_exists('position', $detail) ? $detail['position'] : null;
        $paper->body_weight = array_key_exists('body_weight', $detail) ? $detail['body_weight'] : null;
        $paper->departing_city = array_key_exists('departing_city', $detail) ? $detail['departing_city'] : null;
        $paper->arrival_date = array_key_exists('arrival_date', $detail) ? $detail['arrival_date'] : null;
        $paper->arrival_flight_no = array_key_exists('arrival_flight_no', $detail) ? $detail['arrival_flight_no'] : null;
        $paper->arrival_time = array_key_exists('arrival_time', $detail) ? $detail['arrival_time'] : null;
        $paper->departure_date = array_key_exists('departure_date', $detail) ? $detail['departure_date'] : null;
        $paper->departure_flight_no = array_key_exists('departure_flight_no', $detail) ? $detail['departure_flight_no'] : null;
        $paper->departure_time = array_key_exists('departure_time', $detail) ? $detail['departure_time'] : null;
        $paper->destination_city = array_key_exists('destination_city', $detail) ? $detail['destination_city'] : null;
        $paper->transport_to = array_key_exists('transport_to', $detail) ? $detail['transport_to'] : null;
        $paper->transport_from = array_key_exists('transport_from', $detail) ? $detail['transport_from'] : null;
        $paper->notes = array_key_exists('notes', $detail) ? $detail['notes'] : null;
        $paper->nationality = array_key_exists('nationality', $detail) ? $detail['nationality'] : null;
        $paper->id_card = array_key_exists('id_card', $detail) ? $detail['id_card'] : null;
        $paper->employee_type = array_key_exists('employee_type', $detail) ? $detail['employee_type'] : null;
        $paper->company = array_key_exists('company', $detail) ? $detail['company'] : null;
        $paper->seat_no = array_key_exists('seat_no', $detail) ? $detail['seat_no'] : null;
        $paper->status = array_key_exists('status', $detail) ? $detail['status'] : null;
        $paper->room_no = array_key_exists('room_no', $detail) ? $detail['room_no'] : null;
        $paper->alternative_room_no = array_key_exists('alternative_room_no', $detail) ? $detail['alternative_room_no'] : null;
        $paper->national_id = array_key_exists('national_id', $detail) ? $detail['national_id'] : null;
        $paper->phone_no = array_key_exists('phone_no', $detail) ? $detail['phone_no'] : null;
        $paper->department = array_key_exists('department', $detail) ? $detail['department'] : null;
        $paper->address = array_key_exists('address', $detail) ? $detail['address'] : null;
        // $paper->item_name = array_key_exists('item_name', $detail) ? $detail['item_name'] : null;
        // $paper->uom = array_key_exists('uom', $detail) ? $detail['uom'] : null;
        // $paper->qty = array_key_exists('qty', $detail) ? $detail['qty'] : null;

        if ($status == 'create') {
            $paper->paper_id = $paper_id;
            $paper->created_by = auth()->user()->id;
        } else {
            $paper->updated_by = auth()->user()->id;
        }
        return $paper->save();
    }


    /**
     * @param $sysDate
     * @param $alias
     * @param $master_paper_id
     *
     * @return string
     */
    public function generateDocNum($sysDate, $alias, $master_paper_id): string
    {
        $data_date = strtotime($sysDate);
        $year_val = date('y', $data_date);
        $full_year = date('Y', $data_date);
        $month = date('m', $data_date);
        $day_val = date('j', $data_date);
        $end_date = date('t', $data_date);

        if ((int) $day_val === 1) {
            $docnum = Str::upper($alias) . '/IMIP/' . $full_year . '/' . $month . '/' . sprintf('%05s', '1');
            $check_docnum = Paper::where('paper_no', '=', $docnum)->first();
            if (!$check_docnum) {
                return Str::upper($alias) . '/IMIP/' . $full_year . '/' . $month . '/' . sprintf('%05s', '1');
            } else {
                $first_date = "${full_year}-${month}-01";
                $second_date = "${full_year}-${month}-${end_date}";
                $doc_num = Paper::selectRaw('paper_no')
                    ->where('master_paper_id', '=', $master_paper_id)
                    ->whereBetween(DB::raw('CONVERT(date, created_at)'), [$first_date, $second_date])
                    ->orderBy('paper_no', 'DESC')
                    ->first();
                //SIK/IMIP/2021/06/xxxxx
                //STKPD/IMIP/2021/06/xxxxx
                $number = empty($doc_num) ? '0000000000' : $doc_num->paper_no;
                if (Str::upper($alias) == 'STKPD') {
                    $clear_doc_num = (int) substr($number, 19, 24);
                } else {
                    $clear_doc_num = (int) substr($number, 17, 22);
                }
                $number = $clear_doc_num + 1;
                return Str::upper($alias) . '/IMIP/' . $full_year . '/' . $month . '/' . sprintf('%05s', $number);
            }
        }
        $first_date = "${full_year}-${month}-01";
        $second_date = "${full_year}-${month}-${end_date}";
        $doc_num = Paper::selectRaw('paper_no')
            ->where('master_paper_id', '=', $master_paper_id)
            ->whereBetween(DB::raw('CONVERT(date, created_at)'), [$first_date, $second_date])
            ->orderBy('paper_no', 'DESC')
            ->first();
        //SIK/IMIP/2021/06/xxxxx
        //STKPD/IMIP/2021/06/xxxxx
        $number = empty($doc_num) ? '0000000000' : $doc_num->paper_no;
        if (Str::upper($alias) == 'STKPD') {
            $clear_doc_num = (int) substr($number, 19, 24);
        } else {
            $clear_doc_num = (int) substr($number, 17, 22);
        }
        $number = $clear_doc_num + 1;
        return Str::upper($alias) . '/IMIP/' . $full_year . '/' . $month . '/' . sprintf('%05s', $number);
    }


    /**
     * @param $alias
     *
     * @return mixed
     */
    public function getMasterPaper($alias)
    {
        return MasterPaper::where('alias', '=', $alias)->first();
    }

    public function printDocument($request, $superApps = false)
    {
        setlocale(LC_TIME, 'id_ID');
        Carbon::setLocale('id');

        if ($superApps) {
            $item = (object) $request->item;
        } else {
            $item = json_decode($request->item);
        }
        $alias = $request->type;
        $paper = Paper::withCount('lineItems')
            ->with(['lineItems'])
            ->where('id', '=', $item->id)
            ->first();

        $approval = ViewApprovalStage::where('DocumentReferenceID', $paper->paper_no)
            ->orderBy('Sequence', 'desc')
            ->first();

        if ($approval) {
            $approvalRapid = ViewApprovalStage::where('DocumentReferenceID', $paper->paper_no)
                ->orderBy('Sequence', 'desc')
                ->where('Sequence', '<', $approval->Sequence)
                ->first();
        }


        $direct_superior = ViewEmployee::where('Nik', '=', $paper->id_card)->first();

        if (!$direct_superior) {
            $direct_superior = ViewEmployee::where('Nik', '=', $paper->user_id)->first();
        }

        $file_export_name = $paper->str_url;

        $qr_file = "https://sbo2.imip.co.id:3001/eaccess_test" . '/verification?str=' . $file_export_name;

        $qrCodeService = new QrCodeService();

        $qrCodeService->generatePath(public_path() . '/images/qrcode/');
        // generate QR Code
        $qrImagePath = public_path('images/qrcode/' . $file_export_name . '.png');
        $qr_code = $qrCodeService->generateQrCode($qr_file, $file_export_name, 300);

        $qrCodeService->generatePath(public_path() . '/paper/');


        $paper_place = 'Fatufia';
        if ($alias == 'sim') {
            $letter_template = new TemplateProcessor(public_path('template/paper/SIM.docx'));
        } elseif ($alias == 'sik' || $alias == 'skt') {
            $letter_template = new TemplateProcessor(public_path('template/paper/SIK.docx'));
        } elseif ($alias == 'srm') {
            $letter_template = new TemplateProcessor(public_path('template/paper/SRM.docx'));
        } elseif ($alias == 'rtm') {
            $paper_place = 'Jakarta';
            if ($paper->line_items_count > 1) {
                $letter_template = new TemplateProcessor(public_path('template/paper/SRM-2.docx'));
            } else {
                $letter_template = new TemplateProcessor(public_path('template/paper/SRM-1.docx'));
            }
        } elseif ($alias == 'srk' || $alias == 'rtk') {
            $letter_template = new TemplateProcessor(public_path('template/paper/SRK.docx'));
        } elseif ($alias == 'stkpd') {
            $letter_template = new TemplateProcessor(public_path('template/paper/STK.docx'));
        } elseif ($alias == 'smt') {
            if ($paper->line_items_count > 1) {
                $letter_template = new TemplateProcessor(public_path('template/paper/SMT-2.docx'));
            } else {
                $letter_template = new TemplateProcessor(public_path('template/paper/SMT.docx'));
            }
        } elseif ($alias == 'fsr') {
            $letter_template = new TemplateProcessor(public_path('template/paper/FSR.docx'));
        } elseif ($alias == 'gsv') {
            $letter_template = new TemplateProcessor(public_path('template/paper/GSV.docx'));
        } elseif ($alias == 'abr') {
            $letter_template = new TemplateProcessor(public_path('template/paper/ABR.docx'));
        } elseif ($alias == 'ibk') {
            $letter_template = new TemplateProcessor(public_path('template/paper/IBK.docx'));
        }

        $letter_template->setImageValue(
            'QRCODE',
            $qrImagePath
        );
        $letter_template->setImageValue(
            'QRCODE1',
            array(
                'path' => $qrImagePath,
                'width' => 50,
                'height' => 50,
                'ratio' => false
            )
        );

        $user_created = ViewEmployee::where('Nik', $paper->created_by)->first();
        $department = str_replace('&', '&amp;', $paper->department);
        $occupation = str_replace('&', '&amp;', $paper->occupation);
        $occupation_created = str_replace('&', '&amp;', $user_created->JobPosition);

        $date_in = $paper->date_in;
        $date_out = $paper->date_out;
        $details = PaperDetails::where('paper_id', '=', $paper->id)->first();
        if ($details) {
            if (!empty($date_in) && !empty($date_out)) {
                $date_in = Carbon::parse($date_in);
                $date_out = Carbon::parse($date_out);

                $diff_in_day = $date_out->diffInHours($date_in);

                $arrival_time = date('H', strtotime($details->arrival_time));
                $departure_time = date('H', strtotime($details->departure_time));
                $hour_date_in = $date_in->format('H');
                $count_day = '';
                $count_night = '';
                if ($arrival_time == $departure_time) {
                    $count_night = floor(($diff_in_day / 24));
                    $count_day = floor($diff_in_day / 24);
                } elseif (($arrival_time >= 18 && $arrival_time <= 24) || ($arrival_time >= 0 && $arrival_time <= 6)) {
                    if ($departure_time > 6 && $departure_time < 18) {
                        $count_night = floor(($diff_in_day / 24));
                        $mod = ($diff_in_day % 24);
                        $count_day = ceil($diff_in_day / 24);
                    } elseif (($departure_time >= 18 && $departure_time <= 24) && ($departure_time >= 0 && $departure_time <= 6)) {
                        $count_night = floor(($diff_in_day / 24));
                        $count_day = floor($diff_in_day / 24);
                    }
                } elseif (($departure_time >= 18 && $departure_time <= 24) || ($departure_time >= 0 && $departure_time <= 6)) {
                    if ($arrival_time > 6 && $arrival_time < 18) {
                        $count_day = floor(($diff_in_day / 24));
                        $mod = ($diff_in_day % 24);
                        // return response()->json($diff_in_day, 422);
                        $count_night = ceil($diff_in_day / 24);
                    } elseif (($arrival_time >= 18 && $arrival_time <= 24) && ($arrival_time >= 0 && $arrival_time <= 6)) {
                        $count_night = floor(($diff_in_day / 24));
                        $count_day = floor($diff_in_day / 24);
                    }
                }
                $letter_template->setValue('DAY', (($count_day) ? $count_day + 1 : ''));
                $letter_template->setValue('NIGHT', (($count_night) ? $count_night : ''));
            }
        }

        $clinic_response = Paper::where("paper_no", "=", $paper->reference_no)->first();
        // throw new \Exception(json_encode($firstItem->name), 1);

        $letter_template->setValue('REQUESTNAME', $paper->user_name);
        $letter_template->setValue('REQUESTER', $paper->user_name);
        $letter_template->setValue('PAPER_PLACE', $paper_place);
        $letter_template->setValue('NOHP', $paper->no_hp);

        $approvalStage1 = ViewApprovalStage::where('DocumentReferenceID', $paper->paper_no)
            // ->whereNotNull('ResponseDate')
            ->orderBy('Sequence', 'asc')
            ->first();

        if (Str::contains($alias, ['rtm', 'smt']) && $paper->line_items_count == 1) {
            $firstItem = $paper->lineItems->first();
            $department = str_replace('&', '&amp;', $firstItem->department);
            $occupation = str_replace('&', '&amp;', $firstItem->position);

            $letter_template->setValue('NAME', $firstItem->name);
            $letter_template->setValue('ADDRESS', $firstItem->address);
            $letter_template->setValue('NO_HP', $firstItem->phone_no);
            $letter_template->setValue('KTP', $firstItem->national_id);
            $letter_template->setValue('DEP_AND_COMP', ($department . ' / ' . $firstItem->company));
            $letter_template->setValue('OCCUPATION', $occupation);
            $letter_template->setValue('NIK', $firstItem->national_id);
            $letter_template->setValue('ID_CARD', $firstItem->id_card);
            $letter_template->setValue('COMPANY', $firstItem->company);
        } else {
            $letter_template->setValue('NAME', $paper->user_name);
            $letter_template->setValue('ADDRESS', $paper->address);
            $letter_template->setValue('NO_HP', $paper->no_hp);
            $letter_template->setValue('NOHP', $paper->no_hp);
            $letter_template->setValue('KTP', $paper->ktp);
            $letter_template->setValue('DEP_AND_COMP', ($department . ' / ' . $paper->company));
            $letter_template->setValue('OCCUPATION', $occupation);
            $letter_template->setValue('JABATAN', $occupation);
            $letter_template->setValue('DEPARTMEN', $department);
            $letter_template->setValue('NIK', $paper->id_card);
        }
        $letter_template->setValue('YEAR', date('Y', strtotime($paper->issue_date)));
        $letter_template->setValue('MONTH', date('m', strtotime($paper->issue_date)));
        $letter_template->setValue('DATE', date('d', strtotime($paper->issue_date)));
        $letter_template->setValue('HOUR', date('H:i', strtotime($paper->issue_date)));

        $letter_template->setValue('REASON', str_replace('&', '&amp;', $paper->reason));
        $letter_template->setValue('PERIOD_STAY', $paper->period_stay);
        $letter_template->setValue('DATE_IN', date('d-m-Y', strtotime($paper->date_in)));
        $letter_template->setValue('PERIOD_STAY_VISITOR', date('d-m-Y', strtotime($paper->period_stay)) . ' s/d selesai');
        $letter_template->setValue('DATE_IN_VISITOR', date('d-m-Y', strtotime($paper->date_in)) . ' s/d selesai');
        $letter_template->setValue('TRANSPORTATION', str_replace('&', '&amp;', $paper->transportation));
        $letter_template->setValue('JOURNEY', $paper->route);


        $letter_template->setValue('ID_CARD', $paper->id_card);
        $letter_template->setValue('EMAIL', $paper->email);
        $letter_template->setValue('NIP', $paper->id_card);
        $letter_template->setValue('CLINIC_RESPONSE', ($clinic_response) ? $clinic_response->clinic_response : '');
        $letter_template->setValue('TITLE1', ($occupation) ? $occupation : str_replace('&', '&amp;', $direct_superior->JobPosition));
        $letter_template->setValue('PREPARE_TITLE', str_replace('&', '&amp;', $occupation_created));
        $letter_template->setValue('HOST_TITLE', $occupation);
        $letter_template->setValue('POSITION', $occupation);
        $letter_template->setValue('DIVISION_AND_OCCUPATION', ($department . ' / ' . $occupation));
        $letter_template->setValue('COMPANY', $paper->company);
        $letter_template->setValue('BOOKINGREMARK', str_replace('&', '&amp;', $paper->reason));
        $letter_template->setValue('DATE_OUT', $paper->date_out);
        $letter_template->setValue('DESTINATION', $paper->destination);
        $letter_template->setValue('FULL_ADDRESS', $paper->address);
        // $letter_template->setValue('TRANSPORTATION', $paper->transportation);
        $letter_template->setValue('PAPER_NO', $paper->paper_no);
        $letter_template->setValue('EMP', $this->signatureName($paper->user_name));
        $letter_template->setValue('PREPAREBY', $this->signatureName($paper->user_name));
        $letter_template->setValue('REQUEST', $this->signatureName($paper->user_name));
        $letter_template->setValue('HOST_COMPANY', $this->signatureName($paper->user_name));
        $letter_template->setValue('EMP_NAME', $paper->user_name);
        $letter_template->setValue('EMP1', $this->signatureName($paper->created_name));
        $letter_template->setValue('EMP1_NAME', $paper->created_name);
        $letter_template->setValue('PREPARE_NAME', $paper->user_name);
        $letter_template->setValue('HOST_NAME', $paper->user_name);

        $bos = ($direct_superior) ? $direct_superior->DirectSuperiorName : '';
        $bos_title = ($direct_superior) ? $direct_superior->DirectSuperiorPosition : '';
        $letter_template->setValue('BOS', $this->signatureName($bos));
        $letter_template->setValue('SPV', $this->signatureName($bos));
        $letter_template->setValue('DIRECTOR', $this->signatureName('Hamid Mina'));
        $letter_template->setValue('DIRECTOR1', (($approvalStage1) ? $this->signatureName('Hamid Mina') : ''));
        $letter_template->setValue('DIR', $this->signatureName('Hamid Mina'));

        $letter_template->setValue('BOS_NAME', $bos);
        $letter_template->setValue('DIRECTOR_NAME', strtoupper('Hamid Mina'));
        $letter_template->setValue('DIR_NAME', strtoupper('Hamid Mina'));
        $letter_template->setValue('DIRECTOR_TITLE', strtoupper('Managing Director'));
        $letter_template->setValue('DIR_TITLE', strtoupper('Managing Director'));
        $letter_template->setValue('SPV_NAME', $bos);
        $letter_template->setValue('SPV_TITLE', $bos_title);
        if ($approval) {
            $letter_template->setValue('SPV1_NAME', $approval->Name);
            $letter_template->setValue('APPROVALNAME', $approval->Name);
            $letter_template->setValue('SPV1_TITLE', str_replace('&', '&amp;', $approval->employee->JobPosition));
            $letter_template->setValue('APPROVAL', $this->signatureName($approval->Name));
            $letter_template->setValue('SPV1', $this->signatureName($approval->Name));
            $letter_template->setValue('TITLE2', str_replace('&', '&amp;', $approval->employee->JobPosition));
            $letter_template->setValue('SPV1_NIK', $approval->employee->Nik);
        }

        if ($alias == 'smt') {
            if ($approvalRapid) {
                $letter_template->setValue('SPVRN', $this->signatureName($approvalRapid->Name));
                $letter_template->setValue('SPV_RAPID_NAME', $approvalRapid->Name);
                $letter_template->setValue('SPV_RAPID_TITLE', str_replace('&', '&amp;', $approvalRapid->employee->JobPosition));
                $letter_template->setValue('SPV_RAPID_NIK', $approvalRapid->employee->Nik);
            }
        }

        if ($alias == 'fsr' || $alias == 'abr') {
            $approvalStage = ViewApprovalStage::where('DocumentReferenceID', $paper->paper_no)
                // ->whereNotNull('ResponseDate')
                ->orderBy('Sequence', 'asc')
                ->get();

            // throw new \Exception(json_encode($approvalStage), 1);

            $table = new Table(
                array(
                    'borderSize' => 0,
                    // 'borderColor' => '#000000',
                    'borderColor' => '#ffffff',
                    "layout" => "fixed",
                    "width" => 100 * 50,
                    //word in 1/50th. 1% = 50 units
                    "unit" => "pct",
                    'align' => 'center',
                    'alignment' => 'center',
                )
            );
            $table->addRow();

            $rowStyle = ["unit" => "pct", 'alignment' => 'center', 'align' => 'center', 'size' => 10];
            $signStyle = ["unit" => "pct", 'size' => 10];
            $signStyleCell = [
                "unit" => "pct",
                'borderBottomColor' => '000000',
                'borderBottomSize' => 6,
                'spacing' => 100,
                'cellMargin' => 100,
                'size' => 10
            ];
            $signatureStyle = array('name' => 'Creattion Demo', 'size' => 22, 'bold' => true, 'valign' => 'center');

            $table->addCell(190, ['gridSpan' => 2])->addText('Prepare by (Disiapkan oleh)', $rowStyle);
            $table->addCell(190, ['gridSpan' => (count($approvalStage) * 2), 'alignment' => 'center', 'align' => 'center'])->addText('Approval By', $rowStyle, $rowStyle);

            $table->addRow(900, array("exactHeight" => true));
            $table->addCell(50, $rowStyle)->addText('', $rowStyle);
            $table->addCell(190, array('valign' => 'center'))->addText($this->signatureName($paper->user_name), $signatureStyle);
            for ($i = 0; $i < count($approvalStage); $i++) {
                $table->addCell(50, $rowStyle)->addText('', $rowStyle);
                $table->addCell(190, array('valign' => 'center'))->addText((($approvalStage[$i]->ResponseDate) ? $this->signatureName($approvalStage[$i]->Name) : null), $signatureStyle);
            }

            $table->addRow();

            $table->addCell(50, $rowStyle)->addText('Name', $rowStyle);
            $table->addCell(190, $signStyleCell)->addText($paper->created_name, $signStyle);

            for ($i = 0; $i < count($approvalStage); $i++) {
                $table->addCell(50, $rowStyle)->addText('Name', $rowStyle);
                $table->addCell(190, $signStyleCell)->addText($approvalStage[$i]->Name, $signStyle);
            }

            $table->addRow();

            $table->addCell(50, $rowStyle)->addText('Title', $rowStyle);
            $table->addCell(190)->addText(str_replace('&', '&amp;', $occupation_created), $signStyle);
            for ($i = 0; $i < count($approvalStage); $i++) {
                $table->addCell(50, $rowStyle)->addText('Title', $rowStyle);
                $table->addCell(190, $rowStyle)->addText(str_replace('&', '&amp;', $approvalStage[$i]->employee->JobPosition), $signStyle);
            }

            $letter_template->setComplexBlock('TABLE', $table);
        }

        $letter_template->setValue(
            'PAPER_DATE',
            Carbon::parse($paper->paper_date)
                ->locale('id')
                ->isoFormat('D MMMM Y')
        );

        $letter_template->setValue(
            'BOODATE',
            Carbon::parse($paper->paper_date)
                ->locale('id')
                ->isoFormat('D MMMM Y')
        );
        $letter_template->setValue('OTHER_PURPOSE', $paper->reason_purpose);
        $letter_template->setValue('UPDATED_BY', $paper->updated_name);
        $letter_template->setValue('NOTE', str_replace('&', '&amp;', $paper->notes));
        $letter_template->setValue('SEAT', $paper->total_seat);
        $letter_template->setValue('REQ_DATE', date('d-m-Y', strtotime($paper->request_date)));

        $flight_origin = Airport::where('id', '=', $paper->flight_origin)->first();
        $flight_origin = ($flight_origin) ? $flight_origin->code . ' - ' . $flight_origin->name : '';

        $flight_origin_approve = Airport::where('id', '=', $paper->flight_origin_approve)->first();
        $flight_origin_approve = ($flight_origin_approve) ? $flight_origin_approve->code . ' - ' . $flight_origin_approve->name : '';

        $letter_template->setValue('FLIGHT_ORIGIN', $flight_origin);
        $letter_template->setValue('ORIGIN', $flight_origin_approve);

        $flight_destination = Airport::where('id', '=', $paper->flight_destination)->first();
        $flight_destination = ($flight_destination) ? $flight_destination->code . ' - ' . $flight_destination->name : '';

        $flight_destination_approve = Airport::where('id', '=', $paper->flight_destination_approve)->first();
        $flight_destination_approve = ($flight_destination_approve) ? $flight_destination_approve->code . ' - ' . $flight_destination_approve->name : '';

        $letter_template->setValue('FLIGHT_DESTINATION', $flight_destination);
        $letter_template->setValue('DES', $flight_destination_approve);
        $letter_template->setValue('FDATE', $paper->flight_date_approve);
        $letter_template->setValue('FLIGHT_DAY', Carbon::parse($paper->request_date)->format('l'));
        $letter_template->setValue('NAME_BOSS', $paper->name_boss);
        if ($approval) {
            $letter_template->setValue('BOS1_NAME', $approval->Name);
            $letter_template->setValue('BOS1', $this->signatureName($approval->Name));
        }
        $letter_template->setValue('BOSS', $this->signatureName($paper->name_boss));
        $letter_template->setValue('BY', $this->signatureName($paper->user_name));
        $letter_template->setValue('POSITION_BOSS', str_replace('&', '&amp;', $paper->position_boss));
        $letter_template->setValue('NIK_BOS', $paper->nik_boss);
        $letter_template->setValue('PREPARE_BY', $paper->created_name);
        $letter_template->setValue('PREPARE_TITLE', str_replace('&', '&amp;', $occupation_created));
        $letter_template->setValue('SA', $paper->seat_available);
        $letter_template->setValue('TAS', $paper->total_seat);
        $letter_template->setValue('CHOST', $paper->host_company);
        $letter_template->setValue('VCOMPANY', $paper->visitor_company);
        $letter_template->setValue('DFROM', $paper->date_in);
        $letter_template->setValue('INDATE', date('d-m-Y', strtotime($paper->date_in)));
        $letter_template->setValue('DTO', date('d-m-Y', strtotime($paper->date_out)));
        $letter_template->setValue('OUTDATE', date('d-m-Y', strtotime($paper->date_out)));
        $letter_template->setValue('UCOMPANY', $paper->user_name);
        $letter_template->setValue('OHP', $paper->no_hp);
        $letter_template->setValue('OMAIL', $paper->company_email);
        $letter_template->setValue('VCOMPANY', $paper->visitor_officer);
        $letter_template->setValue('VHP', $paper->visitor_no_hp);
        $letter_template->setValue('VMAIL', $paper->visitor_email);
        $letter_template->setValue('VADDRESS', $paper->visitor_address);
        $letter_template->setValue('PURPOSE_VISIT', $paper->purpose_visit);
        $letter_template->setValue('GTOTAL', $paper->total_guest);
        $letter_template->setValue('GUEST', $paper->total_guest);
        $letter_template->setValue('OF', $paper->other_facilities);
        $letter_template->setValue('GNOTES', $paper->notes);
        $letter_template->setValue('REMARK', $paper->notes);
        $letter_template->setValue('COST', $paper->cost_cover);
        $letter_template->setValue('VEHICLE', $paper->vehicle);
        $letter_template->setValue('VEHICLENO', $paper->vehicle_no);
        $letter_template->setValue('PLACE', 'Jakarta');
        $letter_template->setValue('SIGN', 'Zulkifli Arman');

        $visit_area = $paper->plan_visit_area;
        // return response()->json($visit_area, 422);
        if ($alias != 'abr') {
            if (!empty($visit_area)) {
                $letter_template->setImageValue('Y1', [
                    'path' => (in_array('Smelter', $visit_area)) ?
                        public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                    'width' => 21,
                    'height' => 21,
                ]);

                $letter_template->setImageValue('Y2', [
                    'path' => (in_array('Power Plan', $visit_area)) ?
                        public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                    'width' => 21,
                    'height' => 21,
                ]);

                $letter_template->setImageValue('Y3', [
                    'path' => (in_array('Factory', $visit_area)) ?
                        public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                    'width' => 21,
                    'height' => 21,
                ]);

                $letter_template->setImageValue('Y4', [
                    'path' => (in_array('Mine Site', $visit_area)) ?
                        public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                    'width' => 21,
                    'height' => 21,
                ]);

                $letter_template->setImageValue('Y5', [
                    'path' => (in_array('Harbour', $visit_area)) ?
                        public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                    'width' => 21,
                    'height' => 21,
                ]);

                $letter_template->setImageValue('Y6', [
                    'path' => (in_array('All Area', $visit_area)) ?
                        public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                    'width' => 21,
                    'height' => 21,
                ]);
            }
        } else {
            $visit_area = str_replace('"', "", $paper->plan_visit_area);

            $letter_template->setImageValue('RK', [
                'path' => ($visit_area == 'Rumah Kayu (RK)') ?
                    public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);

            $letter_template->setImageValue('MK', [
                'path' => ($visit_area == 'Mess Kendari (MK)') ?
                    public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);
        }

        $facilities = $paper->facilities;
        if (!empty($facilities)) {
            $letter_template->setImageValue('F1', [
                'path' => (in_array('Meeting Room with', $facilities)) ?
                    public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);

            $letter_template->setImageValue('F2', [
                'path' => (in_array('LCD Projector & Dispay Screen', $facilities)) ?
                    public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);

            $letter_template->setImageValue('F3', [
                'path' => (in_array('Transport on site', $facilities)) ?
                    public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);

            $letter_template->setImageValue('F4', [
                'path' => (in_array('Other', $facilities)) ?
                    public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);
        } else {
            $letter_template->setImageValue('F1', [
                'path' => public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);

            $letter_template->setImageValue('F2', [
                'path' => public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);

            $letter_template->setImageValue('F3', [
                'path' =>  public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);

            $letter_template->setImageValue('F4', [
                'path' =>  public_path('images/icons_unchecked.png'),
                'width' => 21,
                'height' => 21,
            ]);
        }

        $letter_template->setImageValue('PAY1', [
            'path' => $paper->payment === 'Dibayar Tunai' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 27,
            'height' => 27,
        ]);

        $letter_template->setImageValue('C1', [
            'path' => $paper->resv_for === 'Own Purpose' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C2', [
            'path' => $paper->resv_for === 'Subordinate' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C3', [
            'path' => $paper->resv_for === 'Superior' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C4', [
            'path' => $paper->travel_purpose === 'Duty Travel' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C5', [
            'path' => $paper->travel_purpose === 'Family Visit/Yearly Leave' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C6', [
            'path' => $paper->travel_purpose === 'Special Permit' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C7', [
            'path' => $paper->travel_purpose === 'Others Purpose' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C8', [
            'path' => $paper->cost_cover === 'IMIP' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C9', [
            'path' => $paper->cost_cover === 'Guest Company' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('C10', [
            'path' => $paper->cost_cover === 'Contractor' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 21,
            'height' => 21,
        ]);

        $letter_template->setImageValue('PAY2', [
            'path' => $paper->payment !== 'Dibayar Tunai' ?
                public_path('images/icons_checked.png') : public_path('images/icons_unchecked.png'),
            'width' => 27,
            'height' => 27,
        ]);

        // $letter_template->setValue('FNO', $paper->flight_no);

        if (Str::contains($alias, ['rtm', 'smt'])) {
            if ($paper->line_items_count > 1) {
                $values = [];
                foreach ($paper->lineItems as $key => $value) {
                    $values[] = [
                        'NAMERAPID' => $value->name,
                        'NIKRAPID' => $value->id_card,
                        'IDCARDRAPID' => $value->id_card,
                        'KTPRAPID' => $value->national_id,
                        'DEPTRAPID' => str_replace('&', '&amp;', $value->department),
                        'DEPARTMENTRAPID' => str_replace('&', '&amp;', $value->department),
                        'COMPANYRAPID' => $value->company,
                        'POSITIONRAPID' => str_replace('&', '&amp;', $value->position),
                        'HPRAPID' => $value->phone_no,
                        'ADDRESSRAPID' => $value->address,
                    ];
                }

                // throw new \Exception(json_encode($values), 1);

                $letter_template->cloneRowAndSetValues('NAMERAPID', $values);
            }
        } elseif (Str::contains($alias, ['ibk'])) {
            $letter_template->setValue('STRIKEINOUT', ($paper->resv_for == 'OUT') ? 'keluar/' . 'm̶a̶s̶u̶k̶' : 'k̶e̶l̶u̶a̶r̶' . '/masuk');
            // throw new \Exception($paper->notes, 1);
            $letter_template->setValue('VNOTES', $paper->notes);
            $letter_template->setValue('STRIKEINOUT2', ($paper->resv_for == 'OUT') ? 'mengeluarkan/' . 'm̶e̶m̶a̶s̶u̶k̶k̶a̶n̶' : 'm̶e̶n̶g̶e̶l̶u̶a̶r̶k̶a̶n̶' . '/memasukkan');
            if ($paper->line_items_count > 1) {
                $values = [];
                foreach ($paper->lineItems as $key => $value) {
                    $values[] = [
                        'NO' => ($key + 1),
                        'ITEMNAME' => $value->item_name,
                        'UOM' => $value->uom,
                        'QTY' => $value->qty,
                        'NOTES' => $value->notes,
                    ];
                }

                // throw new \Exception(json_encode($values), 1);
                $letter_template->cloneRowAndSetValues('NO', $values);
            }

            if (count($paper->driver) > 0) {
                $value_driver = [];
                $table = new Table(
                    array(
                        'borderSize' => 0,
                        // 'borderColor' => '#000000',
                        'borderColor' => '#ffffff',
                        "layout" => "autofit",
                        "width" => 1000,
                        //word in 1/50th. 1% = 50 units
                        "unit" => "pct",
                        'align' => 'left',
                        'alignment' => 'left',
                    )
                );

                $rowStyle = ["unit" => "pct", 'alignment' => 'center', 'align' => 'center', 'size' => 10];
                $signStyle = ["unit" => "pct", 'size' => 10];

                foreach ($paper->driver as $key => $value) {
                    $table->addRow();
                    $table->addCell(50, $rowStyle)->addText(($key + 1) . ')', $rowStyle);
                    $table->addCell(190, $rowStyle)->addText($value, $rowStyle);
                }
                $letter_template->setComplexBlock('DRIVER', $table);
            }
        } else {
            $details = PaperDetails::where('paper_id', '=', $paper->id)->get();
            if (count($details) > 0) {
                $data_detail = [];
                $data_detail_booking = [];
                $data_detail_guest = [];
                $data_seat_no = [];
                $count_details = count($details);

                // $letter_template->cloneBlock('block_name', 3, true, true);
                $is_flight_reservation = false;
                $is_booking_request = false;
                $table = new Table(array('borderSize' => 2, 'borderColor' => 'black', 'width' => 2500, 'unit' => TblWidth::TWIP));
                $table->addRow();
                foreach ($details as $index => $detail) {
                    if (!empty($detail->nationality)) {
                        $is_flight_reservation = true;
                    }

                    if ($alias == 'abr') {
                        $is_booking_request = true;
                    }
                    $data_detail[] = [
                        'NO' => ($index + 1),
                        'PASSENGER_NAME' => $detail->name_title . ' ' . $detail->name,
                        'NATIONALITY' => $detail->nationality,
                        'ID_CARD_NO' => $detail->id_card,
                        'EMPLOYEE_TYPE' => $detail->employee_type,
                        'COMPANY_NAME' => $detail->company,
                        'SEAT_NO' => $detail->seat_no,
                    ];

                    $data_detail_booking[] = [
                        'NO' => ($index + 1),
                        'TIT' => $detail->name_title,
                        'GUESTNAME' => $detail->name,
                        'COMPANY' => $detail->company,
                        'COMPANY_NAME' => $detail->company,
                        'POSITION_NAME' => str_replace('&', '&amp;', $detail->position),
                        'TRANSPORT' => $detail->transport_from,
                        'ARRIVAL' => ($detail->arrival_date) ? date('d-m-Y', strtotime($detail->arrival_date)) : null,
                        'STATUS' => $detail->status,
                        'ROOM' => $detail->room_no,
                        'ALTERNATIVE' => $detail->alternative_room_no,
                    ];

                    $data_detail_guest[] = [
                        'NO2' => ($index + 1),
                        'PNAME' => $detail->name_title . ' ' . $detail->name,
                        'PPOSITION' => str_replace('&', '&amp;', $detail->position),
                        'BW' => $detail->body_weight,
                        'CDEP' => $detail->departing_city,
                        'ADATE' => ($detail->arrival_date) ? date('d-m-Y', strtotime($detail->arrival_date)) : null,
                        'AFN' => $detail->arrival_flight_no,
                        'ATIME' => ($detail->arrival_time) ? date('H:i:s', strtotime($detail->arrival_time)) : null,
                        'DDATE' => $detail->departure_date,
                        'DFN' => $detail->departure_flight_no,
                        'DTIME' => ($detail->departure_time) ? date('H:i:s', strtotime($detail->departure_time)) : null,
                        'CDES' => $detail->destination_city,
                        'TTO' => $detail->transport_to,
                        'TFROM' => $detail->transport_from,
                        'NOTES' => $detail->notes,
                    ];

                    $table->addCell(40)->addText(($index + 1) . '. ' . $detail->seat_no);
                }
                $letter_template->setComplexBlock('SEAT_NOS', $table);
                $letter_template->setValue('FLIGHT_NO', $paper->flight_no);

                if ($is_flight_reservation) {
                    //$letter_template->cloneRowAndSetValues('NO1', $data_seat_no);
                    $letter_template->cloneRowAndSetValues('NO', $data_detail);
                } elseif ($is_booking_request) {
                    $letter_template->cloneRowAndSetValues('NO', $data_detail_booking);
                } else {
                    $letter_template->cloneRowAndSetValues('NO2', $data_detail_guest);
                }
            }
        }

        $file_path_name = base_path(
            'public/paper/' . $file_export_name . '.docx'
        );


        $letter_template->saveAs($file_path_name);

        // convert to pdf
        $pdf_path_name = public_path('documents/');
        if (!file_exists($pdf_path_name)) {
            if (!mkdir($pdf_path_name, 0777, true) && !is_dir($pdf_path_name)) {
                throw new \RuntimeException(
                    sprintf(
                        'Directory "%s" was not created',
                        $pdf_path_name
                    )
                );
            }
        }

        $pdf_file_name = $pdf_path_name . $file_export_name . '.pdf';

        // $serviceConvert = new ConvertDocxToPdfService();
        // $pathToSavingDirectory = public_path('/paper');
        // $pdfFileName =  $pdf_path_name . $file_export_name . '.pdf';
        // $serviceConvert->convert($file_path_name, $pathToSavingDirectory, $pdfFileName);

        // Create PDF File
        // $this->createPdfFile($pdf_file_name, $file_path_name);
        $serviceConvert = new ConvertDocxToPdfService();
        $pathToSavingDirectory = $pdf_path_name;
        $pdfFileName =  $file_export_name . '.pdf';
        $serviceConvert->convert($file_path_name, $pathToSavingDirectory, $pdfFileName);
        // $this->createPdfFile($pdf_file_name, $file_path_name);

        $headers = [
            'Content-Type' => 'application/pdf',
        ];

        // Update print document date
        $paper->print_date = Carbon::now();
        $paper->save();

        unlink(public_path('images/qrcode/' . $file_export_name . '.png'));

        if ($alias === 'stkpd') {
            $pdfMerger = PDFMerger::init(); //Initialize the merger
            $pdfMerger->addPDF($pdf_file_name, 'all');
            $pdfMerger->addPDF(public_path('template/IMIP_IOMKI.pdf'), 'all');
            $pdfMerger->merge(); //For a normal merge (No blank page added)
            $pdfMerger->save($pdf_file_name);
        }

        $all_files = [
            $file_path_name,
            $pdf_file_name,
            $qrImagePath
        ];
        // Remove Attachment
        RemoveAttachment::dispatch($all_files)->delay(now()->addMinutes(5));

        return [
            'pdf_file_name' => $pdf_file_name,
            'file_export_name' => $file_export_name,
            'headers' => $headers,
        ];
    }

    /**
     * @param $pdf_file_name
     * @param $file_path_name
     */
    protected function createPdfFile($pdf_file_name, $file_path_name)
    {
        $word_file = new \COM('Word.Application') or die('Could not initialise Object.');
        $word_file->Visible = 0;
        $word_file->DisplayAlerts = 0;
        $word_file->Documents->Open($file_path_name);
        $word_file->ActiveDocument->ExportAsFixedFormat(
            $pdf_file_name,
            17,
            false,
            0,
            0,
            0,
            0,
            7,
            true,
            true,
            2,
            true,
            true,
            false
        );
        // quit the Word process
        $word_file->Quit(false);
        // clean up
        unset($word_file);

        unlink($file_path_name);
    }


    /**
     * @param $path
     */
    protected function generatePath($path)
    {
        if (!file_exists($path)) {
            if (!mkdir($path, 0777, true) && !is_dir($path)) {
                throw new \RuntimeException(
                    sprintf(
                        'Directory "%s" was not created',
                        $path
                    )
                );
            }
        }
    }

    /**
     * @param $name
     * @return string
     */
    protected function signatureName($name): string
    {
        $without_space = str_replace(' ', '', $name);
        $limit = substr($without_space, 0, 10);
        return ucwords(strtolower($limit));
    }

    /**
     * @param $request
     *
     * @return void
     */
    public function cancelDocument($request)
    {
        if ($request->form['status'] == 'canceled') {
            $cherry_token = Auth::user()->cherry_token;

            $headers = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ];

            $documents = Http::withHeaders($headers)
                ->post(config('app.cherry_service_req'), [
                    'CommandName' => 'GetList',
                    'ModelCode' => 'GADocuments',
                    'UserName' => Auth::user()->username,
                    'Token' => $cherry_token,
                    'ParameterData' => [
                        [
                            'ParamKey' => 'DocumentReferenceID',
                            'ParamValue' => $request->form['paper_no'],
                            'Operator' => 'eq'
                        ]
                    ]
                ]);

            if ($documents['MessageType'] == 'error') {
                throw new \Exception($documents->collect()['Message'], 1);
            }

            $collect = $documents->collect();

            if (count($collect['Data']) == 0) {
                throw new \Exception('There are no GA Documents For This Document!', 1);
            }


            Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'Remove',
                'ModelCode' => 'GADocuments',
                'UserName' => Auth::user()->username,
                'Token' => $cherry_token,
                'ModelData' => $collect['Data'][0]
            ]);
        }
    }

    /**
     * @param $request
     * @param $paper
     *
     * @return void
     */
    public function sendEmail($request, $paper)
    {
        if (array_key_exists('clinic_response', $request->form)) {
            $response = $request->form['clinic_response'];
            $user = ViewEmployee::where("Nik", "=", $paper->created_by)->first();
            $email = null;
            if ($user->OfficeEmailAddress) {
                $email = $user->OfficeEmailAddress;
            } else {
                $email = $user->PrivateEmailAddress;
            }

            $receiver = null;

            if ($email) {
                $receiver = [
                    $email
                ];
            }
            $cc_email = ['<EMAIL>'];
            if (isset($response)) {
                if ($response == 'Negatif') {
                    $subject = "Hasil RAPID KLINIK IMIP: Negatif";
                    $content = "Berdasarkan hasil Rapid test karyawan atas nama " . $paper->user_name
                        . " (" . $paper->id_card . ") tanggal " . $paper->swab_date . " dinyatakan Negatif,
                            silahkan untuk lanjut membuat surat izin masuk kawasan!";

                    $dataPaper = Paper::where('reference_no', $paper->paper_no)->first();
                    $dataPaper->status = 'active';
                    $dataPaper->save();
                    if ($receiver) {
                        SendClinicNotificationEmail::dispatch(
                            $subject,
                            $content,
                            $receiver,
                            $paper->created_name,
                            $cc_email
                        );
                    }
                } elseif ($response == 'Positif') {
                    $subject = "Hasil RAPID KLINIK IMIP: Positif";
                    $content = "Berdasarkan hasil Rapid test karyawan atas nama " . $paper->user_name
                        . " (" . $paper->id_card . ") tanggal " . $paper->swab_date . " dinyatakan Positif,
                            silahkan menghubungi HRD untuk info lebih lanjut!";

                    if ($receiver) {
                        SendClinicNotificationEmail::dispatch(
                            $subject,
                            $content,
                            $receiver,
                            $paper->created_name,
                            $cc_email
                        );
                    }
                }
            }
        }
    }

    /**
     * @throws \Exception
     */
    public function validateDocument($request, $alias)
    {
        if (empty($request->form['paper_date'])) {
            throw new \Exception('Tanggal Surat Tidak Boleh Kosong!', 1);
        }

        if (!Str::contains($request->alias, ['rtm', 'smt'])) {
            if (empty($request->form['user_name'])) {
                throw new \Exception('Karyawan Tidak Boleh Kosong!', 1);
            }
        }

        if ($alias == 'sim' || $alias == 'sik' || $alias == 'srm' || $alias == 'srk') {
            if (array_key_exists('work_location', $request->form)) {
                if (str_contains($request->form['work_location'], 'MOROWALI')) {
                    if ($request->form['for_self'] == 'Karyawan') {
                        if ($alias == 'sim' && $request->form['swab_type'] != '3') {
                            if (empty($request->form['reference_no'])) {
                                throw new \Exception('Nomor Rapid tidak boleh kosong!', 1);
                            }
                        }
                        if (empty($request->form['leave_from_to']) || empty($request->form['reference_number'])) {
                            throw new \Exception('Tanggal Cuti dan Nomor Cuti tidak boleh kosong!', 1);
                        }
                    }
                    $master_paper = $this->getMasterPaper($request->alias);
                    $check_paper = Paper::where('master_paper_id', '=', $master_paper->id)
                        ->where('reference_number', '=', $request->form['reference_number'])
                        ->where('user_id', '=', $request->username)
                        ->where('user_name', '=', $request->form['user_name'])
                        ->whereNotIn('status', ['canceled', 'rejected'])
                        ->count();
                    if ($check_paper > 0) {
                        throw new \Exception($master_paper->name . ' dengan Nomer cuti ' .
                            $request->form['reference_number'] .
                            ' Sudah ada, Silahkan pilih nomer yang lain!', 1);
                    }
                }
            }
        }

        if ($alias == 'sim') {
            if (
                date('Y-m-d', strtotime($request->form['date_in']))
                < date('Y-m-d', strtotime($request->form['paper_date']))
            ) {
                throw new \Exception('Tanggal masuk kawasan harus melebihi tanggal surat!', 1);
            }
        }

        if ($alias == 'sik') {
            if (
                date('Y-m-d', strtotime($request->form['date_out']))
                < date('Y-m-d', strtotime($request->form['paper_date']))
            ) {
                throw new \Exception('Tanggal keluar kawasan harus melebihi tanggal surat!', 1);
            }
        }

        // if ($alias == 'srm') {
        //     if (empty($request->form['date_in'])) {
        //         return $this->error('Tanggal masuk kawasan tidak boleh kosong!', 422);
        //     }

        //     if (empty($request->form['transportation'])) {
        //         return $this->error('Transportasi tidak boleh kosong!', 422);
        //     }

        //     if (empty($request->form['route'])) {
        //         return $this->error('Jalur tidak boleh kosong!', 422);
        //     }

        //     if (empty($request->form['reason'])) {
        //         return $this->error('Keperluan masuk kawasan tidak boleh kosong!', 422);
        //     }
        // }

        if ($alias == 'srm' || $alias == 'srk' || $alias == 'rtm' || $alias == 'rtk') {
            if (empty($request->form['swab_date'])) {
                throw new \Exception('Tanggal Swab tidak boleh kosong!', 1);
            }
            if (empty($request->form['name_boss'])) {
                throw new \Exception('Nama Atasan tidak boleh kosong!', 1);
            }
            if (!empty($request->form['swab_date'])) {
                // if (date('Y-m-d', strtotime($request->form['swab_date']))
                //     < date('Y-m-d', strtotime(Carbon::now()))) {
                //     return $this->error('Tanggal Swab tidak boleh kurang dari tannggal sekarang!');
                // }
                if (
                    date('Y-m-d', strtotime($request->form['swab_date']))
                    <= date('Y-m-d', strtotime(Carbon::now()))
                ) {
                    if (empty($request->form['reason_swab'])) {
                        throw new \Exception('Alasan tanggal Swab tidak boleh kosong!', 1);
                        return $this->error('Alasan tanggal Swab tidak boleh kosong!');
                    }
                }
            }
        }
    }
}
