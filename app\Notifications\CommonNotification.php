<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CommonNotification extends Notification
{
    use Queueable;

    // public array $content;
    // public array $documentId;
    // public array $documentType;

    /**
     * Create a new notification instance.
     */
    public function __construct(public array $content, public string $documentId, public string $documentType, public string $transactionType)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'documentType' => $this->documentType,
            'transactionType' => $this->transactionType,
            'documentId' => $this->documentId,
            'documentNumber' => $this->content["document"]->document_number,
            'title' =>  $this->content['title'],
            'content' => $this->content['document'],
        ];
    }
}
