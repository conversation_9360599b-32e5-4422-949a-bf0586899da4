<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Services\SapS4Service;
use Illuminate\Http\Request;
use Illuminate\Container\Container;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Log;
use App\Support\Collection;

class UomController extends Controller
{
    public function index(Request $request)
    {
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 10;
        $search = isset($request->search) ? (string) $request->search : null;

        $service = new SapS4Service();

        $service->login();

        $items = $service->getUom($pages, 1000, $search);
        $total = 0;
        $data = [];
        if ($items) {
            if (!array_key_exists('DATA', $items)) {
                return $this->error('Error', '422', [
                    $items
                ]);
            }
            foreach ($items['DATA'] as $index => $item) {
                $data[] = [
                    "Keys" => $index,
                    "MSEHL" => $item["MSEHL"],
                    "MSEHT" => $item["MSEHT"],
                    "MSEHI" => $item["MSEHI"],
                ];
            }
        }


        // $collection = collect($data);

        // if ($search) {
        //     $filtered = $collection->whereIn("MSEHI", [$search])->all();
        //     if (count($filtered) == 1) {
        //         $filtered = array_values($filtered);
        //         // $filtered = collect($filtered->all())->all();
        //     }
        // } else {
        //     $filtered = $collection->all();
        // }

        // $paginatedData = array_slice($filtered, ($pages - 1) * $row_data, $row_data);


        // return response()->json($this->paginate(collect($filtered), $row_data, $pages));
        // return response()->json($collect);
        // return response()->json($this->paginateData($filtered, $row_data, $pages));
        return response()->json([
            'data' => $data,
            'total' => count($data)
        ]);
    }

    public function paginateData($items, $perPage = 5, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }

    public static function paginate(Collection $results, $showPerPage, $pages)
    {
        // $pageNumber = Paginator::resolveCurrentPage('page');
        $pageNumber = $pages;

        $totalPageNumber = $results->count();

        return self::paginator($results->forPage($pageNumber, $showPerPage), $totalPageNumber, $showPerPage, $pageNumber, [
            'path' => Paginator::resolveCurrentPath(),
            'pageName' => 'page',
        ]);

    }

    /**
     * Create a new length-aware paginator instance.
     *
     * @param  \Illuminate\Support\Collection  $items
     * @param  int  $total
     * @param  int  $perPage
     * @param  int  $currentPage
     * @param  array  $options
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    protected static function paginator($items, $total, $perPage, $currentPage, $options)
    {
        return Container::getInstance()->makeWith(
            LengthAwarePaginator::class,
            compact(
                'items',
                'total',
                'perPage',
                'currentPage',
                'options'
            )
        );
    }
}
