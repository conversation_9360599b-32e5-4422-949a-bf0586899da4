<?php

namespace App\Services\Reports;

use App\Models\Resv\ResvHeader;
use App\Models\Resv\ResvDetail;
use App\Services\SapS4Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ReportService
{
    public function getS4Docs($rows, $reportType = "Detail")
    {
        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();

        $rows = (is_array($rows)) ? collect($rows) : collect($rows);

        $dataFlow = [];
        if ($reportType == "Header") {
            $flow = $service->getFlow($rows->pluck('DocNum')->toArray());
            if ($flow && array_key_exists('DATA', $flow) && $flow['DATA'] != 'NULL') {
                $flowData = collect($flow['DATA']);

                $rows = $rows->map(function ($row) use ($flowData) {
                    $match = $flowData->firstWhere('DocNum', $row['ERESERVASI']);

                    if ($match) {
                        $row['DocNum'] = $match["ERESERVASI"];
                        $row['SAP_GIRNo'] = $match["RESERVASI"];
                        $row['SAP_PRNo'] = $match["PR"];
                        $row['PONum'] = $match["PO"];
                        $row['GRPONum'] = $match["GR"];
                        $row['SAP_GINo'] = $match["GI"];
                    }

                    return $row;
                });
            }
        } else {
            $flow = $service->getFlowDetail($rows->pluck('DocNum')->toArray());
            if ($flow && array_key_exists('DATA', $flow) && $flow['DATA'] != 'NULL') {
                $flowData = collect($flow['DATA']);

                $rows = $rows->map(function ($row) use ($flowData) {
                    $match = $flowData->Where('I_ERESERVASI', $row->LineNum)->where('ERESERVASI', $row->DocNum)->first();

                    if ($match) {
                        $row->SAP_GIRNo = $match["RESERVASI"];
                        $row->SAP_PRNo = $match["PR"];
                        $row->PONum = $match["PO"];
                        $row->GRPONum = $match["GR"];
                        $row->SAP_GINo = $match["GI"];
                    }

                    return $row;
                });
            }
        }

        return $rows;
    }

    public function isStdClass($var)
    {
        return is_object($var) && get_class($var) === 'stdClass';
    }

    public function queryIssue(Request $request, array $whs, array $itemGroup)
    {
        $date_from = $request->dateFrom;
        $date_to = $request->dateTo;

        $rows = ResvHeader::select(
            "resv_headers.DocNum",
            "resv_headers.DocDate",
            "resv_headers.RequiredDate",
            "resv_headers.Department",
            "resv_headers.WhsCode",
            "resv_headers.WorkLocation",
            "resv_headers.ItemType",
            DB::raw('resv_headers."RequestType" as "HeaderRequestType"'),
            "resv_headers.ItemType",
            "resv_headers.RequesterName",
            "resv_headers.Memo",
            "resv_details.ItemCode",
            "resv_details.ItemName",
            "resv_details.ReqQty",
            "resv_details.ReqNotes",
            "resv_details.UoMCode",
            "resv_details.LineNum",
            "resv_details.EmployeeId",
            "resv_details.EmployeeName",
            "resv_details.EmployeeName",
            DB::raw("CONVERT(varchar,inventories.updated_at,23) as updated_at"),
            DB::raw("(CONCAT(inventories.prefix,FORMAT(inventories.doc_number, '00000'), inventories.suffix)) as InternalGiNo")
        )
            ->leftJoin("resv_details", "resv_details.U_DocEntry", "resv_headers.U_DocEntry")
            ->leftJoin("inventory_details", "inventory_details.resv_detail_id", "resv_details.LineEntry")
            ->leftJoin("inventories", "inventories.id", "inventory_details.header_id")
            // ->where("inventory_details.resv_detail_id", "resv_details.LineEntry")
            ->where("inventories.doc_type", "out")
            ->whereRaw("resv_headers.DocDate BETWEEN '$date_from' AND '$date_to' ")
            // ->whereRaw("resv_headers.\"ItemType\" LIKE '%${item_type}%'")
            ->where("resv_headers.ApprovalStatus", "=", "Y")
            ->when($request, function ($query) use ($request) {
                if (!$request->user()->hasAnyRole(['Superuser'])) {
                    $query->where("resv_headers.WorkLocation", "=", $request->user()->location);
                }
            })
            ->whereIn("resv_details.ItemGroup", $itemGroup)
            ->whereIn("resv_headers.WhsCode", $whs)
            ->get();

        $pluck = $rows->pluck('DocNum')->toArray();

        $service = new ReportService();
        $rows = $service->getS4Docs($rows);

        $rows = $this->getQtyIssueDetailS4($rows, $pluck);
        return $rows;
    }

    public function getQtyIssueDetailS4($rows, array $pluck)
    {
        // Initialize service sap s4
        $service = new SapS4Service();

        $flow = $service->getGoodIssue($pluck);

        $dataFlow = [];

        if ($flow) {
            if (array_key_exists('DATA', $flow)) {
                if ($flow['DATA'] != 'NULL') {
                    foreach ($flow['DATA'] as $index => $item) {
                        $dataFlow[] = [
                            "DocNum" => (array_key_exists('ERESV', $flow['DATA'][$index]))
                                ? $flow['DATA'][$index]['ERESV'] : null,
                            "IssueQTY" => (array_key_exists('MATDOC_QTY', $flow['DATA'][$index]))
                                ? $flow['DATA'][$index]['MATDOC_QTY'] : null,
                            "ItemCode" => (array_key_exists("MATNR", $flow['DATA'][$index]))
                                ? (int) $flow['DATA'][$index]['MATNR'] : null,
                        ];
                    }
                }
            }
        }

        $collectFlowFix = collect($dataFlow);
        $collectionFix = collect($rows);
        // Iterate through the collectionFix
        $collectionFix->each(function ($item) use ($collectFlowFix) {
            $item = ($this->isStdClass($item)) ? (array) $item : $item;
            $filteredFix = $collectFlowFix->whereIn('DocNum', [$item['DocNum']])
                ->whereIn('ItemCode', [$item['ItemCode']])
                ->first();

            if ($filteredFix) {
                $item['IssueQTY'] = $filteredFix['IssueQTY'];
            } else {
                $item['IssueQTY'] = null;
            }
        });

        return $collectionFix->all();
    }
}
