<?php

namespace App\Http\Controllers\Enviro;

use App\Http\Controllers\Controller;
use App\Models\Enviro\EnviroActivity;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;

class EnviroActivityController extends Controller
{
    /**
     * MasterUserController constructor.
     */
    public function __construct()
    {
        $this->middleware(['direct_permission:Enviro Activity-index'])->only(['index', 'show', 'permissionRole']);
        $this->middleware(['direct_permission:Enviro Activity-store'])->only(['store', 'storePermissionRole']);
        $this->middleware(['direct_permission:Enviro Activity-edits'])->only('update');
        $this->middleware(['direct_permission:Enviro Activity-erase'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "id";
        $order = isset($request->sortBy[0]) ? (string) $options->sortDesc[0] : "desc";
        $offset = $pages;

        $result = array();
        $query = DB::table('enviro_activities as a')
            ->leftJoin('enviro_sub_roles as b', 'a.sub_role_id', 'b.id')
            ->selectRaw("a.*, 'actions' as ACTIONS, b.name as sub_role_name");

        $result["total"] = $query->count();

        $all_data = $query->offset($offset)
            ->orderBy($sorts, $order)
            ->limit($row_data)
            ->get();


        $result = array_merge($result, [
            "rows" => $all_data,
            "form" => $this->getDefaultForm()
        ]);
        return $this->success($result);
    }

    /**
     * @return array
     */
    protected function getDefaultForm()
    {
        $defaults = Schema::getColumnListing('enviro_activities');
        $form = collect();
        foreach ($defaults as $index => $default) {
            $form->push([
                $default => null
            ]);
        }

        $flattened = $form->flatMap(function ($values) {
            return $values;
        });

        return $flattened->all();
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function subRole(Request $request)
    {
        $sub_role = DB::table('enviro_sub_roles as a')
            ->leftJoin('enviro_user_sub_roles as b', 'a.id', 'b.enviro_sub_role_id')
            ->select('a.name', 'a.id')
            ->where('b.user_id', $request->user()->id)
            ->get();
        return response()->json($sub_role);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['created_by'] = $request->user()->id;
            $data['created_at'] = Carbon::now();
            if (empty($data['id'])) {
                Arr::forget($data, 'id');
            }
            EnviroActivity::create($data);
            DB::commit();

            return $this->success([
                "errors" => false
            ], 'Data inserted!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $validator = Validator::make($request->all(), [
            'problem_desc' => 'required',
        ]);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): \Illuminate\Http\JsonResponse
    {
        $data = EnviroActivity::where("id", "=", $id)->first();

        return $this->success([
            'rows' => $data,
            "form" => $this->getDefaultForm()
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = $request->user()->id;
            $data['updated_at'] = Carbon::now();
            if (!empty($data['id'])) {
                Arr::forget($data, 'id');
            }

            EnviroActivity::where("id", "=", $id)->update($data);
            DB::commit();

            return $this->success([
                "errors" => false
            ], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): \Illuminate\Http\JsonResponse
    {
        $details = EnviroActivity::where("id", "=", $id)->first();
        if ($details) {
            EnviroActivity::where("id", "=", $id)->delete();
            return $this->success([
                "errors" => false
            ], 'Row deleted!');
        }

        return $this->error('Row not found', 422, [
            "errors" => true
        ]);
    }
}
