<?php

namespace App\Jobs;

use App\Models\Common\Attachment;
use App\Services\ApprovalDigisign;
use App\Traits\ApiResponse;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;

class ProcessSubmitApprovalDigisign implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use ApiResponse;

    public $tries = 1;
    protected $document;
    protected $fileName;
    protected $userId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($document, $fileName, $userId)
    {
        $this->document = $document;
        $this->fileName = $fileName;
        $this->userId = $userId;
        $this->onQueue('digitalSign');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        sleep(5);
        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');

        $document = $this->document;
        if ($document->digisign_coordinate == 'Y') {
            // $fileName = $this->fileName;
            $fileName = Attachment::where('source_id', $document->id)
                ->where('type', 'peruri')
                ->first();

            $dataCheck = [
                'document_id' => $document->id,
                'name' => 'Submit Digital Sign',
            ];
            try {
                $service = new ApprovalDigisign();
                $service->digitalSign($document, $fileName, $dataCheck, []);
            } catch (\Exception $exception) {
                $dataUpdate = [
                    'batch_id' => 0,
                    'status' => 'Processing 100%',
                    'callback_message' => 'Failed sign document: ' . $exception->getMessage(),
                    'callback_trace' => ''
                ];
                $this->createApprovalBatchLog($dataCheck, $dataUpdate);
                $this->fail($exception);
                throw new \Exception('E-DIGITALSIGN: ' . $exception->getMessage(), 1);
            }
        }
    }
}
