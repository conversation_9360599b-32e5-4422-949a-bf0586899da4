worker_processes  auto;

error_log /dev/stderr error;
pid /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format main '[nginx] $remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    sendfile        on;
    tcp_nopush     on;
    tcp_nodelay    on;
    keepalive_timeout  120;
    types_hash_max_size 2048;

    client_header_buffer_size 2k;
    large_client_header_buffers 4 8k;

    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;

    proxy_read_timeout 999999;
    client_max_body_size 4096M;

    gzip  on;
    gzip_disable "msie6";

    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;

    access_log /dev/stdout main;

    server {
        listen ${NGINX_PORT};
        root /opt/laravel/public;

        index index.php index.html index.htm;

        location / {
            try_files $uri $uri/ /index.php?$query_string;
            gzip_static on;
        }

        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass localhost:9000;
            fastcgi_index index.php;
            include fastcgi_params;
            # Block httpoxy attacks. See https://httpoxy.org/.
            fastcgi_param HTTP_PROXY "";
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;

            # FastCGI buffering settings
            fastcgi_buffering on;
            fastcgi_buffers 16 32k;
            fastcgi_buffer_size 64k;
            fastcgi_busy_buffers_size 128k;
            fastcgi_temp_file_write_size 128k;

            fastcgi_connect_timeout 1000s;  # Increase connect timeout
            fastcgi_send_timeout 1000s;     # Increase send timeout
            fastcgi_read_timeout 1000s;     # Increase read timeout
        }

        error_page 404 /index.php;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
