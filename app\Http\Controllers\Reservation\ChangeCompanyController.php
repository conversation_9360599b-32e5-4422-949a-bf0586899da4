<?php

namespace App\Http\Controllers\Reservation;

use App\Http\Controllers\Controller;
use App\Models\User\UserWhs;
use App\Services\SapS4Service;
use Illuminate\Http\Request;

class ChangeCompanyController extends Controller
{
    public function getWhs(Request $request)
    {
        if (str($request->company)->contains(["IMIP"])) {
            $companyHeader = "PT IMIP";
        } elseif (str($request->company)->contains(["BDM"])) {
            $companyHeader = "PT BDM";
        } elseif (str($request->company)->contains(["BDW"])) {
            $companyHeader = "PT BDW";
        }
        $user_id = $request->user()->id;
        $service = new SapS4Service();
        $service->login();

        $user_wh = UserWhs::where("user_id", $user_id)->get()->pluck('whs_code');
        $result = [];

        $plant = $service->getPlanByCompany($companyHeader);

        $company = $request->user()->company;
        $user = $request->user();
        $resultWh = $service->getSloc(1, 100, null, $company, $user, $companyHeader);

        $arr = [];
        if ($resultWh) {
            foreach ($resultWh['DATA'] as $itemWh) {
                // if (Str::contains($itemWh['LGORT'], (array)$user_wh)) {
                // }
                $arr[] = [
                    "name" => $itemWh['LGORT'] . ' - ' . $itemWh['LGOBE'],
                    "whs_name" => $itemWh['LGOBE'],
                    "whs_code" => $itemWh['LGORT'],
                    "code" => $itemWh['LGORT'],
                    "plant" => $itemWh['WERKS'],
                ];
            }
        }

        $filtered = collect($arr);
        $resultUserWh = $filtered->whereIn('whs_code', $user_wh)->whereIn('plant', $plant);
        $result['itemUserWhs'] = collect($resultUserWh)->values()->toArray();
        $result['resultWh'] = $resultWh;

        return response()->json($result);
    }
}
