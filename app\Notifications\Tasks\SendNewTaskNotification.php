<?php

namespace App\Notifications\Tasks;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SendNewTaskNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $task;
    protected $user;
    protected $user_created;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($task, $user, $user_created)
    {
        $this->task = $task;
        $this->user = $user;
        $this->user_created = $user_created;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = config('app.front_url') . '/tasks?board='
            . $this->task->board_id . '&department='
            . $this->task->department . '&userId='
            . $this->user->id . '&display=list';

        return (new MailMessage)
            ->subject($this->user_created . ' created new task on  ' . $this->task->department . ' board')
            ->line('Hey ' . $this->user->name . ', '
                . $this->user_created . ' created task:')
            ->line($this->task->title)
            ->line(' on ' . $this->task->department . ' board ')
            ->action('Go to Task', $url)
            ->line('This is auto generate message!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'source_id' => $this->task->board_id,
            'department' => $this->task->department,
            'type' => 'task'
        ];
    }
}
