<?php

namespace App\Traits;

use App\Models\Document\BatchApproval;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait ApiResponse
{
    /**
     * Return a success JSON response.
     *
     * @param array|string $data
     * @param string|null $message
     * @param int|null $code
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function success($data, string $message = null, int $code = 200): JsonResponse
    {
        return response()->json([
            'status' => 'Success',
            'message' => $message,
            'data' => $data
        ], $code);
    }

    /**
     * Return an error JSON response.
     *
     * @param string|null $message
     * @param int $code
     * @param array|string|null $data
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function error(string $message = null, int $code = 422, $data = null): JsonResponse
    {
        Log::error('error response ' . $message);
        return response()->json([
            'status' => 'Error',
            'message' => $message,
            'data' => $data
        ], $code);
    }

    /**
     * Return a success JSON response.
     *
     * @param array|string $data
     * @param string|null $message
     * @param int|null $code
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function successApps($data, string $message = null, int $code = 200, $request): JsonResponse
    {
        $merge = [
            'UserName' => $request->UserName,
            'Token' => $request->Token,
        ];
        return response()->json(array_merge([
            'MessageType' => 'Success',
            'MessageTitle' => 'Submit success',
            'Message' => $message,
            'Data' => $data,
            'AlertMessage' => true,
            'Secure' => false,
        ], $merge), $code);
    }

    /**
     * Return an error JSON response.
     *
     * @param string|null $message
     * @param int $code
     * @param array|string|null $data
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function errorApps(string $message = null, int $code = 200, $data = null, $request): \Illuminate\Http\JsonResponse
    {
        $merge = [
            'UserName' => $request->UserName,
            'Token' => $request->Token,
        ];
        return response()->json(array_merge([
            'MessageType' => 'Error',
            'MessageTitle' => 'Error',
            'Message' => $message,
            'Data' => $data,
            'AlertMessage' => true,
            'Secure' => false,
        ], $merge), $code);
    }

    /**
     * @param $table
     * @return array
     */
    protected function form($table): array
    {
        $forms = DB::getSchemaBuilder()->getColumnListing($table);
        $arr_form = [];
        foreach ($forms as $form) {
            $arr_form[$form] = null;
        }
        return $arr_form;
    }

    /**
     * @param $log_id
     * @param $log_type
     * @param $event_name
     * @param $user_id
     * @return void
     */
    protected function createLog($log_id, $log_type, $event_name, $user_id)
    {
        DB::table('log_documents')->insert([
            'log_id' => $log_id,
            'log_type' => $log_type,
            'event_name' => $event_name,
            'user_id' => $user_id,
            'created_at' => Carbon::now()
        ]);
    }

    /**
     * @param $dataCheck
     * @param $dataUpdate
     * @return void
     */
    protected function createApprovalBatchLog($dataCheck, $dataUpdate)
    {
        BatchApproval::updateOrCreate($dataCheck, $dataUpdate);
    }
}
