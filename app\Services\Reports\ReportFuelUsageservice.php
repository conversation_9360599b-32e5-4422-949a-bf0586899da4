<?php

namespace App\Services\Reports;

use App\Services\Reports\ReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReportFuelUsageService
{
    public function show(Request $request, string $date_from, string $date_to)
    {

        $department = $request->user()->userDivision()->pluck('division_name')->toArray();
        $authUser = $request->user();
        $rows = DB::connection('sqlsrv')
            ->table("resv_headers")
            ->join("resv_details", "resv_details.U_DocEntry", "resv_headers.U_DocEntry")
            ->where("resv_headers.CategoryType", "Fuel")
            ->whereBetween("resv_headers.DocDate", [$date_from, $date_to])
            ->when($authUser, function ($query) use ($authUser, $department) {
                if (!$authUser->hasAnyRole(['Superuser'])) {
                    $query->whereIn("resv_headers.Division", $department);
                }
            })
            ->select(
                'resv_headers.DocNum',
                'resv_headers.WorkLocation',
                'resv_headers.ItemType',
                'resv_headers.DocDate',
                'resv_details.ItemName',
                'resv_details.ReqQty',
                "resv_details.LineNum",
                'resv_details.UoMCode',
                'resv_headers.Memo',
                'resv_headers.RequesterName',
                'resv_headers.Department',
                'resv_headers.SAP_GIRNo',
                'resv_headers.WorkLocation',
                DB::raw("'Y' as DocStatus")
            )->get()->toArray();

        $service = new ReportService();
        return $service->getS4Docs($rows);
    }

    public function header()
    {
        return ['DocNum', 'Request Date', 'Item Name', 'Qty', 'UoM', 'Notes', 'RequesterName', 'Department', 'SAP_GIRNo'];
    }

    public function columns()
    {
        return [
            [
                'data' => "DocNum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ReqQty",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "UoMCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Memo",
                'width' => 200,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "RequesterName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Department",
                'width' => 200,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_GIRNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $header = $this->header();
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value->DocNum,
                $value->DocDate,
                $value->ItemName,
                $value->ReqQty,
                $value->UoMCode,
                $value->Memo,
                $value->RequesterName,
                $value->Department,
                $value->SAP_GIRNo,
            ];
        }

        return [
            'header' => $header,
            'rows' => $rows,
        ];
    }
}
