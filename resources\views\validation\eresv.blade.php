<p class="{{ (Str::contains($reservation->creator->company, ['PT IMIP', 'PT BDM'])) ? 'text-left': 'text-center' }}" style="margin-top: 5px">
    {{-- <img src="{{ asset($logo['header']) }}" alt="" style="width: auto; height: 50px;"> --}}
    <img src="{{ asset($logo['header']) }}" alt="" style="width: auto; height:40px">
</p>

<p class="text-center">

{{-- <h3 class="text-center">{{ $reservation->paper_name }}</h3> --}}

<p class="text-center" style="margin-top: -12px;">
    <strong style="border-bottom: 1px solid #222;">Nomor: {{ $reservation->DocNum }}</strong>
</p>


<table width="100%">
   {{--  <tr>
        <td>Document Type</td>
        <td> : </td>
        <td>{{ $reservation->document_type }}</td>
    </tr> --}}
    <tr>
        <td> Tanggal Dokumen</td>
        <td colspan="2">: {{ $reservation->DocDate }}</td>
    </tr>

    <tr>
        <td>Requester</td>
        <td colspan="2">: {{ $reservation->RequesterName }}</td>
    </tr>

    <tr>
        <td>GIR</td>
        <td colspan="2">: {{ $reservation->SAP_GIRNo }}</td>
    </tr>

    <tr>
        <td>PR</td>
        <td colspan="2">: {{ $reservation->SAP_PRNo }}</td>
    </tr>

    <tr>
        <td>Notes</td>
        <td colspan="2">: {{ $reservation->Memo }}</td>
    </tr>

    @if($reservation->CategoryType == 'APD')
        <tr>
            <td>Expired Date</td>
            <td colspan="2">: {{ $reservation->ExpiredDate ?? \Carbon\Carbon::parse($reservation->updated_at)->addDays(7)->format('Y-m-d') }}</td>
            {{-- <td colspan="2">: {{ $reservation->ExpiredDate }}</td> --}}
        </tr>
    @endif


    @if ($reservation->issueReceipt)
    <tr>
        <td>Status</td>
        <td colspan="2">: Closed</td>
    </tr>
    @endif


    <tr>
        <td colspan="3">
            <table width="100%" border="1" class="mt-3 mb-3">
                <tr>
                    <th align="left">Request Qty</th>
                    <th align="left">Item Name</th>
                    <th align="left">Request Notes</th>
                    @if ($reservation->issueReceipt)
                        <th align="left">GI Safety No</th>
                        <th align="left">GI Safety Date</th>
                    @endif
                </tr>
                @foreach ($reservation->details as $detail)
                    <tr>
                        <td>{{ $detail->ReqQty }}</td>
                        <td>{{ $detail->ItemName }}</td>
                        <td>{{ $detail->ReqNotes }}</td>
                        @if ($detail->issueReceipt)
                            <td>{{ $detail->issueReceipt->header->doc_num }}</td>
                            <td>{{ $detail->issueReceipt->header->post_date }}</td>
                        @endif
                    </tr>
                @endforeach
            </table>
        </td>
    </tr>

    <tr>
        <td colspan="3">
        </td>
    </tr>
</table>

<table class="mt-4" width="100%">
    <tr>
        <td colspan="3">Approval Stage</td>
    </tr>
    <tr>
        <td><strong>Approver</strong></td>
        <td><strong>Approve Date</strong></td>
        <td><strong>Status</strong></td>
    </tr>

    @foreach ($approver as $element)
        <tr>
            <td>
                {{ $element->user_name }} <br>
            </td>
            <td>
                {{ date('Y-m-d H:i:s', strtotime($element->response_date)) }}
            </td>
            <td>
                {{ $element->status }}
            </td>
        </tr>
    @endforeach
</table>

<br>
<br>
<br>

<p class="{{ (Str::contains($reservation->creator->company, ['PT IMIP', 'PT BDM', 'PT BDT'])) ? 'text-left': 'text-center' }}" style="margin-bottom: -2px">
    <img src="{{ asset($logo['footer']) }}" alt="" style="width: 100%;">
</p>
{{-- <p class="text-center" style="margin-bottom: -2px">
    <small>
        <b>PT. Indonesia Morowali Industrial Park</b>
    </small>
</p>
<p class="text-center" style="margin-bottom: -2px">
    <small>
        Gedung IMIP, Jl. Batu Mulia No 8, Taman Meruya Hilir Blok N, Meruya Utara, Kembangan, Kota Jakarta Barat, DKI
        Jakarta
    </small>
</p>
<p class="text-center">
    <small>
        Phone : +62 21 2941 9688 │Fax : +62 21 2941 9696 │E-mail : <EMAIL> │www.imip.co.id
    </small>
</p> --}}

