<?php

namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Approval\TransactionApproval
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionApproval newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionApproval newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TransactionApproval query()
 * @mixin \Eloquent
 */
class TransactionApproval extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv2';
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $table = 'U_OWDD';
    protected $primaryKey = 'U_WddCode';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
