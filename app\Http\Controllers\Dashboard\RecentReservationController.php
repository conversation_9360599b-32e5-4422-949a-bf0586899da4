<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Resv\ResvHeader;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RecentReservationController extends Controller
{
    public function index(Request $request)
    {
        $data = ResvHeader::orderBy("DocNum", "desc")
            ->select(
                "resv_headers.*",
                DB::raw('
                    CASE
                        WHEN resv_headers."ApprovalStatus" = \'W\' THEN \'Waiting\'
                        WHEN resv_headers."ApprovalStatus" = \'P\' THEN \'Pending\'
                        WHEN resv_headers."ApprovalStatus" = \'N\' THEN \'Rejected\'
                        WHEN resv_headers."ApprovalStatus" = \'Y\' THEN \'Approved\'
                        WHEN resv_headers."ApprovalStatus" = \'-\' THEN \'-\'
                    END AS "AppStatus"
                '),
                DB::raw('
                    CASE
                        WHEN resv_headers."DocStatus" = \'D\' THEN \'Draft\'
                        WHEN resv_headers."RequestType" = \'Restock\' AND resv_headers."DocStatus" = \'O\' THEN \'Open\'
                        WHEN resv_headers."DocStatus" = \'O\' THEN \'Open\'
                    END AS "DocumentStatus"
                ')
            )
            ->where("CreatedBy", "=", $request->user()->username)
            ->paginate($request->itemsPerPage)
            ->items();

        return $this->success($data);
    }
}
