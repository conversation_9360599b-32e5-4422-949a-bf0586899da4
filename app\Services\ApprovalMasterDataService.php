<?php

namespace App\Services;

use App\Models\View\ViewEmployee;

class ApprovalMasterDataService
{
    /**
     * Retrieves an array containing document types and departments.
     *
     * @return array an associative array containing 'document_type' and 'department' keys
     */
    public function index()
    {
        return [
            'document_type' => [
                [
                    'name' => 'Invoice'
                ],
                [
                    'name' => 'Invoice Other'
                ],
                [
                    'name' => 'Internal Sign'
                ],
            ],
            'department' => collect($this->getDepartments()),
            'department' => collect($this->getDepartments())->merge($this->getReplaceDepartments('PT MMM')),
            // 'company' => collect($this->getCompany())->add(['name' => 'PT MMM']),
            'company' => collect($this->getCompany()),
        ];
    }

    /**
     * Returns all departments of PT IMIP employees.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getDepartments()
    {
        return ViewEmployee::selectRaw('Department as name, Company')
            ->distinct()
            // ->where('Company', 'PT IMIP')
            ->get();
    }

    protected function getReplaceDepartments($comp)
    {
        $data =  ViewEmployee::selectRaw('Department as name, Company')
            ->distinct()
            ->whereIn('Company', ['PT IMIP', $comp])
            ->get();
        $dep = [];
        foreach ($data as $key => $value) {
            $dep[] = [
                'name' => $value->name,
                'Company' => $comp
            ];
        }

        return $dep;
    }

    /**
     * Retrieves a distinct list of companies from the ViewEmployee table
     *
     * @return \Illuminate\Database\Eloquent\Collection The list of companies
     */
    protected function getCompany()
    {
        return ViewEmployee::selectRaw("Company as name")
            ->distinct()
            ->get();
    }
}
