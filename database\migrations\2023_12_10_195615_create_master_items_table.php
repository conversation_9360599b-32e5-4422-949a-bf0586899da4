<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMasterItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('master_items', function (Blueprint $table) {
            $table->id();
            $table->string('item_code', 50);
            $table->string('item_name');
            $table->string('uom');
            $table->string('item_category')->nullable();
            $table->string('item_subcategory')->nullable();
            $table->double('on_hand_qty', 12, 2)->nullable();
            $table->double('committed_qty', 12, 2)->nullable();
            $table->double('ordered_qty', 12, 2)->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->index('created_by');
            $table->index(['item_code', 'item_name', 'uom']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('master_items');
    }
}
