<?php

namespace App\Services;

use App\Models\Master\MasterSubCategory;
use App\Models\Task\Task;
use App\Models\Task\TaskBoard;
use App\Models\Task\TaskSection;
use App\Models\User;
use App\Notifications\Tasks\SendAssignNotification;
use App\Notifications\Tasks\SendNewTaskNotification;
use Illuminate\Support\Facades\DB;

class TaskService
{
    /**
     * @param $request
     * @return array[]
     */
    public function index($request)
    {
        $userDivision = DB::table('user_divisions')
            ->where('user_id', '=', $request->user()->id)
            ->pluck('division_name');
        $listType = $request->listType;
        $query = Task::with(['requester', 'section', 'subCategory', 'priority', 'tags'])
            ->select('*', DB::raw('(SELECT
                          X2.name,
                          X2.username as nik,
                          X1.task_id
                    FROM task_tag_people as X1
                    left join users as X2 on CONVERT(nvarchar, X1.user_id) = CONVERT(nvarchar, x2.username)
                    WHERE X1.task_id = tasks.id
                    FOR JSON PATH
                ) as assignees'));

        if ($listType == 'myTicket') {
            $tasks = $query->where('user_id', $request->user()->id)->get();
        } elseif ($listType == 'myTeamTicket') {
            $tasks = $query->whereIn('department', $userDivision)->get();
        } elseif ($listType == 'myAssignTicket') {
            $tasks = $query->leftJoin('task_tag_people as C', 'C.task_id', 'tasks.id')
                ->where('C.user_id', '=', $request->user()->id)
                ->get();
        }
        return [
            'rows' => $tasks
        ];
    }

    /**
     * @param $form
     * @param $request
     * @param $type
     * @return array
     */
    public function formData($form, $request, $type): array
    {
        $board = $this->checkBoard($request, $form);
        $sub_category = MasterSubCategory::where('title', '=', $form['sub_category']['title'])->first();
        // $priority_id = null;
        // if (array_key_exists('priority_name', $form)) {
        //     if ($form['priority_name']) {
        //         $priority = MasterPriority::where('title', '=', $form['priority_name'])->first();
        //         $priority_id = $priority->id;
        //     }
        // }
        if ($type == 'store') {
            $section = TaskSection::where('department', '=', $form['department'])
                ->where('order_line', '=', 1)
                ->first();
        } else {
            $section = TaskSection::where('department', '=', $form['department'])
                ->where('id', '=', $form['section_id'])
                ->first();
        }


        $data = [
            'title' => $form['title'],
            'description' => $form['description'],
            'department' => $form['department'],
            'start_date' => (array_key_exists('start_date', $form)) ? $form['start_date'] : null,
            'approve' => $form['approve'],
            'priority_id' => $form['priority_id'],
            'due_date' => array_key_exists('due_date', $form) ? $form['due_date'] : null,
            'task_temp_id' => (array_key_exists('task_temp_id', $form)) ? $form['task_temp_id'] : 0,
            'sub_category_id' => $sub_category->id,
            'order_line' => 1,
            'board_id' => 0,
            // 'section_id' => 0,
            // 'board_id' => $board->id,
            'section_id' => (!empty($form['section_id'])) ? $form['section_id'] : 0,
        ];

        $merge = [];

        if ($type == 'store') {
            $merge['user_id'] = $request->user()->id;
            $merge['task_number'] = $this->generateDocNum(date('Y-m-d H:i:s'));

            $data = array_merge($data, $merge);
        }
        return $data;
    }

    /**
     * @param $request
     * @param $form
     * @return mixed
     */
    public function checkBoard($request, $form)
    {
        $department = $form['department'];
        $board = TaskBoard::where('department', '=', $department)->first();
        if (!$board) {
            $board = TaskBoard::create([
                'department' => $form['department'],
                // 'color' => $form['color']['hex'],
                'created_by' => $request->user()->id,
            ]);
        }
        return $board;
    }

    /**
     * @param $sysDate
     * @return string
     */
    public function generateDocNum($sysDate): string
    {
        $data_date = strtotime($sysDate);
        $year_val = date('y', $data_date);
        $full_year = date('Y', $data_date);
        $month = date('m', $data_date);
        $day_val = date('j', $data_date);
        $end_date = date('t', $data_date);
        $first_date = "$full_year-$month-01";
        $second_date = "$full_year-$month-$end_date";

        if ($day_val == 1) {
            $docnum = (int)$year_val . $month . sprintf("%04s", "1");
            if (!$docnum) {
                return (int)$year_val . $month . sprintf("%04s", "1");
            } else {
                return $this->docNum($first_date, $second_date, $year_val, $month);
            }
        } else {
            return $this->docNum($first_date, $second_date, $year_val, $month);
        }
    }

    /**
     * @param $first_date
     * @param $second_date
     * @param $year_val
     * @param $month
     * @return string
     */
    protected function docNum($first_date, $second_date, $year_val, $month)
    {
        $doc_num = Task::selectRaw('ISNULL("task_number", 0) as task_number')
            ->whereBetween(DB::raw('(convert(varchar, created_at, 23))'), [$first_date, $second_date])
            ->orderBy("task_number", "DESC")
            ->first();

        $number = (empty($doc_num)) ? '00000000' : $doc_num->task_number;
        $clear_doc_num = (int)substr($number, 4, 7);
        $number = $clear_doc_num + 1;
        return (int)$year_val . $month . sprintf("%04s", $number);
    }

    /**
     * @param $request
     * @param $id
     * @param $user
     * @return void
     */
    public function sendAssignNotification($request, $id, $user)
    {
        // $user = User::where('name', $assign)->first();
        $assigner = $request->user()->name;
        $task = Task::find($id);
        // $notifications = $user->unreadNotifications();
        $user->notify(new SendAssignNotification($user, $task, $assigner));
        // foreach ($notifications as $value) {
        //     if ($value->data['source_id'] != $task->board_id) {
        //         $user->notify(new SendAssignNotification($user, $task, $assigner));
        //     }
        // }
    }

    /**
     * @param $request
     * @param $task
     * @return void
     */
    public function sendNewTaskNotification($request, $task)
    {
        $users = User::where('department', $task->department)->get();
        foreach ($users as $user) {
            $user->notify(new SendNewTaskNotification($task, $user, $request->user()->name));
        }
    }
}
