[supervisord]
nodaemon=true
user=root
pidfile=/var/run/supervisor.pid
logfile = /var/log/supervisord.log

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0

[program:php-fpm]
command=/usr/local/sbin/php-fpm
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0

# [program:laravel-schedule]
# process_name=%(program_name)s_%(process_num)02d
# command=php /opt/laravel/artisan schedule:work
# autostart=true
# autorestart=true
# stopasgroup=true
# killasgroup=true
# user=root
# numprocs=2
# redirect_stderr=true
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stopwaitsecs=3600

# [program:queue-worker-privy]
# process_name=%(program_name)s_%(process_num)02d
# command=php /opt/laravel/artisan queue:work --tries=2 --memory=512 --daemon --timeout=7000 --queue=processDocumentPrivy
# autostart=true
# autorestart=true
# stopasgroup=true
# killasgroup=true
# user=root
# numprocs=2
# redirect_stderr=true
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stopwaitsecs=3600

# [program:queue-worker-sign-document]
# process_name=%(program_name)s_%(process_num)02d
# command=php /opt/laravel/artisan queue:work --tries=2 --memory=512 --timeout=7000  --daemon --queue=digitalSign,processDocument,internalSign,document
# autostart=true
# autorestart=true
# stopasgroup=true
# killasgroup=true
# user=root
# numprocs=2
# redirect_stderr=true
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stopwaitsecs=3600


# [program:queue-worker-default]
# process_name=%(program_name)s_%(process_num)02d
# command=php /opt/laravel/artisan queue:work --tries=2 --memory=512 --timeout=7000  --daemon --queue=default
# autostart=true
# autorestart=true
# stopasgroup=true
# killasgroup=true
# user=root
# numprocs=2
# redirect_stderr=true
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stopwaitsecs=3600


# [program:queue-worker-document-purchase]
# process_name=%(program_name)s_%(process_num)02d
# command=php /opt/laravel/artisan queue:work --tries=2 --memory=512 --timeout=7000  --daemon --queue=documentPurchase
# autostart=true
# autorestart=true
# stopasgroup=true
# killasgroup=true
# user=root
# numprocs=2
# redirect_stderr=true
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stopwaitsecs=3600
