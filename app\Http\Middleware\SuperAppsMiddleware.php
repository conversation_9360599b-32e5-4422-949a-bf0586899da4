<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Traits\ApiResponse;

class SuperAppsMiddleware
{
    use ApiResponse;
    
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // return $this->errorApps('', 422, [$request]);
        if ($request->Token != config('app.superapps_token')) {
            return $this->errorApps('Token credentials not match!', '422', [], $request);
        }
        return $next($request);
    }
}
