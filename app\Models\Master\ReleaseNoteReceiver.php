<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\ReleaseNoteReceiver
 *
 * @property int $id
 * @property int $release_noted_id
 * @property int $user_id
 * @property string|null $read_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver query()
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver whereReleaseNotedId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ReleaseNoteReceiver whereUserId($value)
 * @mixin \Eloquent
 */
class ReleaseNoteReceiver extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $guarded = [];
}
