#!/bin/sh

BASEDIR=/opt/laravel
FIRST_INSTALL=false

if [ ! -f "$BASEDIR/.env" ]; then
    FIRST_INSTALL=true
fi

if [ "$FIRST_INSTALL" = true ]; then
    echo "This is a FRESH INSTALL."

    if [ "$PRODUCTION" = "1" ]; then
        ENV_FILE=".env.prod"
    else
        ENV_FILE=".env.dev"
    fi

    echo "Generating .env from a copy $ENV_FILE ..."
    cp $ENV_FILE .env
    echo "File .env generated."
fi

php artisan clear-compiled

if [ "$PRODUCTION" = "1" ]; then
    composer install --no-dev --no-interaction --no-scripts
else
    composer install --no-interaction --no-scripts
fi


php artisan optimize:clear
rm -rf public/storage
php artisan storage:link

if [ "$PRODUCTION" = "1" ]; then
    echo "Running in production mode"
else
    echo "Running in development mode"
fi


LOG_DIR="/opt/laravel/storage"
LOG_FILES="$LOG_DIR/*"

# Set the correct ownership and permissions for each log file
for LOG_FILE in $LOG_FILES
do
    if [ -f "$LOG_FILE" ]; then
        echo "Setting ownership and permissions for $LOG_FILE"
        chown -R www-data:www-data "$LOG_FILE"
        chmod 775 "$LOG_FILE"
    fi
done

LOG_DIR="/opt/laravel/public"
LOG_FILES="$LOG_DIR/*"

# Set the correct ownership and permissions for each log file
for LOG_FILE in $LOG_FILES
do
    if [ -f "$LOG_FILE" ]; then
        echo "Setting ownership and permissions for $LOG_FILE"
        chown -R www-data:www-data "$LOG_FILE"
        chmod 775 "$LOG_FILE"
    fi
done
echo "Log file setup complete."

chown -R www-data:www-data /opt/laravel/public
chown -R www-data:www-data /opt/laravel/storage

# Substitute environment variables in the NGINX configuration
envsubst '${NGINX_PORT}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf

service cron start
exec /usr/bin/supervisord -c /etc/supervisord.conf
