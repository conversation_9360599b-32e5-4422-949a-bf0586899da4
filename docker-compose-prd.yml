version: "3"
services:
  # PHP
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: backendcore-laravel-prd
    restart: always
    tty: true
    ports:
      - 8101:8101
    environment:
      PHP_OPCACHE_ENABLE: 1
      PRODUCTION: "1"
      NGINX_PORT: 8101
    volumes:
      - ./:/opt/laravel
      - vendor-data:/opt/laravel/vendor
      - storage-data:/opt/laravel/storage
    networks:
      - imip-app
    extra_hosts:
      - "auth-dev.corp.imip.co.id:${DOMAIN_HOST:-127.0.0.1}"
      - "auth.imip.co.id:${DOMAIN_HOST:-**********}"
      - "websocket.imip.co.id:${DOMAIN_HOST:-**********}"

# Networks
networks:
  imip-app:
    driver: bridge

# Volumes
volumes:
  vendor-data:
  storage-data:
