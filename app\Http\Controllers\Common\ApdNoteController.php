<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Models\Common\ApdNote;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApdNoteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = ApdNote::select(
            "id",
            "notes",
        )
            ->orderBy('notes', 'desc')
            ->get();

        if (count($data) < 1) {
            $data = [
                [
                    "id" => null,
                    "notes" => null
                ]
            ];
        }

        return $this->success([
            'rows' => $data,
            'columns' => [
                [
                    'data' => 'id',
                    'width' => 50,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'notes',
                    'width' => 100,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'id',
                    'width' => 3,
                    'wordWrap' => false,
                    'renderer' => 'DeleteRenderer'
                ],
            ],
            'header' => ['Id', 'NOTES', ''],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        try {
            foreach ($details as $detail) {
                $data = [
                    'notes' => $detail['notes'],
                ];

                $brand = ApdNote::where('id', '=', $detail['id'])->first();

                if (!$brand) {
                    $data = array_merge($data, [
                        'created_by' => $request->user()->id,
                    ]);
                    DB::connection('sqlsrv')
                        ->table('apd_notes')
                        ->insert($data);
                } else {
                    DB::connection('sqlsrv')
                        ->table('apd_notes')
                        ->where('id', '=', $detail['id'])
                        ->update($data);
                }
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /* Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = ApdNote::where('category', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param Config $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Config $productBrand)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            ApdNote::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
