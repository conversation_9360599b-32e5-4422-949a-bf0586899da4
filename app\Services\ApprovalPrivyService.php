<?php

namespace App\Services;

use App\Models\Document\Document;
use App\Models\Settings\RegisterPrivy;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;


class ApprovalPrivyService
{
    use AppConfig;
    use ApiResponse;

    public function login()
    {
        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('PrivyAuthTokenUrl', 'PRIVY');
        $clientId = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $clientSecret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->post($url, [
                'client_id' => $clientId,
                'client_secret' => $clientSecret,
                'grant_type' => 'client_credentials'
            ])
            ->throw(function ($response, $e) {
                throw new \Exception('PRIVY: ' . json_decode($response->collect()), 1);
            });

        $this->storeConfig('PrivyAuthToken', $response->collect()['data']['access_token'], 'PRIVY');
    }

    /**
     * Summary of digitalSign
     * @param mixed $document
     * @param mixed $fileName
     * @param mixed $dataCheck
     * @param mixed $serialTable
     * @param mixed $batch
     * @throws \Exception
     * @return void
     */
    public function digitalSign($document, $fileName, $dataCheck, $batch)
    {
        $privyBackupPlan = $this->getConfigByName('UserPrivyBackupPlan', 'GENERAL');
        if ($privyBackupPlan == '1') {
            $pdf_file = public_path('docs/' . $fileName->file_name);
        } else {
            create_file_delete_job('/Attachment/docs/' . $fileName->file_name);
            $pdf_file = public_path('/Attachment/docs/' . $fileName->file_name);
        }

        // if ($document->meterai_coordinate == 'Y') {
        //     $pdf_file = public_path('/report/documents/' . $fileName->file_name);
        // } else {
        //     $pdf_file = public_path('/Attachment/docs/' . $fileName->file_name);
        // }

        if (!file_exists($pdf_file)) {
            throw new \Exception('PRIVY: ' . $pdf_file . ' not exist', 1);
        }

        $esign_user_id = $this->getConfigByName('UserIdEsign', 'ESIGN', $document->company);
        if (date('Y-m-d', strtotime($document->created_at)) < '2022-06-27') {
            $doc_id = Str::slug(strtoupper($document->document_number));
        } else {
            $doc_id = Str::slug(strtoupper($document->external_document_number));
        }
        $responseSign = $this->submitSign($pdf_file, $fileName, $document, $doc_id, $esign_user_id);
        // end submit digisign
        if ($responseSign->failed()) {
            $dataUpdate = [
                'batch_id' => 0,
                'status' => 'Processing 100%',
                'callback_message' => 'PRIVY: ',
                'callback_trace' => json_encode($responseSign->collect())
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            throw new \Exception('PRIVY: failed', 1);
        }
        if (array_key_exists('error', (array) $responseSign->collect())) {
            // throw new \Exception(json_encode($responseSign->collect()['JSONFile']), 1);
            $this->errorSubmitSign($responseSign, $doc_id, $dataCheck, $batch);
        } else {
            $this->successSubmitSign($responseSign, $doc_id, $dataCheck, $document, $esign_user_id);
        }
        // event(new BatchProcessEvent($document->created_by, $document->id));
    }
    /**
     * Summary of successSubmitSign
     * @param mixed $batch
     * @param mixed $doc_id
     * @param mixed $dataCheck
     * @param mixed $document
     * @param mixed $esign_user_id
     * @return void
     */
    public function successSubmitSign($responseSign, $doc_id, $dataCheck, $document, $esign_user_id)
    {
        $document = Document::find($document->id);
        $document->document_token = $responseSign['data']['document_token'];
        $document->privy_status = $responseSign['data']['status'];
        $document->status = 'approved - ' . $responseSign['data']['status'];
        $document->save();
        // $contents = base64_decode($responseSign['data']['signed_document']);
        // $file_name = $doc_id . '_DOWNLOAD.pdf';
        // $path_download = public_path('documents/' . $file_name);
        // file_put_contents($path_download, $contents);

        $dataUpdate = [
            'batch_id' => 0,
            'status' => 'Processing 100%',
            'callback_message' => 'Success document_id: ' . $doc_id,
            'callback_trace' => $responseSign
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        return;
    }
    /**
     * Summary of errorSubmitSign
     * @param mixed $responseSubmitDigisign
     * @param mixed $doc_id
     * @param mixed $dataCheck
     * @throws \Exception
     * @return void
     */
    public function errorSubmitSign($responseSign, $doc_id, $dataCheck, $batch)
    {
        if (Str::contains($responseSign['error']['errors'][0], 'Invalid signature, try using')) {
            $value = Str::after($responseSign['error']['errors'][0], 'Invalid signature, try using ');
            $this->storeConfig('PrivySignature', $value, 'PRIVY');

            $dataUpdate = [
                'batch_id' => 0,
                'status' => 'Processing 100%',
                'callback_message' => 'Error document_id: ' . $doc_id . ' '
                    . $responseSign->collect()['error']['errors'][0],
                'callback_trace' => $responseSign->collect()['error']['errors'][0]
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            throw new \Exception('PRIVY: ' . $responseSign->collect()['error']['errors'][0], 1);
        } else {
            $dataUpdate = [
                'batch_id' => 0,
                'status' => 'Processing 100%',
                'callback_message' => 'Success document_id: ' . $doc_id,
                'callback_trace' => $responseSign->collect()
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        }
    }
    /**
     * Summary of submitSign
     * @param mixed $pdf_file
     * @param mixed $fileName
     * @param mixed $serialTable
     * @param mixed $doc_id
     * @param mixed $esign_user_id
     * @return \Illuminate\Http\Client\Response
     */
    public function submitSign($pdf_file, $fileName, $document, $doc_id, $esign_user_id)
    {
        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('PrivyUploadDocument', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');

        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');

        $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');

        return Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $this->signature($pdf_file, $fileName, $document, $timestamp, 'POST')
            ])
            ->post($url, $this->signParams($pdf_file, $fileName, $document));
    }

    /**
     * Generates a signature for a PDF file.
     *
     * @param mixed $pdf_file The PDF file to generate the signature for.
     * @param mixed $fileName The name of the file.
     * @param mixed $document The document.
     * @param mixed $timestamp The timestamp.
     * @param mixed $httpVerb The HTTP verb.
     * @return string The generated signature.
     */
    protected function signature($pdf_file, $fileName, $document, $timestamp, $httpVerb): string
    {
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $body = $this->signParams($pdf_file, $fileName, $document);

        Arr::forget($body, 'document');

        Log::info('params submit privy ' . $document->document_number, [
            'body' => $body,
            'url' => $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('PrivyUploadDocument', 'PRIVY')
        ]);

        $strBody = json_encode($body);
        $strBody = str_replace(" ", "", $strBody);
        $method = $httpVerb;
        $body_md5 = base64_encode(md5($strBody, true));
        $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        $byte_key = utf8_encode($api_secret);
        $new_hmac = utf8_encode($hmac_signature);
        $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        $base64_message = base64_encode($hmac);
        $auth_string = $api_key . ":" . $base64_message;
        $signature = base64_encode($auth_string);
        return $signature;
    }

    /**
     * Sign the parameters.
     *
     * @param string $pdf_file The path to the PDF file.
     * @param object $fileName The name of the file.
     * @param object $document The document object.
     * @return array The signed parameters.
     */
    protected function signParams($pdf_file, $fileName, $document): array
    {
        $channelId = $this->getConfigByName('PrivyChannelId', 'PRIVY', $document->company);
        $docOwnerId = $this->getConfigByName('PrivyDocOwnerId', 'PRIVY', $document->company);
        $enterpriseToken = $this->getConfigByName('PrivyEnterpriseToken', 'PRIVY', $document->company);
        $enterpriseTokenImip = $this->getConfigByName('PrivyEnterpriseToken', 'PRIVY', 'PT IMIP');
        $docOwnerIdImip = $this->getConfigByName('ImipUserPrivyId', 'PRIVY', 'PT IMIP');
        $useImipToken = $this->getConfigByName('UseImipToken', 'PRIVY', 'PT IMIP');
        $secretKey = $this->getConfigByName('PrivySecretKey', 'PRIVY');
        $registerPrivy = RegisterPrivy::first();
        if (Str::contains($document->document_number, ['DOK/IMIP/24/04/01019', 'DOK/IMIP/24/04/01031'])) {
            $approverId = $this->getConfigByName('PrivyApproverId', 'PRIVY', 'PT BDM');
        } elseif (isset($document->signer)) {
            $approverId = $this->getConfigByName($document->signer, 'SIGNER', 'PT IMIP');
        } else {
            $approverId = $this->getConfigByName('PrivyApproverId', 'PRIVY', $document->company);
        }

        $base64 = base64_encode(file_get_contents($pdf_file));
        $privyFile = 'data:application/pdf;base64,' . $base64;
        $coordinate = $this->getRequestSign($document);

        $reference_number = 'ESIGN' . preg_replace("/[^a-zA-Z0-9]+/", "", $document->external_document_number);
        $document = Document::find($document->id);
        $document->reference_number = $reference_number;
        $document->save();

        $paramSign = [
            "reference_number" => $reference_number,
            "channel_id" => $channelId,
            "doc_process" => ($document->meterai_coordinate == 'Y') ? 3 : 0,
            // "doc_process" => 0,
            "custom_signature_placement" => true,
            // "info" => "",
            // "visibility" => true,
            "doc_owner" => [
                "privyId" => ($useImipToken == 'Y') ? $docOwnerIdImip : $docOwnerId,
                "enterpriseToken" => ($useImipToken == 'Y') ? $enterpriseTokenImip : $enterpriseToken,
                // "enterpriseToken" => $enterpriseTokenImip
            ],
            "document" => [
                "document_file" => $privyFile,
                "document_name" => Str::limit($fileName->file_name, 80),
                "sign_process" => "1",
                "barcode_position" => "0",
                "notify_user" => "1",
            ],
        ];

        if ($document->document_type == 'other') {
            $listParams = $this->getMultipleCoordinate($document, $approverId, $enterpriseToken);
            // $listParams = $this->getSingleCoordinate($document, $approverId, $enterpriseToken);
            $recipients = [
                "recipients" => $listParams['recipients'],
            ];

            $paramMeterai = [
                "e_meterai" => [
                    "doc_category" => $document->type,
                    "stamp_position" => $listParams['stamp_position']
                ]
            ];

            return ($document->meterai_coordinate == 'Y') ? array_merge($paramSign, $recipients, $paramMeterai) : array_merge($paramSign, $recipients);
        } else {
            $listParams = $this->getSingleCoordinate($document, $approverId, $enterpriseToken);
            $recipients = [
                "recipients" => $listParams['recipients'],
            ];

            $paramMeterai = [
                "e_meterai" => [
                    "doc_category" => $document->type,
                    "stamp_position" => $listParams['stamp_position']
                ]
            ];
            return ($document->meterai_coordinate == 'Y') ? array_merge($paramSign, $recipients, $paramMeterai) : array_merge($paramSign, $recipients);
        }
    }

    public function getSingleCoordinate($document, $approverId, $enterpriseToken)
    {
        $coordinate = $this->getRequestSign($document);
        return [
            "stamp_position" => [
                [
                    "pos_x" => $coordinate['vis_urx_meterai'],
                    "pos_y" => $coordinate['vis_ury_meterai'],
                    "page" => $document->materai_page,
                    "dimension" => 79.43023251
                ]
            ],
            "recipients" => [
                [
                    "user_type" => "0",
                    "autosign" => "1",
                    "id_user" => $approverId,
                    "signer_type" => "Signer",
                    // "enterpriseToken" => '',
                    "enterpriseToken" => $enterpriseToken,
                    "drag_n_drop" => false,
                    // "detail" => 0,
                    "posX" => strval($coordinate['urx']),
                    "posY" => strval($coordinate['ury']),
                    "signPage" => $document->sign_page
                ]
            ],
        ];
    }

    public function getMultipleCoordinate($document, $approverId, $enterpriseToken)
    {
        $coordinates = $document->coordinate;
        $recipients = [];
        $stamp_position = [];

        $signPosition = [];

        foreach ($coordinates as $item) {
            $coordinate = $this->getRequestSignMultiple($item, $document);

            if ($item->digisign_coordinate == 'Y') {
                $signPosition[] = [
                    // "detail" => 0,
                    "posX" => strval($coordinate['urx']),
                    "posY" => strval($coordinate['ury']),
                    "signPage" => $item->sign_page
                ];
            }

            if ($item->meterai_coordinate == "Y") {
                $stamp_position[] = [
                    "pos_x" => $coordinate['vis_urx_meterai'],
                    "pos_y" => $coordinate['vis_ury_meterai'],
                    "page" => $item->materai_page,
                    "dimension" => 79.43023251
                ];
            }
        }

        if ($item->digisign_coordinate == 'Y') {
            $recipients[] = [
                "user_type" => "0",
                "autosign" => "1",
                "id_user" => $approverId,
                "signer_type" => "Signer",
                // "enterpriseToken" => '',
                "enterpriseToken" => $enterpriseToken,
                "drag_n_drop" => false,
                // "detail" => 0,
                "sign_positions" => $signPosition
            ];
        }

        return [
            'stamp_position' => $stamp_position,
            'recipients' => $recipients,
        ];
    }

    public function getRequestSignMultiple($coordinate, $document)
    {
        $vis_digisign_urx = $coordinate->vis_digisign_urx;
        $vis_digisign_ury = $coordinate->vis_digisign_ury;

        $vis_urx_meterai = $coordinate->vis_urx;
        $vis_ury_meterai = $coordinate->vis_ury;

        return [
            'vis_urx_meterai' => $vis_urx_meterai,
            'vis_ury_meterai' => $vis_ury_meterai,
            'urx' => $vis_digisign_urx,
            'ury' => $vis_digisign_ury,
        ];
    }

    /**
     * Summary of getRequestSign
     * @param mixed $document
     * @param mixed $esign_user_name
     * @param mixed $esign_user_email
     * @param mixed $esign_key_user
     * @return array<array>
     */
    public function getRequestSign($document)
    {
        $vis_digisign_urx = (!empty($document->vis_digisign_urx)) ? $document->vis_digisign_urx : $document->coordinate()->first()->vis_digisign_urx;
        $vis_digisign_ury = (!empty($document->vis_digisign_ury)) ? $document->vis_digisign_ury : $document->coordinate()->first()->vis_digisign_ury;

        $vis_urx_meterai = $document->vis_urx;
        $vis_ury_meterai = $document->vis_ury;

        return [
            'vis_urx_meterai' => $vis_urx_meterai,
            'vis_ury_meterai' => $vis_ury_meterai,
            'urx' => $vis_digisign_urx,
            'ury' => $vis_digisign_ury
        ];
    }
}
