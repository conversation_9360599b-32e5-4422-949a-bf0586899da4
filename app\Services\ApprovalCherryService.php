<?php

namespace App\Services;

use App\Models\Common\Vehicle;
use App\Models\Master\ResvSapUsage;
use App\Models\Resv\ResvHeader;
use App\Models\User;
use App\Models\View\ViewApprovalStage;
use App\Models\View\ViewEmployee;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ApprovalCherryService
{
    use ApiResponse;
    use AppConfig;

    public function submitApproval($header, $details, $request)
    {
        $form = $header;

        $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');

        if ($checkUseNewApproval == '1') {
            $approvalEngineService = new ApprovalEngineService();
            $listCodeResponse = $approvalEngineService->getDocumentType();

            [$counts, $groups] = $this->getItemCounts($details, $header, $form);
            $reservationCode = $this->processReservationCode($listCodeResponse, $form, $header, $counts, $groups);

            // throw new \Exception($reservationCode);
            $documentContent = $this->generateDocumentContent($details, $header, $checkUseNewApproval);

            $response = $approvalEngineService->submitApproval(
                $header,
                $reservationCode,
                $documentContent,
            );

            ResvHeader::where('U_DocEntry', '=', $form->U_DocEntry)
                ->update(['ApprovalStatus' => 'W']);

            return $this->success([
                "U_DocEntry" => $form->U_DocEntry
            ], ($form->U_DocEntry != 'null' ? "Data updated!" : "Data inserted!"));
        } else {
            $form = $header;
            $this->validateExistingApproval($form);

            $authData = $this->getAuthenticationData($header);
            $listCodeResponse = $this->fetchDocumentTypes($authData);

            [$counts, $groups] = $this->getItemCounts($details, $header, $form);
            $reservationCode = $this->processReservationCode($listCodeResponse, $form, $header, $counts, $groups);

            $employeeData = $this->getEmployeeData($form, $authData['authUser']);
            $documentContent = $this->generateDocumentContent($details, $header, $checkUseNewApproval);

            $this->submitToCherry($form, $authData, $employeeData, $reservationCode, $documentContent);

            return $this->updateHeaderAndRespond($form);
        }
    }

    private function validateExistingApproval($form)
    {
        $checkApproval = ViewApprovalStage::where('DocumentReferenceID', $form->DocNum)->first();
        if ($checkApproval) {
            ResvHeader::where("DocNum", $form->DocNum)->update([
                "ApprovalStatus" => "W"
            ]);
            throw new \RuntimeException("Document already submitted to cherry");
        }
    }

    private function getAuthenticationData($header)
    {
        $authUser = User::where('id', Auth::user()->id)->first();
        $username = ($authUser->hasAnyRole(['Superuser'])) ? $header->CreatedBy : Auth::user()->username;
        $createdUser = User::where('username', $username)->first();
        $cherryToken = ($authUser->hasAnyRole(['Superuser'])) ? $createdUser->cherry_token : Auth::user()->cherry_token;

        return [
            'username' => $username,
            'cherryToken' => $cherryToken,
            // 'cherryToken' => 'Lp5kj1nsm0SiiWnw6KLqVA==',
            'authUser' => $authUser
        ];
    }

    private function fetchDocumentTypes($authData)
    {
        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'ExternalDocuments',
                'UserName' => $authData['username'],
                'Token' => $authData['cherryToken'],
                'ParameterData' => []
            ]);

        $this->validateResponse($response);
        return $response->collect();
    }

    private function validateResponse($response)
    {
        if ($response->failed()) {
            throw new \Exception($response->body(), 1);
        }

        $data = $response->collect();
        if ($data['MessageType'] == 'error') {
            throw new \Exception($data['Message'], 1);
        }
    }

    private function processReservationCode($listCodeResponse, $form, $header, $counts, $groups)
    {
        $requestFlags = $this->calculateRequestFlags($counts, $groups, $form, $header);

        $reservationCode = $this->determineReservationCode(
            $listCodeResponse['Data'],
            $form,
            $header,
            $counts,
            $requestFlags['is_request_it'],
            $requestFlags['is_request_safety'],
            $groups
        );

        if (empty($reservationCode)) {
            throw new \Exception('External Document Code cannot empty!', 1);
        }

        return $reservationCode;
    }

    private function getEmployeeData($form, $authUser)
    {
        $employee = ViewEmployee::where('Nik', '=', $form->Requester)->first();
        $employeeCode = $employee->EmployeeCode ?? Auth::user()->employee_code;
        $companyCode = $employee->CompanyCode;

        if (
            $form->CategoryType == 'APD' &&
            !Str::contains($form->Department, ['EXTERNAL - COMREL', 'EXTERNAL - MEDIA RELATIONSHIP'])
        ) {
            // Handle APD specific logic if needed
        }

        return [
            'employeeCode' => $employeeCode,
            'companyCode' => $companyCode
        ];
    }

    private function generateDocumentContent($details, $header, $checkUseNewApproval)
    {
        $movementType = ResvSapUsage::where('movement_type', $header->Usage)->first();
        return view('email.approval_resv', [
            'details' => $details,
            'movementType' => $movementType,
            'header' => $header,
            'newApproval' => $checkUseNewApproval
        ])->render();
    }

    private function submitToCherry($form, $authData, $employeeData, $reservationCode, $documentContent)
    {
        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->post(config('app.cherry_service_req'), [
                'CommandName' => 'Submit',
                'ModelCode' => 'GADocuments',
                'UserName' => $authData['username'],
                'Token' => $authData['cherryToken'],
                'ParameterData' => [],
                'ModelData' => [
                    'TypeCode' => $reservationCode,
                    'CompanyCode' => $employeeData['companyCode'],
                    'Date' => date('m/d/Y'),
                    'EmployeeCode' => $employeeData['employeeCode'],
                    'DocumentReferenceID' => $form->DocNum,
                    'CallBackAccessToken' => config('app.access_token_1'),
                    'DocumentContent' => $documentContent,
                    'Notes' => $form->Memo
                ]
            ]);

        $this->handleCherryResponse($response, $form);
        $this->logCherrySubmission($form, $response, $authData);
    }

    private function logCherrySubmission($form, $response, $authData)
    {
        Log::info('Params submit cherry, DocNum: ' . $form->DocNum, [
            'CommandName' => 'Submit',
            'ModelCode' => 'GADocuments',
            'UserName' => $authData['username'],
            'Token' => $authData['cherryToken'],
            'ParameterData' => [],
            'ModelData' => [
                'TypeCode' => $response['TypeCode'] ?? null,
                'CompanyCode' => $response['CompanyCode'] ?? null,
                'Date' => date('m/d/Y'),
                'EmployeeCode' => $authData['authUser']->employee_code,
                'DocumentReferenceID' => $form->DocNum,
                'CallBackAccessToken' => config('app.access_token_1'),
                'Notes' => $form->Memo
            ]
        ]);

        if ($response['MessageType'] == 'error') {
            Log::error("Error submit cherry: " . $response->collect()['Message'], [
                'docNum' => $form->DocNum,
                'message' => $response->collect()['Message'],
                'response' => $response->collect()
            ]);
        }
    }

    private function handleCherryResponse($response, $form)
    {
        if ($response->failed()) {
            throw new \Exception($response->body(), 1);
        }

        if ($response['MessageType'] == 'error') {
            $this->handleCherryError($response, $form);
        }
    }

    private function handleCherryError($response, $form)
    {
        $docNum = $form->DocNum;
        $error = "Document Number $docNum already exist!  Code: 2012";

        if (Str::contains($response->collect()['Message'], [$error])) {
            $checkApproval = ViewApprovalStage::where('DocumentReferenceID', $form->DocNum)->first();

            if ($checkApproval) {
                ResvHeader::where("DocNum", $form->DocNum)->update([
                    "ApprovalStatus" => "W"
                ]);
                throw new \RuntimeException("Document already submitted to cherry");
            }

            $this->handleDuplicateDocument($form);
        }

        throw new \Exception($response->collect()['Message'], 1);
    }

    private function handleDuplicateDocument($form)
    {
        $checkApproval = ViewApprovalStage::where('DocumentReferenceID', $form->DocNum)->first();
        if (!$checkApproval) {
            $service = new ReservationDataService();
            ResvHeader::where("U_DocEntry", $form->U_DocEntry)
                ->update([
                    "DocNum" => $service->generateDocNum($form->CreateDate)
                ]);
            throw new \Exception('Please submit approval again!', 1);
        }
    }

    private function updateHeaderAndRespond($form)
    {
        ResvHeader::where('U_DocEntry', '=', $form->U_DocEntry)
            ->update(['ApprovalStatus' => 'W']);

        return $this->success([
            "U_DocEntry" => $form->U_DocEntry
        ], ($form->U_DocEntry != 'null' ? "Data updated!" : "Data inserted!"));
    }

    private function getItemCounts($details, $header, $form)
    {
        $counts = [
            'it' => 0,
            'it_bdm' => 0,
            'sub_group_network' => 0,
            'sub_group_other' => 0,
            'ga' => 0,
            'safety' => 0,
            'seragam' => 0,
            'npb' => 0,
            'spb' => 0,
            'ga_in_safety' => 0,
            'bdm' => 0
        ];

        $groups = [
            'item_groups' => [],
            'item_groups_bdm' => [],
            'item_groups_ga' => [],
            'item_groups_safety' => [],
            'item_groups_ga_in_safety' => [],
            'item_group_bdm' => []
        ];

        $validateService = new ReservationValidateDataService();

        // Handle BDM MOROWALI specific logic
        if (Str::contains($header->WorkLocation, ['BDM MOROWALI']) && $form->WhsCode == 'BG02') {
            foreach ($details as $item) {
                $groups['item_groups_bdm'][] = $item['ItemGroup'];
                if ($item['ItemGroup'] == 'ZITS') {
                    $counts['it_bdm']++;
                }
            }
        }

        // Handle non-Restock requests
        if ($form->RequestType != 'Restock') {
            foreach ($details as $item) {
                $groups['item_groups'][] = $item['ItemGroup'];
                $this->countITItems($item, $counts);
                $this->countSubGroups($item, $counts);
            }
        }

        // Count remaining items
        foreach ($details as $item) {
            $this->countGAItems($item, $counts, $groups);
            $this->countSafetyItems($item, $header, $counts, $groups);
            $this->countBDMItems($item, $form, $counts, $groups, $validateService);
            $this->countNPBSPBItems($item, $counts);
        }
        return [$counts, $groups];
    }

    private function countBDMItems(array $item, $form, array &$counts, array &$groups, $validateService): void
    {
        $groups['item_group_bdm'][] = $item['ItemGroup'];

        if (array_key_exists('SubGroup', $item)) {
            if (
                str($item['SubGroup'])->contains($validateService->itemGroupSapBDM()) &&
                str($form->Company)->contains(["BDM", "BDW"])
            ) {
                $counts['bdm']++;
            }
        }
    }

    private function calculateRequestFlags(array $counts, array $groups, $form, $header): array
    {
        return [
            'is_request_it' => $this->isRequestIT($counts, $groups),
            'is_request_it_bdm' => $this->isRequestITBDM($counts, $groups),
            'is_request_ga' => $this->isRequestGA($counts, $groups),
            'is_request_item_bdm' => $this->isRequestBDM($counts, $groups),
            'is_request_ga_seragam' => $this->isRequestGASeragam($counts, $groups),
            'is_request_ga_in_safety' => $this->isRequestGAInSafety($counts, $groups),
            'is_request_safety' => $this->isRequestSafety($counts, $form),
            'is_carpool' => $this->isCarpool($form)
        ];
    }

    private function countITItems(array $item, array &$counts): void
    {
        if (
            $item['ItemGroup'] == 'ZITS' ||
            ($item['ItemGroup'] == 'ZAST' && Str::contains($item['SubGroup'], ['89112']))
        ) {
            $counts['it']++;
        }
    }

    private function countSubGroups(array $item, array &$counts): void
    {
        if (Str::contains($item['SubGroup'], ['89112']) || $item['SubGroup'] != '66103') {
            $counts['sub_group_other']++;
        }
        if ($item['SubGroup'] == '66103') {
            $counts['sub_group_network']++;
        }
    }

    private function countGAItems(array $item, array &$counts, array &$groups): void
    {
        $groups['item_groups_ga'][] = $item['ItemGroup'];
        if (
            $item['SubGroup'] == '66201' ||
            ($item['SubGroup'] == '66202' && $item["ItemCategory"] == 'RS')
        ) {
            $counts['ga']++;
        }
    }

    private function isRequestIT(array $counts, array $groups): bool
    {
        return $counts['it'] > 0 && $counts['it'] == count($groups['item_groups']);
    }

    private function isRequestITBDM(array $counts, array $groups): bool
    {
        return $counts['it_bdm'] > 0 && $counts['it_bdm'] == count($groups['item_groups_bdm']);
    }

    private function isRequestGA(array $counts, array $groups): bool
    {
        return $counts['ga'] > 0 && $counts['ga'] == count($groups['item_groups_ga']);
    }

    private function isRequestBDM(array $counts, array $groups): bool
    {
        return $counts['bdm'] > 0 && $counts['bdm'] == count($groups['item_group_bdm']);
    }

    private function isRequestGASeragam(array $counts, array $groups): bool
    {
        return $counts['seragam'] > 0 && $counts['seragam'] == count($groups['item_groups_ga']);
    }

    private function isRequestGAInSafety(array $counts, array $groups): bool
    {
        return $counts['ga_in_safety'] > 0 &&
            $counts['ga_in_safety'] == count($groups['item_groups_ga_in_safety']);
    }

    private function isRequestSafety(array $counts, $form): bool
    {
        return $counts['safety'] > 0 && $form->CategoryType == 'APD';
    }

    private function countSafetyItems(array $item, $header, array &$counts, array &$groups): void
    {
        $groups['item_groups_safety'][] = $item['U_AppResBy'] ?? null;
        $groups['item_groups_ga_in_safety'][] = $item['U_AppResBy'] ?? null;

        if (isset($item['U_AppResBy'])) {
            if ($item['U_AppResBy'] == 'GA') {
                $counts['ga_in_safety']++;
            }
            if ($item['U_AppResBy'] == 'HSE' || $header->CategoryType == 'APD') {
                $counts['safety']++;
            }
        }

        if ($item['SubGroup'] == '67104' && $item["ItemCategory"] == 'RS') {
            $counts['seragam']++;
        }
    }

    private function countNPBSPBItems(array $item, array &$counts): void
    {
        if ($item['NPB'] == 'Y') {
            $counts['npb']++;
        }
        if ($item['SPB'] == 'Y') {
            $counts['spb']++;
        }
    }

    private function isCarpool($form): bool
    {
        $vehicle = Vehicle::where('vehicle_no', '=', $form->VehicleNo)->first();
        return $vehicle && $vehicle->is_carpool == 'Yes';
    }

    private function determineReservationCode($listCodeData, $form, $header, $counts, $isRequestIt, $isRequestSafety, $groups)
    {
        $authUser = Auth::user();

        if ($form->DocumentType == 'Service') {
            return $this->getServiceReservationCode($listCodeData);
        }

        // Special handling for Admin E-RESERVATION JETTY BDM IMIP
        if ($authUser->hasAnyRole(['Admin E-RESERVATION JETTY BDM IMIP'])) {
            return $this->getJettyBDMReservationCode($listCodeData, $form, $counts);
        }

        $location = $header->WorkLocation;
        $itemType = $form->ItemType;
        $vehicle = Vehicle::where('vehicle_no', '=', $form->VehicleNo)->first();

        // Calculate additional request flags
        $requestFlags = $this->calculateRequestFlags($counts, $groups, $form, $header);

        foreach ($listCodeData as $datum) {
            $code = $this->matchReservationCode(
                $datum,
                $form,
                $header,
                $counts,
                $isRequestIt,
                $isRequestSafety,
                $requestFlags['is_request_ga_in_safety'],
                $requestFlags['is_request_ga'],
                $vehicle
            );


            if ($code) {
                return $code;
            }
        }

        return null;
    }

    private function getJettyBDMReservationCode($listCodeData, $form, $counts)
    {
        foreach ($listCodeData as $datum) {
            // IMIP_LIVE company logic
            if ($form->Company == 'IMIP_LIVE') {
                if ($counts['spb'] > 0 && $datum['Name'] == 'E-RESV SPB IMIP') {
                    return $datum['Code'];
                }
                if ($counts['npb'] > 0 && $datum['Name'] == 'E-RESV NPB IMIP') {
                    return $datum['Code'];
                }
            }

            // BDM_LIVE company logic
            elseif ($form->Company == 'BDM_LIVE') {
                if ($counts['spb'] > 0 && $datum['Name'] == 'E-RESV SPB BDM') {
                    return $datum['Code'];
                }
                if ($counts['npb'] > 0 && $datum['Name'] == 'E-RESV NPB BDM') {
                    return $datum['Code'];
                }
            }

            // BDW_LIVE company logic
            elseif ($form->Company == 'BDW_LIVE') {
                if ($counts['spb'] > 0 && $datum['Name'] == 'E-RESV SPB BDM') {
                    return $datum['Code'];
                }
                if ($counts['npb'] > 0 && $datum['Name'] == 'E-RESV NPB BDM') {
                    return $datum['Code'];
                }
            }
        }

        return null;
    }

    private function matchReservationCode(
        $datum,
        $form,
        $header,
        $counts,
        $isRequestIt,
        $isRequestSafety,
        $isRequestGaInSafety,
        $isRequestGa,
        $vehicle
    ) {
        // IT BDM requests
        if ($this->isItBdmRequest($datum, $form, $counts['spb'], $counts['npb'], $isRequestIt)) {
            return $datum['Code'];
        }

        // Restock requests
        if ($this->isRestockRequest($datum, $form, $header, $counts)) {
            return $datum['Code'];
        }

        // Fuel/Carpool requests
        if ($this->isFuelRequest($datum, $form, $header, $vehicle)) {
            return $datum['Code'];
        }

        // IT department requests
        if ($this->isItDepartmentRequest($datum, $form, $header, $counts, $isRequestIt)) {
            return $datum['Code'];
        }

        // GA and Safety requests
        if (
            $this->isGaOrSafetyRequest(
                $datum,
                $form,
                $header,
                $counts,
                $isRequestGaInSafety,
                $isRequestGa,
                $isRequestSafety
            )
        ) {
            return $datum['Code'];
        }

        // General requests
        if ($this->isGeneralRequest($datum, $form, $header, $counts, $vehicle)) {
            return $datum['Code'];
        }

        return null;
    }

    private function isItBdmRequest($datum, $form, $countSpb, $countNpb, $isRequestItBdm)
    {
        if (!$isRequestItBdm) {
            return false;
        }

        $validItemTypes = ['Non Ready Stock', 'Asset', 'Service'];
        $isValidItemType = str($form->ItemType)->contains($validItemTypes);

        return (
            $isRequestItBdm &&
            $isValidItemType &&
            (
                ($countSpb > 0 && $datum['Name'] == 'E-RESV SPB ASSET SERVICE IT BDM') ||
                ($countNpb > 0 && $datum['Name'] == 'E-RESV NPB ASSET SERVICE IT BDM')
            )
        );
    }

    private function isRestockRequest($datum, $form, $header, $counts)
    {
        if ($form->RequestType != 'Restock') {
            return false;
        }

        $morowaliLocations = ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'BDM MOROWALI', 'BDW MOROWALI', 'SMI MOROWALI'];
        $jakartaLocations = ['IMIP JAKARTA', 'BDM JAKARTA'];

        if (
            Str::contains($header->WorkLocation, $morowaliLocations) &&
            $form->ItemType == 'Ready Stock' &&
            $datum['Name'] == 'E-RESV SPB RESTOCK'
        ) {
            return true;
        }

        if (Str::contains($header->WorkLocation, $jakartaLocations)) {
            return (
                ($counts['spb'] > 0 && $datum['Name'] == 'E-RESV NPB JAKARTA') ||
                ($counts['npb'] > 0 && $datum['Name'] == 'E-RESV NPB JAKARTA')
            );
        }

        return false;
    }

    private function isFuelRequest($datum, $form, $header, $vehicle)
    {
        if ($form->CategoryType != 'Fuel' || !$vehicle || $vehicle->is_carpool != 'Yes') {
            return false;
        }

        $morowaliLocations = ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'BDM MOROWALI', 'BDW MOROWALI', 'SMI MOROWALI'];

        return (
            Str::contains($header->WorkLocation, $morowaliLocations) &&
            $form->ItemType == 'Ready Stock' &&
            $datum['Name'] == 'NPB PERTALITE'
        );
    }

    private function isItDepartmentRequest($datum, $form, $header, $counts, $isRequestIt)
    {
        if (!$isRequestIt) {
            return false;
        }

        $morowaliLocations = ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI'];
        if (!Str::contains($header->WorkLocation, $morowaliLocations)) {
            return false;
        }

        if ($counts['sub_group_network'] > 0) {
            return (
                ($counts['spb'] > 0 && $form->ItemType == 'Asset' && $datum['Name'] == 'E-RESV ASSET') ||
                ($counts['spb'] > 0 && $datum['Name'] == 'SPB IT REQUEST NETWORK') ||
                ($counts['npb'] > 0 && $datum['Name'] == 'NPB IT REQUEST NETWORK')
            );
        }

        if ($counts['sub_group_other'] > 0) {
            return (
                ($counts['spb'] > 0 && $form->ItemType == 'Asset' && $datum['Name'] == 'E-RESV ASSET') ||
                ($counts['spb'] > 0 && $datum['Name'] == 'SPB IT REQUEST HARDWARE') ||
                ($counts['npb'] > 0 && $datum['Name'] == 'NPB IT REQUEST HARDWARE')
            );
        }

        return false;
    }

    private function isGaOrSafetyRequest($datum, $form, $header, $counts, $isRequestGaInSafety, $isRequestGa, $isRequestSafety)
    {
        $morowaliLocations = ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI'];

        if ($isRequestGaInSafety && Str::contains($header->WorkLocation, $morowaliLocations)) {
            if ($datum['Name'] == 'E-RESV PENGAMBILAN APD') {
                return true;
            }

            return (
                ($datum['Name'] == 'SPB GA (Office Supplies)' && $counts['spb'] > 0 &&
                    (($form->ItemType == 'Asset' && $datum['Name'] == 'E-RESV ASSET') || true)) ||
                ($datum['Name'] == 'NPB GA (Office Supplies)' && $counts['npb'] > 0)
            );
        }

        if ($isRequestSafety && Str::contains($header->WorkLocation, $morowaliLocations)) {
            return (
                ($datum['Name'] == 'E-RESV SPB SAFETY' && $counts['spb'] > 0) ||
                ($datum['Name'] == 'E-RESV NPB SAFETY' && $counts['npb'] > 0)
            );
        }

        return false;
    }

    private function isGeneralRequest($datum, $form, $header, $counts, $vehicle)
    {
        $morowaliLocations = ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'BDM MOROWALI'];
        $jakartaLocations = ['IMIP JAKARTA', 'BDM JAKARTA'];

        // Asset requests
        if (
            $counts['spb'] > 0 &&
            Str::contains($header->WorkLocation, $morowaliLocations) &&
            $form->ItemType == 'Asset' &&
            !str($form->Department)->contains(['IT - ']) &&
            $datum['Name'] == 'E-RESV ASSET'
        ) {
            return true;
        }

        // General SPB/NPB requests
        if (Str::contains($header->WorkLocation, $morowaliLocations)) {
            if ($counts['spb'] > 0 && $datum['Name'] == 'E-RESERVATION SPB') {
                return true;
            }
            if (
                $counts['npb'] > 0 &&
                $form->CategoryType != 'Fuel' &&
                $datum['Name'] == 'E-RESERVATION NPB'
            ) {
                return true;
            }
        }

        // Fuel requests (non-carpool)
        if (
            $counts['npb'] > 0 &&
            $form->CategoryType == 'Fuel' &&
            $vehicle &&
            $vehicle->is_carpool == 'No' &&
            Str::contains($header->WorkLocation, $morowaliLocations) &&
            $datum['Name'] == 'E-RESERVATION NPB'
        ) {
            return true;
        }

        // Jakarta location requests
        if (
            Str::contains($header->WorkLocation, $jakartaLocations) &&
            !str($form->Requester)->contains(['88100102'])
        ) {
            return (
                ($counts['npb'] > 0 && $datum['Name'] == 'E-RESV NPB JAKARTA') ||
                ($counts['spb'] > 0 && $datum['Name'] == 'E-RESV SPB JAKARTA')
            );
        }

        return false;
    }


    private function getServiceReservationCode($listCodeData)
    {
        foreach ($listCodeData as $datum) {
            if ($datum['Name'] == 'E-RESV SPB JAKARTA' || $datum['Name'] == 'E-RESV NPB JAKARTA') {
                return $datum['Code'];
            }
        }
        return null;
    }
}
