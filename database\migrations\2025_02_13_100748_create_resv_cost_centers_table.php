<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resv_cost_centers', function (Blueprint $table) {
            $table->id();
            $table->string("division");
            $table->string("work_location");
            $table->string("cc_code");
            $table->string("cc_name");
            $table->unsignedBigInteger("created_by");
            $table->timestamps();
        });

        Schema::table("resv_headers", function (Blueprint $table) {
            $table->string("CostCenter")->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resv_cost_centers');
    }
};
