<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\ReservationHeader
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Resv\ReservationDetails> $details
 * @property-read int|null $details_count
 * @method static \Illuminate\Database\Eloquent\Builder|ReservationHeader newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReservationHeader newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReservationHeader query()
 * @mixin \Eloquent
 */
class ReservationHeader extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $connection = 'sqlsrv2';
    //    public $incrementing = false;
    protected $table = 'RESV_H';
    protected $primaryKey = 'U_DocEntry';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $casts = [
        "DocNum" => "string",
        "DocDate" => "string",
        "RequiredDate" => "string",
        "Requester" => "string",
        "Division" => "string",
        "Department" => "string",
        "Company" => "string",
        "Memo" => "string",
        "Canceled" => "string",
        "DocStatus" => "string",
        "ApprovalStatus" => "string",
        "ApprovalKey" => "string",
        "isConfirmed" => "string",
        "ConfirmDate" => "string",
        "ConfirmBy" => "string",
        "SAP_GIRNo" => "string",
        "SAP_TrfNo" => "string",
        "SAP_PRNo" => "string",
        "CreateDate" => "string",
        "CreateTime" => "string",
        "CreatedBy" => "string",
        "UpdateDate" => "string",
        "UpdateTime" => "string",
        "UpdatedBy" => "string",
        "U_DocEntry" => "string",
        "RequestType" => "string",
        "U_NIK" => "string",
        "WhsCode" => "string",
        "WhTo" => "string",
        "Token" => "string",
        "CreatedName" => "string",
        "RequesterName" => "string",
        "UrgentReason" => "string",
        "ItemType" => "string",
        "Is_Urgent" => "string",
        "CategoryType" => "string",
        "UsageFor" => "string",
        "VehicleNo" => "string",
        "Mileage" => "string",
        "Customer" => "string",
        "SAP_SONo" => "string",
        "U_ATTACH" => "string",
        "Replacement" => "string",
        "EmployeeType" => "string",
        "WorkLocation" => "string",
        "DocumentType" => "string",
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function details()
    {
        return $this->hasMany(ReservationDetails::class, 'U_DocEntry', 'U_DocEntry');
    }
}
