<?php

namespace App\Services;

use App\Models\Approval\UserApproveMapping;
use App\Models\Common\Attachment;
use App\Models\Document\Document;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocumentService
{
    use AppConfig;
    use ApiResponse;

    /**
     * @return void
     */
    public function peruriLogin()
    {
        $url = $this->getConfigByName('PeruriLogin');

        $response = Http::withoutVerifying()
            ->withOptions(["verify" => false])
            ->post($url, [
                'user' => $this->getConfigByName('PeruriUsername'),
                'password' => $this->getConfigByName('PeruriPassword'),
            ]);

        if ($response->failed()) {
            // Log::info('login peruri from Services', [
            //     'response' => $response->collect()
            // ]);
            throw new \Exception($response->body());
        }

        if ($response['message'] == 'success') {
            $this->storeConfig('Token', $response['token'], 'PERURI');
        }
    }

    /**
     * @param $document
     * @return array
     */
    public function generateSerialNumber($document): array
    {
        $url = $this->getConfigByName('GenerateSerialNumber');
        $token = $this->getConfigByName('Token');
        $response = Http::withoutVerifying()->withOptions(["verify" => false])
            ->withToken($token)
            ->post($url, [
                'isUpload' => false,
                'namadoc' => $document->type,
                'namafile' => $document->filename,
                'nilaidoc' => $document->value,
                'namejidentitas' => $document->identity_type,
                'noidentitas' => $document->identity_number,
                'namedipungut' => $document->identity_name,
                'snOnly' => false,
                'nodoc' => $document->document_number,
                'tgldoc' => $document->document_date,
            ]);

        if ($response['message'] == 'success') {
            $document = Document::find($document->id);
            $document->ref_token = $response['result']['sn'];
            $document->save();

            $fileName = Attachment::where('source_id', $document->temp_id)
                ->where('type', 'peruri')
                ->first();

            $dataSerial = $this->storeSerialNumberImage($response['result']['image'], $document, $fileName);

            $stamp = $this->stampDocument($dataSerial, $document, $fileName);

            if ($stamp['result']['status'] == '00') {
                $serial = $document->ref_token;

                $doc = '/sharefolder/signed/final_' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';
                return [
                    'error' => false,
                    'message' => $doc
                ];
            } else {
                return [
                    'error' => true,
                    'message' => $stamp['result']['message'],
                    'fileName' => $fileName->file_name
                ];
            }
        } else {
            return [
                'error' => true,
                'message' => $response['result']['message']
            ];
        }
    }

    /**
     * @param $image
     * @param $document
     * @param $fileName
     * @return string[]
     */
    public function storeSerialNumberImage($image, $document, $fileName): array
    {
        $serial = $document->ref_token;
        $image = str_replace('data:image/png;base64,', '', $image);
        $image = str_replace(' ', '+', $image);
        $imageName = $serial . '.' . 'png';
        // put stamp file to local server
        $qrImage = '/images/' . $imageName;
        custom_disk_put($qrImage, base64_decode($image));
        // File::put(public_path('/images/' . $imageName), base64_decode($image));


        // get pdf file
        $pdf_file_path = '/Attachment/docs/' . $fileName->file_name;
        // $pdf_file = public_path($pdf_file_path);

        // store stamp and unsigned file to shared server
        $stamp = '/STAMP/' . $imageName;
        custom_disk_put($stamp, custom_disk_get($qrImage));
        // Storage::disk('ftp')->put($stamp, public_path('/images/' . $imageName));
        // pdf file
        $doc = '/UNSIGNED/' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';
        custom_disk_put($doc, custom_disk_get($pdf_file_path));
        // Storage::disk('ftp')->put($doc, $pdf_file);

        return [
            'stamp' => $stamp,
            'doc' => $doc
        ];
    }

    /**
     * @param $dataSerial
     * @param $document
     * @param $fileName
     * @return \GuzzleHttp\Promise\PromiseInterface|\Illuminate\Http\Client\Response
     */
    public function stampDocument($dataSerial, $document, $fileName)
    {
        $url = $this->getConfigByName('Stamping');
        $token = $this->getConfigByName('Token');
        $serial = $document->ref_token;

        $doc = '/sharefolder/signed/final_' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';
        return Http::withoutVerifying()
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->post($url, [
                'certificatelevel' => 'NOT_CERTIFIED',
                'dest' => $doc,
                'docpass' => $document->password,
                'jwToken' => $token,
                'location' => $document->location,
                'profileName' => $document->profile_name,
                'reason' => $document->type,
                'refToken' => $serial,
                'spesimenPath' => $dataSerial['stamp'],
                'src' => $dataSerial['doc'],
                'visLLX' => 237.10994764397907,
                'visLLY' => 559.9251297391409,
                'visURX' => 337.10994764397907,
                'visURY' => 459.92512973914086,
                'visSignaturePage' => 1,
            ]);
    }

    /**
     * Retrieves a form with default values for a given document type.
     *
     * @param string $documentType The type of the document to retrieve the form for.
     * @throws \Exception A description of the exception that can be thrown
     * @return array The form object with default values
     */
    public function getForm($documentType)
    {
        $form = $this->form('documents');
        $form['company'] = 'PT IMIP';
        $form['value'] = 10000;
        $form['location'] = 'JAKARTA';
        $form['sign_payment'] = '2';
        $form['internal_document'] = ($documentType == 'internal') ? 'Y' : 'N';
        $form['identity_type'] = 'NPWP';
        $form['identity_name'] = 'PT INDONESIA MOROWALI INDUSTRIAL PARK';
        $form['identity_number'] = '033109851014000';
        $form['filename'] = '-';
        $form['document_sub_type_name'] = null;
        $form['customer_name'] = null;
        // $form['approval_id'] = UserApproveMapping::first()->approval->name;
        $form['profile_name'] = 'emeteraicertificateSigner';
        $form['temp_id'] = mt_rand(100000, 999999999999);
        $form['document_date'] = date('Y-m-d');

        return $form;
    }

    /**
     * Returns an array of form data for a document based on the provided request and type.
     *
     * @param mixed $request The request data to be used for the form data.
     * @param string $type The type of form data to be returned, either 'store' or 'update'.
     * @param int|null $id The ID of the document being updated, if applicable.
     * @return array The array of form data to be used for the document.
     */
    public function formData($request, $type, $id = null): array
    {
        if ($type == 'update') {
            $document = Document::find($request->id);
            $request->request->remove('attachment');
            $request->request->remove('user_create');
            $request->request->remove('default_currency_code');
            $request->request->remove('default_currency_symbol');
            $request->request->remove('document_sub_type_name');
            $request->request->remove('id');
            $request->request->remove('paper_no');
            $request->request->remove('approver');
            $request->request->remove('name');
            // $request->request->remove('document_type');
        }


        $data = $request->all();
        Arr::forget($data, 'coordinate');
        Arr::forget($data, 'sign_payments');
        $data['customer_name'] = is_object($data['customer_name']) ? $data['customer_name']->CardName : $data['customer_name'];
        // Arr::forget($data, 'customer_name');
        // $check = (object) $request;
        foreach ($this->getDocumentType() as $item) {
            if ($item['nama'] == $data['type']) {
                $data['type_no'] = $item['kode'];
            }
        }


        if ($type == 'store') {
            if (array_key_exists('approval_id', $data)) {
                $approval = UserApproveMapping::whereHas('approval', function ($query) use ($data) {
                    return $query->where('name', '=', $data['approval_id']);
                })->first();
            } else {
                $approval = null;
            }

            $data['approval_id'] = (!empty($approval)) ? $approval->id : null;
            $data['document_number'] = $this->generateDocNum(date('Y-m-d H:i:s'), $data['type']);
            $data['filename'] = '-';
            $form['temp_id'] = mt_rand(100000, 999999999999);
            $data['status'] = 'draft';
            $data['created_by'] = auth()->user()->id;
            $data['internal_document'] = (array_key_exists('internal_document', $data)) ? $data['internal_document'] : 'N';
            $data['created_at'] = Carbon::now();
            $data['str_url'] = Str::random(120);
            $data['profile_name'] = (isset($data['profile_name'])) ? $data['profile_name'] : 'emeteraicertificateSigner';
            $data['sign_payment'] = (isset($data['sign_payment'])) ? $data['sign_payment'] : '2';
            $data['identity_type'] = (isset($data['identity_type'])) ? $data['identity_type'] : 'NPWP';
            $data['identity_number'] = (isset($data['identity_number'])) ? $data['identity_number'] : '033109851014000';
            $data['identity_name'] = (isset($data['identity_name'])) ? $data['identity_name'] : 'PT INDONESIA MOROWALI INDUSTRIAL PARK';
            $data['value'] = (isset($data['value'])) ? $data['value'] : 10000;
            $data['location'] = (isset($data['location'])) ? $data['location'] : 'JAKARTA';
        } else {
            $data['updated_by'] = auth()->user()->id;
            $data['created_at'] = Carbon::parse($request->created_at);
            $data['updated_at'] = Carbon::now();
        }

        return $data;
    }

    /**
     * @return mixed
     */
    public function getDocumentType()
    {
        return [
            [
                "id" => "4e9de1e8-879e-4bd3-a681-704afd6fd84b",
                "kode" => "3",
                "name" => "Surat Keterangan",
                "nama" => "Surat Keterangan"
            ],
            [
                "id" => "8ee8bdec-5ddf-4797-a74c-fd45977513c9",
                "kode" => "4a",
                "name" => "Dokumen penerimaan uang (lebih dari 5 juta)",
                "nama" => "Dokumen penerimaan uang (lebih dari 5 juta)"
            ],
            [
                "id" => "6a004c92-739d-4325-ab88-873d11d592a0",
                "kode" => "3",
                "name" => "Surat Lainnya",
                "nama" => "Surat Lainnya"
            ],
            [
                "id" => "5ab5ff9d-3fc0-452a-b63a-f78c021de06f",
                "kode" => "3",
                "name" => "Surat Pernyataan",
                "nama" => "Surat Pernyataan"
            ],
            [
                "id" => "3e05283e-e98b-41b7-a3cc-8ae9d3cda2b5",
                "kode" => "2",
                "name" => "Dokumen Transaksi",
                "nama" => "Dokumen Transaksi"
            ],
            [
                "id" => "14bb5e08-3745-40de-b4ce-122a75ae09aa",
                "kode" => "4b",
                "name" => "Dokumen pelunasan utang (lebih dari 5 juta)",
                "nama" => "Dokumen pelunasan utang (lebih dari 5 juta)"
            ],
            [
                "id" => "8cf53120-3c28-496f-920f-aed7e587856f",
                "kode" => "2",
                "name" => "Surat Berharga",
                "nama" => "Surat Berharga"
            ],
            [
                "id" => "d25e6e2f-ef10-44c2-941f-7e77beff2818",
                "kode" => "2",
                "name" => "Akta Pejabat",
                "nama" => "Akta Pejabat"
            ],
            [
                "id" => "8cf53120-3c28-496f-920f-aed7e587856e",
                "kode" => "2",
                "name" => "Dokumen lain-lain",
                "nama" => "Dokumen lain-lain"
            ],
            [
                "id" => "8ee8bdec-5ddf-4797-a74c-fd45977513c8",
                "kode" => "2",
                "name" => "Akta Notaris",
                "nama" => "Akta Notaris"
            ],
            [
                "id" => "6a004c92-739d-4325-ab88-873d11d592a1",
                "kode" => "2",
                "name" => "Dokumen Lelang",
                "nama" => "Dokumen Lelang"
            ],
            [
                "id" => "3e05283e-e98b-41b7-a3cc-8ae9d3cda2b4",
                "kode" => "3",
                "name" => "Surat Perjanjian",
                "nama" => "Surat Perjanjian"
            ],
            [
                "id" => "3e05283e-e98b-41b7-a3cc-8ae9d3cda2b7",
                "kode" => "4a",
                "name" => "Dokumen pernyataan jumlah uang lebih dari 5jt",
                "nama" => "Dokumen pernyataan jumlah uang lebih dari 5jt"
            ]
        ];

        $url = $this->getConfigByName('DocumentType', 'ESIGN');
        $token = $this->getConfigByName('Token');
        $response = Http::withoutVerifying()->withToken($token)->get($url);

        if ($response->failed()) {
            // $url = $this->getConfigByName('DocumentType');
            // $token = $this->getConfigByName('Token');
            // $response = Http::withoutVerifying()->withToken($token)->get($url);

            // if ($response->failed()) {
            //     return [
            //         [
            //             "id" => "4e9de1e8-879e-4bd3-a681-704afd6fd84b",
            //             "kode" => "3",
            //             "nama" => "Surat Keterangan"
            //         ],
            //         [
            //             "id" => "8ee8bdec-5ddf-4797-a74c-fd45977513c9",
            //             "kode" => "4a",
            //             "nama" => "Dokumen penerimaan uang (lebih dari 5 juta)"
            //         ],
            //         [
            //             "id" => "6a004c92-739d-4325-ab88-873d11d592a0",
            //             "kode" => "3",
            //             "nama" => "Surat Lainnya"
            //         ],
            //         [
            //             "id" => "5ab5ff9d-3fc0-452a-b63a-f78c021de06f",
            //             "kode" => "3",
            //             "nama" => "Surat Pernyataan"
            //         ],
            //         [
            //             "id" => "3e05283e-e98b-41b7-a3cc-8ae9d3cda2b5",
            //             "kode" => "2",
            //             "nama" => "Dokumen Transaksi"
            //         ],
            //         [
            //             "id" => "14bb5e08-3745-40de-b4ce-122a75ae09aa",
            //             "kode" => "4b",
            //             "nama" => "Dokumen pelunasan utang (lebih dari 5 juta)"
            //         ],
            //         [
            //             "id" => "8cf53120-3c28-496f-920f-aed7e587856f",
            //             "kode" => "2",
            //             "nama" => "Surat Berharga"
            //         ],
            //         [
            //             "id" => "d25e6e2f-ef10-44c2-941f-7e77beff2818",
            //             "kode" => "2",
            //             "nama" => "Akta Pejabat"
            //         ],
            //         [
            //             "id" => "8cf53120-3c28-496f-920f-aed7e587856e",
            //             "kode" => "2",
            //             "nama" => "Dokumen lain-lain"
            //         ],
            //         [
            //             "id" => "8ee8bdec-5ddf-4797-a74c-fd45977513c8",
            //             "kode" => "2",
            //             "nama" => "Akta Notaris"
            //         ],
            //         [
            //             "id" => "6a004c92-739d-4325-ab88-873d11d592a1",
            //             "kode" => "2",
            //             "nama" => "Dokumen Lelang"
            //         ],
            //         [
            //             "id" => "3e05283e-e98b-41b7-a3cc-8ae9d3cda2b4",
            //             "kode" => "3",
            //             "nama" => "Surat Perjanjian"
            //         ],
            //         [
            //             "id" => "3e05283e-e98b-41b7-a3cc-8ae9d3cda2b7",
            //             "kode" => "4a",
            //             "nama" => "Dokumen pernyataan jumlah uang lebih dari 5jt"
            //         ]
            //     ];
        }

        // if ($response->collect()) {
        //     return $response->collect()['result'];
        // }
        // return $response->collect();
    }

    /**
     * @param $sysDate
     * @param $alias
     *
     * @return string
     */
    public function generateDocNum($sysDate, $alias)
    {
        $type = $alias;
        $alias = $this->abbreviate($alias);

        $data_date = strtotime($sysDate);
        $year_val = date('y', $data_date);
        $month = date('m', $data_date);

        $day_val = date('j', $data_date);

        if ((int) $day_val === 1) {
            $document = 'DOK/IMIP/' . $year_val . '/' . $month . '/' . sprintf('%05s', '1');
            $check_document = Document::where('document_number', '=', $document)
                //->where('type', $type)
                ->first();
            if (!$check_document) {
                return 'DOK/IMIP/' . $year_val . '/' . $month . '/' . sprintf('%05s', '1');
            } else {
                //SQ-220100001
                return $this->itemCode($data_date, $alias, $year_val, $month, $type);
            }
        }
        return $this->itemCode($data_date, $alias, $year_val, $month, $type);
    }

    /**
     * @param $string
     * @return string
     */
    public function abbreviate($string): string
    {
        return implode(
            array_map(
                function ($w) {
                    return substr($w, 0, 1);
                },
                explode(" ", $string)
            )
        );
    }

    /**
     * @param $data_date
     * @param $alias
     * @param $year_val
     * @param $month
     * @param $type
     * @return string
     */
    protected function itemCode($data_date, $alias, $year_val, $month, $type)
    {
        $full_year = date('Y', $data_date);
        $end_date = date('t', $data_date);

        $first_date = "$full_year-$month-01";
        $second_date = "$full_year-$month-$end_date";

        $doc_num = Document::selectRaw('document_number as code')
            ->whereBetween(DB::raw('(convert(varchar, created_at, 23))'), [$first_date, $second_date])
            //->where('type', $type)
            ->orderBy('document_number', 'DESC')
            ->first();

        // return $doc_num;

        $number = empty($doc_num) ? '0000000000' : $doc_num->code;
        $clear_doc_num = (int) substr($number, 15, 22);
        // $clear_doc_num = substr($number, 0, 1);
        $number = $clear_doc_num + 1;

        // throw new \Exception($clear_doc_num, 1);

        // $number = (int) $doc_num + 1 ;

        return 'DOK/IMIP/' . $year_val . '/' . $month . '/' . sprintf('%05s', $number);
    }

    public function pointToPixel($points, $decimals = 3, $calibrate = true): float
    {
        if ($points) {
            if ($calibrate) {
                return round($points * (96 / 72), $decimals);
            }
            return $points;
        }
        return 0;
    }
}
