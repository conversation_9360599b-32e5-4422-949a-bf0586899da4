SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO -- =============================================
    -- Author:		<Author,,Name>
    -- Create date: <Create Date,,>
    -- Description:	<Description,,>
    -- =============================================
    ALTER PROCEDURE SP_Report_Reservation_Summary (
        @FromDate DATE,
        @EndDate DATE,
        @Whs NVARCHAR(200)
    ) AS -- BEGIN
    BEGIN -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
SET NOCOUNT ON;
select A.WhsCode,
    SUM(B.ReqQty) as ReqQty,
    A.Department
from resv_headers as A
    left join resv_details as B on A.U_DocEntry = B.U_DocEntry
where A.WhsCode in (
        SELECT Split.a.value('.', 'NVARCHAR(MAX)') DATA
        FROM (
                SELECT CAST(
                        '<X>' + REPLACE(@Whs, ',', '</X><X>') + '</X>' AS XML
                    ) AS String
            ) AS A
            CROSS APPLY String.nodes('/X') AS Split(a)
    )
    and A.ApprovalStatus = 'Y'
    and A.DocDate between @FromDate and @EndDate
GROUP by A.WhsCode,
    A.Department
order by Department desc
END
GO