<?php

namespace App\Services;

use App\Traits\AppConfig;
use App\Services\ApprovalPrivyService;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CheckStatusPrivyService
{
	use AppConfig;

	public function index($id)
	{
        $service = new ApprovalPrivyService();
        $service->login();

		$row = DB::connection('sqlsrv4')
            ->table('batch_approvals')
            ->where('id', $id)
            ->first();


		$tenant = DB::connection('sqlsrv4')
            ->table('T_MDOC')
            ->leftJoin("M_Tenant", "M_Tenant.DocEntry", "T_MDOC.Tenant_key")
            ->select("M_Tenant.ChannelId")
            ->where('T_MDOC.DocEntry', $row->document_id)
            ->first();

        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('PrivyCheckDocumentRegister', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');
        $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $this->signature($row, $timestamp, 'POST', $tenant),
                'Request-ID' => strtotime($timestamp),
            ])
            ->post($url, $this->params($row, $tenant))
            ->throw(function ($response, $e) {
                throw new \Exception('E-DIGITAL SIGN: ' . json_decode($response->collect()), 1);
            });

        $status = $response->collect()['data']['status'];

        if ($response->collect()['data']['status'] == 'completed') {
            return base64_decode(str_replace('data:application/pdf;base64,', '', $response['data']['signed_document']));
        }
        return '';
	}

	public function params($document, $tenant)
    {
        $channelId = $tenant->ChannelId;

        $referenceNumber = $document->reference_number;
        $documentToken = $document->document_token;

        return [
            "reference_number" => $referenceNumber,
            "channel_id" => $channelId,
            "document_token" => $documentToken,
            "info" => ""
        ];
    }

    protected function signature($row, $timestamp, $httpVerb, $tenant)
    {
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $body = $this->params($row, $tenant);

        $strBody = json_encode($body);
        $strBody = str_replace(" ", "", $strBody);
        $method = $httpVerb;
        $body_md5 = base64_encode(md5($strBody, true));
        $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        $byte_key = utf8_encode($api_secret);
        $new_hmac = utf8_encode($hmac_signature);
        $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        $base64_message = base64_encode($hmac);
        $auth_string = $api_key . ":" . $base64_message;
        $signature = base64_encode($auth_string);
        return $signature;
    }
}