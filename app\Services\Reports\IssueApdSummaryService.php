<?php

namespace App\Services\Reports;

class IssueApdSummaryService
{
    public function header()
    {
        return ['Department', 'ItemCode', 'ItemName', 'UoMCode', 'ReqQty'];
    }

    public function columns()
    {
        return [
            [
                'data' => "Department",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "UoMCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "TotalReqQty",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value['Department'],
                $value['ItemCode'],
                $value['ItemName'],
                $value['UoMCode'],
                $value['TotalReqQty'],
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
