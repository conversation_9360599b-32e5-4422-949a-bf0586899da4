<?php

namespace App\Models\Document;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Document\DocumentCoordinate
 *
 * @property int $id
 * @property int $document_id
 * @property string|null $materai_page
 * @property string|null $meterai_coordinate
 * @property string|null $meterai_id
 * @property string|null $meterai_stamp_type
 * @property string|null $meterai_page_index
 * @property string|null $meterai_top
 * @property string|null $meterai_left
 * @property string|null $meterai_width
 * @property string|null $meterai_height
 * @property string|null $vis_llx
 * @property string|null $vis_lly
 * @property string|null $vis_urx
 * @property string|null $vis_ury
 * @property string|null $digisign_coordinate
 * @property string|null $digisign_id
 * @property string|null $digisign_stamp_type
 * @property string|null $digisign_page_index
 * @property string|null $digisign_top
 * @property string|null $digisign_left
 * @property string|null $digisign_width
 * @property string|null $digisign_height
 * @property string|null $sign_page
 * @property string|null $vis_digisign_llx
 * @property string|null $vis_digisign_lly
 * @property string|null $vis_digisign_urx
 * @property string|null $vis_digisign_ury
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $serial_number
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDigisignCoordinate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDigisignHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDigisignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDigisignLeft($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDigisignPageIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDigisignStampType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDigisignTop($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDigisignWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMateraiPage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMeteraiCoordinate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMeteraiHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMeteraiId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMeteraiLeft($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMeteraiPageIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMeteraiStampType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMeteraiTop($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereMeteraiWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereSerialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereSignPage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereVisDigisignLlx($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereVisDigisignLly($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereVisDigisignUrx($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereVisDigisignUry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereVisLlx($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereVisLly($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereVisUrx($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentCoordinate whereVisUry($value)
 * @mixin \Eloquent
 */
class DocumentCoordinate extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $connection = 'sqlsrv';
}
