<?php

namespace App\Traits;

use App\Events\Documents\BatchProcessEvent;
use App\Models\Common\Attachment;
use App\Models\Common\SignLogging;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

trait MeteraiStampHelper
{
    use ApiResponse;
    /**
     * Summary of stamp
     * @param mixed $document
     * @param mixed $dataCheck
     * @param mixed $snColumn
     * @param mixed $serialTable
     * @param mixed $batch
     * @return void
     */
    public function stamp($document, $dataCheck, $snColumn, $serialTable, $batch, $fileName, $count)
    {
        $token = $this->getConfigByName('Token');
        $fileName = Attachment::where('source_id', $document->id)
            ->where('type', 'peruri')
            ->first();
        $url = $this->getConfigByName('Stamping');
        $serial = $serialTable->$snColumn;
        $imageName = $serial . '.' . 'png';
        $stamp = '/STAMP/' . $imageName;

        $dataLog = [
            'created_by' => $document->created_by,
            'id_document' => $document->external_document_number,
            'request_time' => Carbon::now(),
            'file_path' => $stamp,
            'serial_number' => $serialTable->$snColumn
        ];


        if ($serialTable->meterai_coordinate == 'Y') {
            // $doc = '/UNSIGNED/' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';
            $doc = '/UNSIGNED/' . basename($fileName->file_name, '.pdf') . '.pdf';
            $doc_final = '/SIGNED/final_' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';
            $privyBackupPlan = $this->getConfigByName('UserPrivyBackupPlan', 'GENERAL');
            if (!Storage::disk('ftp')->exists($doc_final)) {
                // code...
                $params = [
                    // 'onPrem' => false,
                    'certificatelevel' => 'NOT_CERTIFIED',
                    'dest' => '/sharefolder' . $doc_final,
                    // 'dest' =>  $doc_final,
                    'docpass' => (!empty($document->password)) ? $document->password : '',
                    'jwToken' => $token,
                    'location' => $document->location,
                    'profileName' => $document->profile_name,
                    'reason' => $document->type,
                    'refToken' => $serial,
                    'spesimenPath' => '/sharefolder' . $stamp,
                    // 'spesimenPath' =>  $stamp,
                    'src' => '/sharefolder' . $doc,
                    'retryFlag' => '1',
                    // 'src' => $doc,
                    'visLLX' => ($document->sign_payment == '2') ? doubleval($serialTable->vis_llx) : (doubleval($serialTable->vis_llx) - 50),
                    'visLLY' => ($document->sign_payment == '2') ? doubleval($serialTable->vis_lly) : (doubleval($serialTable->vis_lly) + 40),
                    'visURX' => ($document->sign_payment == '2') ? doubleval($serialTable->vis_urx) : (doubleval($serialTable->vis_urx) - 50),
                    'visURY' => ($document->sign_payment == '2') ? doubleval($serialTable->vis_ury) : (doubleval($serialTable->vis_ury) + 40),
                    // 'visURY' => $arrayMaterai{0}->visURY,
                    'visSignaturePage' => (int) $serialTable->materai_page,
                ];
                $responseStamp = Http::withoutVerifying()
                    ->timeout(90)
                    ->retry(3, 100)
                    // ->withOptions(["verify" => false])
                    ->withToken($token)
                    ->post($url, $params);

                $mergeResponse = array_merge($dataLog, [
                    'log_type' => 'request',
                    'request_type' => 'STAMP',
                    'payload_request' => json_encode($params),
                    'status' => 'USED',
                    'serial_number' => $serial,
                ]);
                SignLogging::create($mergeResponse);

                if ($responseStamp->failed()) {
                    $this->failedSubmitStamp($dataLog, $params, $responseStamp, $serial, $dataCheck, $batch);
                }

                $responseStampTemp = $responseStamp;
                $responseStamp = $responseStamp->collect();

                if ($responseStamp['errorCode'] == '00') {
                    $this->successStamp($responseStamp, $dataLog, $params, $responseStampTemp, $serial, $document, $doc_final, $fileName, $batch, $dataCheck);
                    if ($count > 1) {
                        Storage::disk('ftp')->delete($doc);
                        Storage::disk('ftp')->copy($doc_final, $doc);
                    }
                    $doc = Storage::disk('ftp')->get($doc_final);
                    if ($privyBackupPlan == '1') {
                        Storage::disk('app_public')->put('/docs/' . $fileName->file_name, $doc);
                    } else {
                        Storage::disk('app_public')->put('/documents/' . $fileName->file_name, $doc);
                    }
                } else {
                    $this->errorStamp($responseStampTemp, $dataLog, $params, $serial, $batch, $document, $responseStamp, $dataCheck);
                }
            } else {
                $doc = Storage::disk('ftp')->get($doc_final);
                if ($privyBackupPlan == '1') {
                    Storage::disk('app_public')->put('/docs/' . $fileName->file_name, $doc);
                } else {
                    Storage::disk('app_public')->put('/documents/' . $fileName->file_name, $doc);
                }
            }
        }
        // event(new BatchProcessEvent($document->created_by, $document->id));
    }
    /**
     * Summary of errorStamp
     * @param mixed $responseStampTemp
     * @param mixed $dataLog
     * @param mixed $params
     * @param mixed $serial
     * @param mixed $batch
     * @param mixed $document
     * @param mixed $responseStamp
     * @param mixed $dataCheck
     * @throws \Exception
     * @return never
     */
    public function errorStamp($responseStampTemp, $dataLog, $params, $serial, $batch, $document, $responseStamp, $dataCheck)
    {
        $res = $responseStampTemp->collect();
        $mergeResponse = array_merge($dataLog, [
            'log_type' => 'callback',
            'request_type' => 'STAMP',
            'payload_request' => json_encode($params),
            'payload_response' => $responseStampTemp->body(),
            'response_code' => $res['errorCode'],
            'response_time' => Carbon::now(),
            'status' => 'FAILED',
            'serial_number' => $serial,
        ]);
        SignLogging::create($mergeResponse);

        $dataUpdate = [
            'batch_id' => $batch->id,
            'status' => 'Processing ' . $batch->progress() . '%',
            'callback_message' => 'Error stamp ' . $document->external_document_number,
            'callback_trace' => $responseStamp['errorCode'] . ' ' . $responseStamp['errorMessage']
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        // $this->fail($responseStamp['errorCode'] . ' ' . $responseStamp['errorMessage']);
        throw new \Exception($responseStamp['errorCode'] . ' ' . $responseStamp['errorMessage'], 1);
    }

    /**
     * Summary of successStamp
     * @param mixed $responseStamp
     * @param mixed $dataLog
     * @param mixed $params
     * @param mixed $responseStampTemp
     * @param mixed $serial
     * @param mixed $document
     * @param mixed $doc_final
     * @param mixed $fileName
     * @param mixed $batch
     * @param mixed $dataCheck
     * @throws \Exception
     * @return void
     */
    public function successStamp($responseStamp, $dataLog, $params, $responseStampTemp, $serial, $document, $doc_final, $fileName, $batch, $dataCheck)
    {
        $res = $responseStamp->collect();
        $mergeResponse = array_merge($dataLog, [
            'log_type' => 'request',
            'request_type' => 'STAMP',
            'payload_request' => json_encode($params),
            'payload_response' => $responseStampTemp->body(),
            //'response_code' => $res['statusCode'],
            'response_code' => '',
            'response_time' => Carbon::now(),
            'status' => 'STAMP',
            'serial_number' => $serial,
        ]);
        SignLogging::create($mergeResponse);

        $this->createLog(
            $document->id,
            'document',
            'E-meterai - Stamp Document Serial Number: ' . $serial,
            $this->userId
        );
        // $doc = '/sharefolder' . $doc_final;
        $doc = Storage::disk('ftp')->get($doc_final);
        Storage::disk('app_public')->put('/documents/' . $fileName->file_name, $doc);

        if (!Storage::disk('ftp')->exists($doc_final)) {
            throw new \Exception('E-METERAI: ' . $doc_final . ' file not exist', 1);
        }

        $dataUpdate = [
            'batch_id' => $batch->id,
            'status' => 'Processing ' . $batch->progress() . '%',
            'callback_message' => 'Success',
            'callback_trace' => 'Success Stamp Meterai'
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);

        return;
    }

    /**
     * Summary of failedSubmitStamp
     * @param mixed $dataLog
     * @param mixed $params
     * @param mixed $responseStamp
     * @param mixed $serial
     * @param mixed $dataCheck
     * @param mixed $batch
     * @throws \Exception
     * @return never
     */
    public function failedSubmitStamp($dataLog, $params, $responseStamp, $serial, $dataCheck, $batch)
    {
        $mergeResponse = array_merge($dataLog, [
            'log_type' => 'request',
            'request_type' => 'STAMP',
            'payload_request' => json_encode($params),
            'payload_response' => $responseStamp->body(),
            //'response_code' => $res['statusCode'],
            'response_code' => '',
            'response_time' => Carbon::now(),
            'status' => 'FAILED',
            'serial_number' => $serial,
        ]);
        SignLogging::create($mergeResponse);

        $dataUpdate = [
            'batch_id' => $batch->id,
            'status' => 'Processing ' . $batch->progress() . '%',
            'callback_message' => 'E-METERAI: ',
            'callback_trace' => $responseStamp
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        // $this->fail('E-METERAI: ' . $responseStamp['result']['err']);
        throw new \Exception('E-METERAI: ' . $responseStamp['result']['err'], 1);
    }
}
