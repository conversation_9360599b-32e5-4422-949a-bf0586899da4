<?php

namespace App\Http\Controllers\Report;

use App\Exports\ReportExport;
use App\Http\Controllers\Controller;
use App\Models\User\UserItmGrp;
use App\Services\Reports\GeneralReportService;
use App\Services\Reports\IssueApdDetailService;
use App\Services\Reports\IssueApdSummaryService;
use App\Services\Reports\IssueAtkDetailService;
use App\Services\Reports\IssueAtkSummaryService;
use App\Services\Reports\OpenReservationService;
use App\Services\Reports\ReceiptApdService;
use App\Services\Reports\ReportFuelUsageService;
use App\Services\Reports\ReportGoodIssueService;
use App\Services\Reports\ReportService;
use App\Services\Reports\ReportStockItemService;
use App\Services\Reports\ReportSummaryService;
use App\Services\Reports\ReportViewDataItService;
use App\Services\Reports\TriwulanReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    const REPORT_ALL = "ALL";
    const READY_STOCK = "Ready Stock";
    const NON_READY_STOCK = "Non Ready Stock";
    const ASSET = "Asset";
    const SERVICE = "Service";
    const STOCK_ITEM = "Stock Item";
    const OPEN_RESERVATION = "Open Reservation";
    const FUEL_USAGE = "Fuel Usage";
    const PENGELUARAN_BARANG_APD_DETAIL = "Pengeluaran Barang APD Detail";
    const PENGELUARAN_BARANG_APD_SUMMARY = "Pengeluaran Barang APD Summary";
    const PENGELUARAN_BARANG_ATK_DETAIL = "Pengeluaran Barang ATK Detail";
    const PENGELUARAN_BARANG_ATK_SUMMARY = "Pengeluaran Barang ATK Summary";
    const DAFTAR_DAN_JUMLAH_ATK_MASUK = "Daftar dan Jumlah ATK Masuk";
    const GOODS_ISSUE_HS = "Goods Issue HS";
    const ALL_SUMMARY = "All Summary";
    const READY_SUMMARY = "Ready Summary";
    const NON_READY_SUMMARY = "Non Ready Summary";
    const SERVICE_SUMMARY = "Service Summary";
    const VIEW_DATA_IT = "View Data IT";
    const TRIWULAN = "Report Triwulan";

    public function __construct(public ReportService $service)
    {
    }
    public function params(Request $request)
    {
        $report = [
            self::REPORT_ALL,
            self::READY_STOCK,
            self::NON_READY_STOCK,
            self::ASSET,
            self::SERVICE,
            self::STOCK_ITEM,
            self::OPEN_RESERVATION,
            self::FUEL_USAGE
        ];

        $merge_apd = [
            self::PENGELUARAN_BARANG_APD_DETAIL,
            self::PENGELUARAN_BARANG_APD_SUMMARY
        ];
        $merge_atk = [
            self::PENGELUARAN_BARANG_ATK_DETAIL,
            self::PENGELUARAN_BARANG_ATK_SUMMARY,
            self::DAFTAR_DAN_JUMLAH_ATK_MASUK
        ];
        $merge_data_it = [self::VIEW_DATA_IT];
        $merge_data_hs = [self::GOODS_ISSUE_HS];
        $merge_data_triwulan = [self::TRIWULAN];
        $merge_data_superuser = [
            self::ALL_SUMMARY,
            self::READY_SUMMARY,
            self::NON_READY_SUMMARY,
            self::SERVICE_SUMMARY
        ];

        if ($request->user()->hasAnyRole('Superuser')) {
            $report = array_merge(
                $report,
                $merge_atk,
                $merge_apd,
                $merge_data_it,
                $merge_data_superuser,
                $merge_data_hs,
                $merge_data_triwulan
            );
        }

        if ($request->user()->hasAnyRole('View Report APD')) {
            $report = array_merge($report, $merge_apd);
        }
        if ($request->user()->hasAnyRole('Admin Warehouse Safety', 'Admin E-RESEVATION SAFETY INVENTORY')) {
            $report = array_merge($report, $merge_data_hs);
        }

        if ($request->user()->hasAnyRole('View Report ATK')) {
            $report = array_merge($report, $merge_atk);
        }

        if ($request->user()->hasAnyRole(self::VIEW_DATA_IT)) {
            $report = array_merge($report);
        }

        if ($request->user()->hasAnyRole(["Admin E-RESERVATION BDM"])) {
            $report = array_merge($report, $merge_data_it, $merge_data_triwulan);
        }
        return $this->success([
            'item_type' => $report,
            'request_type' => ['ALL', 'SPB', 'NPB'],
            "itemDateType" => ["Date Create", "Required Date"]
        ]);
    }

    /**
     * Query the result based on the provided request parameters and item type.
     *
     * @param Request $request The request object containing parameters like itemType, requestType, dateFrom, dateTo, and user information.
     * @return mixed The result of the query based on the item type.
     */
    protected function queryResult($request)
    {
        $item_type = $request->itemType;
        $request_type = $request->requestType;
        $date_type = $request->dateType ?? 'Required Date';
        $date_from = $request->dateFrom;
        $date_to = $request->dateTo;
        $created_by = $request->user()->username;

        if (Str::contains($item_type, ['ALL'])) {
            $item_type = '';
        }

        if ($request_type == 'ALL') {
            $request_type = '';
        }

        $schema = (config('app.db_schema') !== null) ? config('app.db_schema') : 'IMIP_ERESV_LIVE';
        $db_name = config('app.db_sap');

        if ($item_type == self::STOCK_ITEM) {
            $service = new ReportStockItemService();
            $rows = $service->show($request);
        } elseif ($item_type == self::FUEL_USAGE) {
            $service = new ReportFuelUsageService();
            $rows = $service->show($request, $date_from, $date_to);
        } elseif ($item_type == self::PENGELUARAN_BARANG_APD_DETAIL) {
            $service = new ReportService();
            $rows = $service->queryIssue($request, ['IG04', 'MW-HSE'], ['ZSAF', '148']);
        } elseif ($item_type == self::PENGELUARAN_BARANG_APD_SUMMARY) {
            $rows = DB::connection('sqlsrv')
                ->select("CALL SP_REPORT_ISSUE_ITEM_APD_SUMMARY('$date_from', '$date_to')");
        } elseif ($item_type == self::PENGELUARAN_BARANG_ATK_DETAIL) {
            $service = new ReportService();
            $rows = $service->queryIssue($request, ['IG02', 'MW-GA'], ['ZOFS', '147']);
        } elseif ($item_type == self::PENGELUARAN_BARANG_ATK_SUMMARY) {
            $rows = DB::connection('sqlsrv')
                ->select("CALL SP_REPORT_ISSUE_ITEM_ATK_SUMMARY('$date_from', '$date_to',  '147')");
        } elseif ($item_type == self::GOODS_ISSUE_HS) {
            $service = new ReportGoodIssueService();
            $rows = $service->show($date_from, $date_to, 'out');
        } elseif ($item_type == self::DAFTAR_DAN_JUMLAH_ATK_MASUK) {
            $rows = DB::connection('sqlsrv')
                ->select("CALL SP_REPORT_RECEIPT_ITEM_ATK_SUMMARY('$date_from', '$date_to',  '147')");
        } elseif ($item_type == self::OPEN_RESERVATION) {
            $service = new OpenReservationService();
            $rows = $service->show(
                $request,
                $date_from,
                $date_to,
                $item_type,
                $request_type,
                $created_by,
                $db_name
            );
        } elseif ($item_type == self::VIEW_DATA_IT) {
            $service = new ReportViewDataItService();
            $rows = $service->show($date_from, $date_to, $request_type);
        } elseif ($item_type == self::TRIWULAN) {
            $service = new TriwulanReportService();
            $rows = $service->show($date_from, $date_to, $date_type);
        } elseif (
            Str::contains($item_type, [
                self::ALL_SUMMARY,
                self::READY_SUMMARY,
                self::NON_READY_SUMMARY,
                self::SERVICE_SUMMARY
            ])
        ) {
            $service = new ReportSummaryService();
            $rows = $service->show($request, $date_from, $date_to, $created_by);
        } else {
            $service = new GeneralReportService();
            $rows = $service->show($request, $date_from, $date_to, $item_type, $request_type, $created_by);
        }

        return $rows;
    }


    public function show(Request $request)
    {
        $rows = $this->queryResult($request);
        $item_type = $request->itemType;

        if ($item_type == self::STOCK_ITEM) {
            $service = new ReportStockItemService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif ($item_type == self::FUEL_USAGE) {
            $service = new ReportFuelUsageService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif (Str::contains($item_type, [self::PENGELUARAN_BARANG_APD_DETAIL])) {
            $service = new IssueApdDetailService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif (Str::contains($item_type, [self::DAFTAR_DAN_JUMLAH_ATK_MASUK])) {
            $service = new ReceiptApdService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif (Str::contains($item_type, [self::PENGELUARAN_BARANG_ATK_DETAIL])) {
            $service = new IssueAtkDetailService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif (Str::contains($item_type, [self::PENGELUARAN_BARANG_ATK_SUMMARY])) {
            $service = new IssueAtkSummaryService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif (Str::contains($item_type, [self::PENGELUARAN_BARANG_APD_SUMMARY])) {
            $service = new IssueApdSummaryService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif ($item_type == self::VIEW_DATA_IT) {
            $service = new ReportViewDataItService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif ($item_type == self::GOODS_ISSUE_HS) {
            $service = new ReportGoodIssueService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif ($item_type == self::TRIWULAN) {
            $service = new TriwulanReportService();
            $header = $service->header();
            $columns = $service->columns();
        } elseif (
            Str::contains($item_type, [
                self::ALL_SUMMARY,
                self::READY_SUMMARY,
                self::NON_READY_SUMMARY,
                self::SERVICE_SUMMARY
            ])
        ) {
            $service = new ReportSummaryService();
            $header = $service->header();
            $columns = $service->columns();
        } else {
            $service = new GeneralReportService();
            $header = $service->header();
            $columns = $service->columns();
        }

        return $this->success([
            'rows' => $rows,
            'header' => $header,
            'columns' => $columns,
        ]);
    }

    public function export(Request $request)
    {
        $rows_data = $this->queryResult($request);

        $item_type = $request->itemType;
        $rows = [];

        ini_set('memory_limit', '1014M');

        if ($item_type == self::STOCK_ITEM) {
            $service = new ReportStockItemService();
            $header = $service->header();
            $rows = $service->store($rows_data)['rows'];
        } elseif ($item_type == self::FUEL_USAGE) {
            $service = new ReportFuelUsageService();
            $header = $service->store($rows_data)['header'];
            $rows = $service->store($rows_data)['rows'];
        } elseif (Str::contains($item_type, [self::PENGELUARAN_BARANG_APD_DETAIL])) {
            $service = new IssueApdDetailService();
            $header = $service->header();
            $rows = $service->store($rows_data)['rows'];
        } elseif (Str::contains($item_type, [self::PENGELUARAN_BARANG_ATK_DETAIL])) {
            $service = new IssueAtkDetailService();
            $header = $service->header();
            $rows = $service->store($rows_data)['rows'];
        } elseif (Str::contains($item_type, [self::PENGELUARAN_BARANG_APD_SUMMARY])) {
            $service = new IssueApdSummaryService();
            $header = $service->header();
            $rows = $service->store($rows_data)['rows'];
        } elseif (Str::contains($item_type, [self::PENGELUARAN_BARANG_ATK_SUMMARY])) {
            $service = new IssueAtkSummaryService();
            $header = $service->header();
            $rows = $service->store($rows_data)['rows'];
        } elseif (Str::contains($item_type, [self::DAFTAR_DAN_JUMLAH_ATK_MASUK])) {
            $service = new ReceiptApdService();
            $header = $service->header();
            $rows = $service->store($rows_data)['rows'];
        } elseif ($item_type == self::VIEW_DATA_IT) {
            $service = new ReportViewDataItService();
            $header = $service->store($rows_data)['header'];
            $rows = $service->store($rows_data)['rows'];
        } elseif ($item_type == self::TRIWULAN) {
            $service = new TriwulanReportService();
            $header = $service->store($rows_data)['header'];
            $rows = $service->store($rows_data)['rows'];
        } elseif ($item_type == self::GOODS_ISSUE_HS) {
            $service = new ReportGoodIssueService();
            $header = $service->header();
            $rows = $service->store($rows_data)['rows'];
        } elseif (
            Str::contains($item_type, [
                self::ALL_SUMMARY,
                self::READY_SUMMARY,
                self::NON_READY_SUMMARY,
                self::SERVICE_SUMMARY
            ])
        ) {
            $service = new ReportSummaryService();
            $header = $service->store($rows_data)['header'];
            $rows = $service->store($rows_data)['rows'];
        } else {
            $service = new GeneralReportService();
            $header = $service->store($rows_data)['header'];
            $rows = $service->store($rows_data)['rows'];
        }

        return Excel::download(new ReportExport($header, $rows, $request->all()), "report.xlsx");
    }
}
