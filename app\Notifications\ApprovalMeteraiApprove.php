<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;

class ApprovalMeteraiApprove extends Notification implements ShouldQueue
{
    use Queueable;

    public $emailData = [];

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($emailData)
    {
        $this->emailData = $emailData;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        sleep(5);
        // Artisan::call('cache:clear');
        // Artisan::call('config:cache');
        return (new MailMessage)
            // ->to($this->emailData['to'])
            ->subject($this->emailData['subject'])
            ->greeting($this->emailData['greeting'])
            ->line($this->emailData['body'])
            ->line($this->emailData['inv']);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $status = $this->emailData['status'];
        return [
            'documentType' => 'E-Sign',
            'transactionType' => 'E-Sign Invoice',
            'documentId' => $this->emailData['document']->id,
            'documentNumber' => $this->emailData['document']->document_number,
            'title' =>  $this->emailData['document']->document_number . "has been $status. Please check the documents.",
            'content' => $this->emailData['document'],
            'subject' => $this->emailData['subject']
        ];
    }

    /**
     * Determine which queues should be used for each notification channel.
     *
     * @return array
     */
    public function viaQueues()
    {
        return [
            'mail' => 'document',
        ];
    }
}
