<?php

namespace App\Jobs;

use App\Models\Document\Document;
use App\Models\View\ViewEmployee;
use App\Services\ApprovalActionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessApprovalDocumentAction implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $rows;
    public $formApproval;
    public $approvalType;
    public $userId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($rows, $formApproval, $approvalType, $userId)
    {
        $this->approvalType = $approvalType;
        $this->userId = $userId;
        $this->rows = $rows;
        $this->formApproval = $formApproval;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        Log::info('begin process approval action');
        $service = new ApprovalActionService();

        $countDocument = count($this->rows);
        $requesterName = $service->getApprovalRequestData($this->rows, $this->approvalType)['requesterName'];
        $requesterCount = $service->getApprovalRequestData($this->rows, $this->approvalType)['requesterCount'];
        $requestItem = $service->getApprovalRequestData($this->rows, $this->approvalType)['requestItem'];

        $requesterNik = [];

        foreach ($this->rows as $row) {
            $document_id = ($this->approvalType) ? $row->document_id : $row->id;
            $document = Document::find($document_id);

            $service->checkFinalApproval($document, $requesterName, $requestItem, $requesterCount, $countDocument);

            $document = Document::find($document_id);
            $service->processSignDocument($document, $countDocument, $this->userId);

            $requesterNik[] = $document->userCreate->username;
        }

        $collection = collect($requesterNik);

        // Get all unique items.
        $uniqueItems = $collection->unique();

        $requester = ViewEmployee::whereIn('Nik', $uniqueItems)->first();
        $requestItem[$requester->Name][] = $document->external_document_number;

        $status = 'approved';
    }
}
