<?php

namespace App\Services;

use App\Models\Common\ApdNote;
use App\Models\Common\Vehicle;
use App\Models\Resv\ResvHeader;
use App\Models\View\ViewEmployee;
use Illuminate\Support\Facades\DB;

class ReservationDataService
{
    protected function getIoOrder()
    {
        $service = new SapS4Service();
        $service->login();
        $ioOrder = $service->getIoOrder();
        $data = [];
        foreach ($ioOrder['DATA'] as $key => $value) {
            $data[] = $value['ORDER'] . ' - ' . $value['DESC'];
        }

        return $data;
    }

    public function generateDocNum($sysDate): string
    {
        $data_date = strtotime($sysDate);
        $year_val = date('y', $data_date);
        $full_year = date('Y', $data_date);
        $month = date('m', $data_date);
        $day_val = date('j', $data_date);
        $end_date = date('t', $data_date);

        if ($day_val == 1) {
            // $docnum = (int) $year_val . $month . sprintf("%04s", "1");
            // $check_docnum = ResvHeader::where('DocNum', '=', $docnum)->first();

            $first_date = "$full_year-$month-01";
            $second_date = "$full_year-$month-$end_date";
            $doc_num = ResvHeader::selectRaw('ISNULL("DocNum", 0) as DocNum')
                ->whereBetween(DB::raw('CreateDate'), [$first_date, $second_date])
                ->orderBy("DocNum", "DESC")
                ->first();
            // $number = (empty($doc_num)) ? '00000000' : $doc_num->DocNum;
            // $clear_doc_num = (int) substr($number, 4, 7);
            // $number = $clear_doc_num + 1;

            // return (int) $year_val . $month . sprintf("%04s", $number);
            // return $doc_num->DocNum + 1;
            if (!$doc_num) {
                return (int) $year_val . $month . sprintf("%04s", "1");
            } else {
                // $first_date = "$full_year-$month-01";
                // $second_date = "$full_year-$month-$end_date";
                // $doc_num = ResvHeader::selectRaw('ISNULL("DocNum", 0) as DocNum')
                //     ->whereBetween(DB::raw('CreateDate'), [$first_date, $second_date])
                //     ->orderBy("DocNum", "DESC")
                //     ->first();
                $number = (empty($doc_num)) ? '00000000' : $doc_num->DocNum;
                $clear_doc_num = (int) substr($number, 4, 7);
                $number = $clear_doc_num + 1;
                // return (int) $year_val . $month . sprintf("%04s", $number);
                return $doc_num->DocNum + 1;
            }
        } else {
            $first_date = "$full_year-$month-01";
            $second_date = "$full_year-$month-$end_date";
            $doc_num = ResvHeader::selectRaw('ISNULL("DocNum", 0) as DocNum')
                ->whereBetween(DB::raw('CreateDate'), [$first_date, $second_date])
                ->orderBy("DocNum", "DESC")
                ->first();
            $number = (empty($doc_num)) ? '00000000' : $doc_num->DocNum;
            $clear_doc_num = (int) substr($number, 4, 7);
            $number = $clear_doc_num + 1;
            if (!$doc_num) {
                return (int) $year_val . $month . sprintf("%04s", $number);
            }
            // throw new \Exception($year_val . $month . sprintf("%04s", $number));
            return $doc_num->DocNum + 1;
        }
    }

    public function getApdNotes()
    {
        return ApdNote::select('notes')->get()->pluck('notes');
    }

    public function details()
    {
        $ioOrder = $this->getIoOrder();

        $vehicle = Vehicle::pluck('vehicle_no');
        $department = ViewEmployee::select('Department')
            ->whereIn('Company', ['PT IMIP', 'PT BDT'])
            ->where('WorkLocation', 'LIKE', '%MOROWALI%')
            ->distinct()
            ->pluck('Department');

        $config = [
            'colHeaders' => [
                'Order Id',
                '',
                'Item Code',
                'Item Name',
                '',
                'Asset Code',
                'Asset Name',
                'Category',
                'UoM',
                'WH',
                'Req. Qty',
                'Req. Date',
                'Notes',
                'OnHand',
                'Available',
                'Other Resv. No ',
                'SPB',
                'NPB',
                'Last Res. By',
                'Vehicle No',
                'Mileage',
                'Delete',
                'DocEntry',
                'LineStatus',
                'OIGRDocNum',
                'InvntItem',
                'GIR Status',
                'Attachment',
                'CountAttachment',
                'ItemGroup',
                'SubGroup',
                'U_Period',
                'U_Category',
                'U_AppResBy',
                'Employee NIK',
                'Employee NAme',
                'Department',
            ],
            'copyable' => true,
            'copyPaste' => true,
            'columns' => [
                // TODO
                [
                    // 0
                    'data' => 'ItemCodeRender',
                    'width' => 30,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 1
                    'data' => 'ItemCode',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'readOnly' => true,
                ],
                [
                    // 2
                    'data' => 'ItemName',
                    'width' => 200,
                    'height' => 26,
                    'wordWrap' => false,
                    '// readOnly' => true,
                ],
                [
                    // 3
                    'data' => 'AssetCodeRender',
                    'width' => 30,
                    'height' => 26,
                    'wordWrap' => false,
                    'renderer' => 'AssetCodeRender',
                ],
                [
                    // 4
                    'data' => 'AssetCode',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'readOnly' => true,
                ],
                [
                    // 5
                    'data' => 'AssetName',
                    'width' => 200,
                    'height' => 26,
                    'wordWrap' => false,
                    'readOnly' => true,
                ],
                [
                    // 0
                    'data' => 'OrderId',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'dropdown',
                    'source' => $ioOrder
                ],
                [
                    // 6
                    'data' => 'ItemCategory',
                    'width' => 70,
                    'height' => 26,
                    'wordWrap' => false,
                    'readOnly' => true,
                ],
                [
                    // 7
                    'data' => 'UoMCode',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 8
                    'data' => 'WhsCode',
                    'width' => 60,
                    'height' => 26,
                    'wordWrap' => false,
                    'readOnly' => true,
                ],
                [
                    // 9
                    'data' => 'ReqQty',
                    'width' => 80,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'numeric',
                    'numericFormat' => [
                        'pattern' => '0,0.00',
                    ],
                ],
                [
                    // 10
                    'data' => 'ReqDate',
                    'type' => 'date',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'dateFormat' => 'YYYY-MM-DD',
                    'correctFormat' => true,
                    'datePickerConfig' => [
                        'firstDay' => 0,
                        'showWeekNumber' => true,
                        'numberOfMonths' => 1,
                    ],
                ],
                [
                    // 11
                    'data' => 'ReqNotes',
                    'width' => 200,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 12
                    'data' => 'OnHand',
                    'width' => 80,
                    'height' => 26,
                    'wordWrap' => false,
                    'readOnly' => true,
                    'type' => 'numeric',
                    'numericFormat' => [
                        'pattern' => '0,0.00',
                    ],
                ],
                [
                    // 13
                    'data' => 'AvailableQty',
                    'width' => 80,
                    'height' => 26,
                    'wordWrap' => false,
                    'readOnly' => true,
                    'type' => 'numeric',
                    'numericFormat' => [
                        'pattern' => '0,0.00',
                    ],
                ],
                [
                    // 14
                    'data' => 'OtherResvNo',
                    'width' => 150,
                    'height' => 26,
                    'wordWrap' => false,
                ],

                [
                    // 15
                    'data' => 'SPB',
                    'width' => 50,
                    'height' => 26,
                    'wordWrap' => false,
                    'className' => 'htCenter ',
                    'type' => 'checkbox',
                    'checkedTemplate' => 'Y',
                    'uncheckedTemplate' => 'N',
                ],

                [
                    // 16
                    'data' => 'NPB',
                    'width' => 50,
                    'height' => 26,
                    'wordWrap' => false,
                    'className' => 'htCenter ',
                    'type' => 'checkbox',
                    'checkedTemplate' => 'Y',
                    'uncheckedTemplate' => 'N',
                ],
                [
                    // 17
                    'data' => 'LastReqBy',
                    'width' => 150,
                    'height' => 26,
                    'readOnly' => true,
                    'wordWrap' => false,
                ],
                [
                    // 18
                    'data' => 'VehicleNo',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'dropdown',
                    'source' => $vehicle,
                ],
                [
                    // 19
                    'data' => 'Mileage',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'numeric',
                    'numericFormat' => [
                        'pattern' => '0,0.00',
                    ],
                ],

                [
                    // 20
                    'data' => 'Delete',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],

                [
                    // 21
                    'data' => 'LineEntry',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],

                [
                    // 22
                    'data' => 'LineStatus',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],

                [
                    // 23
                    'data' => 'OIGRDocNum',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 24
                    'data' => 'InvntItem',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 25
                    'data' => 'SAPGIRStatus',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 26
                    'data' => 'Attachment',
                    'width' => 120,
                    'height' => 26,
                    'wordWrap' => false,
                    'renderer' => 'AttachmentRender',
                ],
                [
                    // 27
                    'data' => 'CountAttachment',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 28
                    'data' => 'ItemGroup',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 29
                    'data' => 'SubGroup',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                ],
                [
                    // 30
                    'data' => 'U_Period',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'text',
                ],
                [
                    // 31
                    'data' => 'U_Category',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'text',
                ],
                [
                    // 32
                    'data' => 'U_AppResBy',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'text',
                ],
                [
                    // 33
                    'data' => 'EmployeeId',
                    'width' => 100,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'text',
                ],
                [
                    // 34
                    'data' => 'EmployeeName',
                    'width' => 170,
                    'height' => 26,
                    'wordWrap' => false,
                    'readOnly' => true,
                    'type' => 'text',
                ],
                [
                    // 35
                    'data' => 'U_Department',
                    'width' => 200,
                    'height' => 26,
                    'wordWrap' => false,
                    'type' => 'dropdown',
                    'source' => $department,
                    'strict' => true,
                    'filter' => false,
                    'allowInvalid' => false,
                ],
            ],
            'currentRowClassName' => 'currentRow',
            'currentColClassName' => 'currentCol',
            'startRows' => 0,
            'manualColumnFreeze' => true,
            'currData' => [],
            'rowHeaders' => true,
            'manualColumnResize' => true,
            'manualRowResize' => true,
            'autoRowSize' => false,
            'autoColumnSize' => false,
            'viewportRowRenderingOffset' => 1000,
            'viewportColumnRenderingOffset' => 100,
            'colWidths' => 80,
            'rowHeights' => 23,
            'persistentState' => true,
            'width' => '100%',
            'stretchH' => 'all',
            'dataSchema' => [
                'OrderId' => null
            ]
        ];
    }
}
