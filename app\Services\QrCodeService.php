<?php

namespace App\Services;

use App\Jobs\RemoveAttachment;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class QrCodeService
{
    /**
     * generatePath
     *
     * @param  mixed $path
     * @return void
     */
    public function generatePath($path)
    {
        if (!file_exists($path)) {
            if (!mkdir($path, 0777, true) && !is_dir($path)) {
                throw new \RuntimeException(
                    sprintf(
                        'Directory "%s" was not created',
                        $path
                    )
                );
            }
        }
    }

    /**
     * generateQrCode
     *
     * @param  mixed $qr_file
     * @param  mixed $file_export_name
     * @param  mixed $size
     */
    public function generateQrCode($qr_file, $file_export_name, $size, $showLogo = true)
    {
        $qr_code = QrCode::size($size)
            ->format('png');
        if ($showLogo) {
            $qr_code->merge('/public/images/logo2.png', .15);
        }
        // ->errorCorrection('H')
        $qr_code->generate($qr_file, public_path('images/qrcode/' . $file_export_name . '.png'));

        return $qr_code;
    }

    public function generateQrCodeDoc($qr_file, $file_export_name, $size, $showLogo = true)
    {
        $qr_code = QrCode::size($size)
            ->style('round')
            ->format('png');
        if ($showLogo) {
            $qr_code->merge('/public/images/logo2.png', .20);
        }
        // ->errorCorrection('H')
        $qr_code->generate($qr_file, $file_export_name);

        return $qr_code;
    }

    public function mergeImage($qrimage, $speciment, $newname, $dst_x, $dst_y, $src_w, $src_h, $file_export_name)
    {
        list($new_width, $new_height, $new_type, $new_attr) = getimagesize($speciment);

        $new = imagecreatefrompng($speciment);

        $master = imagecreatefrompng($qrimage);

        imagealphablending($master, false);
        imagesavealpha($master, true);

        imagecopymerge($master, $new, $dst_x, $dst_y, 0, 0, $src_w, $src_h, 100);
        // imagecopymerge ( resource $dst_im , resource $src_im , int $dst_x , int $dst_y , int $src_x , int $src_y , int $src_w , int $src_h , int $pct )

        // generate path
        $this->generatePath(public_path() . '/images/speciment/');

        //save image
        imagepng($master, $newname);

        imagedestroy($master);
        imagedestroy($new);
    }
}
