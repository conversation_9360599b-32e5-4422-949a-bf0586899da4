<?php

namespace App\Jobs;

use App\Models\Document\Document;
use App\Services\ApprovalActionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessPendingDocumentQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $rows;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($rows)
    {
        $this->rows = $rows;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $countDocument = count($this->rows);
        $service = new ApprovalActionService();
        
        Log::info('process ' . $countDocument . ' documents that approved but status still pending');

        foreach ($this->rows as $row) {
            $document = Document::find($row->id);
            $document->status = 'approved - on process';
            $document->save();
            $service->processSignDocument($document, $countDocument, 1);
        }
    }
}
