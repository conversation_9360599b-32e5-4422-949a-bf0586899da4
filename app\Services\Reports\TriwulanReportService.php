<?php

namespace App\Services\Reports;

use Illuminate\Support\Facades\DB;
use App\Models\Resv\ResvHeader;

class TriwulanReportService
{
    public function show($date_from, $date_to, $dateType)
    {
        $user = auth()->user();
        $rows = ResvHeader::select(
            "resv_details.ItemCode",
            "resv_details.ItemName",
            "resv_details.UoMCode",
            "resv_details.LineNum",
            DB::raw("SUM(resv_details.ReqQty) as ReqQty")

        )
            ->leftJoin("resv_details", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            ->when($dateType, function ($query) use ($dateType, $date_from, $date_to) {
                if ($dateType == 'Date Create') {
                    $query->whereBetween("resv_headers.CreateDate", [$date_from, $date_to]);
                } else {
                    $query->whereBetween("resv_headers.RequiredDate", [$date_from, $date_to]);
                }
            })
            ->where("resv_headers.CategoryType", "=", "Triwulan")
            ->when($user, function ($query) use ($user) {
                if (!$user->hasAnyRole(['Superuser', 'Admin E-RESERVATION BDM WAREHOUSE'])) {
                    $query->where("resv_headers.CreatedBy", $user->username);
                }
            })
            ->groupBy([
                "resv_details.ItemCode",
                "resv_details.ItemName",
                "resv_details.UoMCode",
                "resv_details.LineNum",
            ])
            ->orderBy("resv_details.ItemCode")
            ->get();
        $service = new ReportService();
        return $service->getS4Docs($rows);
    }

    public function header()
    {
        return [
            'Item Code',
            'Item Name',
            'UoM',
            'Request Qty',
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "UoMCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ReqQty",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value->ItemCode,
                $value->ItemName,
                $value->UoMCode,
                $value->ReqQty,
            ];
        }
        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}