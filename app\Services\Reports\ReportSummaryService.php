<?php

namespace App\Services\Reports;

use App\Models\Resv\ResvHeader;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ReportSummaryService
{
    public function show($request, $date_from, $date_to, $created_by)
    {
        $rows = ResvHeader::select(
            "resv_headers.DocNum",
            "resv_headers.DocDate",
            "resv_headers.RequiredDate",
            "resv_headers.WhsCode",
            DB::raw('resv_headers."RequestType" as "HeaderRequestType"'),
            "resv_headers.ItemType",
            "resv_headers.RequesterName",
            "resv_headers.Memo",
            "resv_headers.WorkLocation",
            "resv_headers.DocStatus",
        )
            ->whereRaw("resv_headers.\"DocDate\" BETWEEN '${date_from}' AND '${date_to}' ")
            // ->whereRaw("resv_headers.\"ItemType\" LIKE '%${item_type}%'")
            ->where("resv_headers.ApprovalStatus", "=", "Y")
            ->when($request, function ($query) use ($request) {
                if (!$request->user()->hasAnyRole(['Superuser'])) {
                    $query->where("resv_headers.WorkLocation", "=", $request->user()->location);
                }
            });

        if (
            Str::contains($request->user()->department, ['WAREHOUSE', 'SAP'])
            || $request->user()->hasAnyRole(['Admin Warehouse', 'Superuser'])
        ) {

            $rows = $rows->orderBy("resv_headers.DocNum")
                ->get();
        } else {
            if ($request->user()->hasAnyRole('View Data IT')) {
                $rows = $rows
                    ->whereIn("resv_headers.WhsCode", ["MW-IT", "MW-ZZ"])
                    // ->orWhereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
                    ->orderBy("resv_headers.DocNum")
                    ->get();
            } else {
                $rows = $rows->whereRaw("resv_headers.\"CreatedBy\" = '${created_by}' ")
                    ->orderBy("resv_headers.DocNum")
                    ->get();
            }
        }

        $service = new ReportService();
        $rows = $service->getS4Docs($rows, "Header");

        return $rows;
    }

    public function header()
    {
        return [
            'DocNum',
            'Doc Date',
            'Req Date',
            'WH',
            'Request Type',
            'Item type',
            'Requester',
            'Memo',
            'GIR NO',
            'PR NO',
            'PO NO',
            'GRPO NO',
            'GI NO',
            'Doc Status',
            'App Status'
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "DocNum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "RequiredDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "WhsCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "HeaderRequestType",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemType",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],

            [
                'data' => "RequesterName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Memo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_GIRNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_PRNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "PONum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "GRPONum",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_GINo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocumentStatus",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value->DocNum,
                $value->DocDate,
                $value->RequiredDate,
                $value->WhsCode,
                $value->HeaderRequestType,
                $value->ItemType,
                $value->RequesterName,
                $value->Memo,
                $value->SAP_GIRNo,
                $value->SAP_PRNo,
                $value->PONum,
                $value->GRPONum,
                $value->SAP_GINo,
                $value->DocumentStatus,
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
