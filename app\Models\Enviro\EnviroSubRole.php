<?php

namespace App\Models\Enviro;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Enviro\EnviroSubRole
 *
 * @property int $id
 * @property string $name
 * @property int $role_id
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole query()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole whereRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroSubRole whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class EnviroSubRole extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
