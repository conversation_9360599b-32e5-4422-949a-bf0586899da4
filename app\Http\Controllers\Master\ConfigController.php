<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Master\Config;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ConfigController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = Config::select('id', 'key', 'value', 'type', 'company')
            ->get();

        if (count($data) < 1) {
            $data = [
                [
                    'id' => null,
                    'key' => null,
                    'value' => null,
                    'type' => null,
                    'company' => null,
                ]
            ];
        }

        return $this->success([
            'rows' => $data,
            'columns' => [
                [
                    'data' => 'id',
                    'width' => 50
                ],
                [
                    'data' => 'key',
                    'width' => 11
                ],
                [
                    'data' => 'value',
                    'width' => 50
                ],
                [
                    'data' => 'type',
                    'width' => 10
                ],
                [
                    'data' => 'company',
                    'width' => 10
                ],
            ],
            'header' => ['Id', 'Key', 'Value', 'Type', 'Company'],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        try {
            foreach ($details as $detail) {
                if (empty($detail['key'])) {
                    return $this->error('Key cannot empty', '422');
                }
                $brand = Config::where('id', '=', $detail['id'])->first();
                if (!$brand) {
                    $brand = new Config();
                }
                $brand->key = $detail['key'];
                $brand->value = $detail['value'];
                $brand->type = $detail['type'];
                $brand->company = $detail['company'];
                $brand->save();
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = Config::where('category', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param Config $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Config $productBrand)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            Config::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
