<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\MasterSubCategory
 *
 * @property int $id
 * @property string $title
 * @property string|null $slug
 * @property int $category_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $category_name
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory whereCategoryName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterSubCategory whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterSubCategory extends Model
{
    use HasFactory;

    protected $table = 'master_sub_category';

    protected $guarded = [];

    protected $connection = 'sqlsrv';
}
