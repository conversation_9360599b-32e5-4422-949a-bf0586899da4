<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAnnotationPropertyToDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->string('digisign_id')->nullable();
            $table->string('digisign_color')->nullable();
            $table->string('digisign_stamp_type')->nullable();
            $table->string('digisign_page_index')->nullable();
            
            $table->string('meterai_id')->nullable();
            $table->string('meterai_color')->nullable();
            $table->string('meterai_stamp_type')->nullable();
            $table->string('meterai_page_index')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('documents', function (Blueprint $table) {
            //
        });
    }
}
