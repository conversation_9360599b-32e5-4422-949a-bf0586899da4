<?php

namespace App\Models\Task;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Task\HelpDesk
 *
 * @method static \Illuminate\Database\Eloquent\Builder|HelpDesk newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HelpDesk newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HelpDesk query()
 * @mixin \Eloquent
 */
class HelpDesk extends Model
{
    use HasFactory;
}
