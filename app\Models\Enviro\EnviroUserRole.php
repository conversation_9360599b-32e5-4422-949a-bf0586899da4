<?php

namespace App\Models\Enviro;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Enviro\EnviroUserRole
 *
 * @property int $id
 * @property int $user_id
 * @property int $enviro_role_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserRole newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserRole newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserRole query()
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserRole whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserRole whereEnviroRoleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserRole whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserRole whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|EnviroUserRole whereUserId($value)
 * @mixin \Eloquent
 */
class EnviroUserRole extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
