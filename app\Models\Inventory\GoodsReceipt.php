<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Inventory\GoodsReceipt
 *
 * @property int $id
 * @property int $doc_number
 * @property int $created_by
 * @property int|null $updated_by
 * @property string $post_date
 * @property string $notes
 * @property string $status
 * @property string $whs_code
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt query()
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereDocNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt wherePostDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GoodsReceipt whereWhsCode($value)
 * @mixin \Eloquent
 */
class GoodsReceipt extends Model
{
    use HasFactory;
}
