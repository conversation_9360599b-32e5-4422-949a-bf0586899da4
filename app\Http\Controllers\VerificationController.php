<?php

namespace App\Http\Controllers;

use App\Models\Approval\ApprovalStage;
use App\Models\Document\Document;
use App\Models\Inventory\Inventory;
use App\Models\Paper\Paper;
use App\Models\Resv\ResvHeader;
use App\Models\View\ViewApprovalStage;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Vinkla\Hashids\Facades\Hashids;

class VerificationController extends Controller
{
    /**
     * @param Request $request
     * @param $str_url
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function verification(Request $request, $str_url)
    {
        try {
            $html = null;
            $document = Document::where('str_url', '=', $str_url)
                ->with(['attachment', 'userCreate', 'coordinate', 'approver'])
                ->first();


            $reservation = ResvHeader::where('U_DocEntry', '=', Hashids::decode($str_url)[0])
                ->with(['details.issueReceipt.header', 'creator', 'issueReceipt'])
                ->first();
            // return response()->json($reservation);

            // throw new \Exception(json_encode($document), 1);
            if ($document) {

                $approver = ApprovalStage::where('document_id', $document->id)
                    ->whereNotNull('document_id')
                    ->leftJoin('users', 'users.id', 'approval_stages.user_id')
                    ->select('approval_stages.*', 'users.name as user_name')
                    ->get();

                if (count($approver) == 0) {
                    $approver = ViewApprovalStage::where('DocumentReferenceID', $document->document_number)
                        ->selectRaw("Name As user_name, ResponseDate as response_date, 'approved                      ' as status  ")
                        ->get();
                }

                $html = view('validation.document')
                    ->with([
                        'document' => $document,
                        'approver' => $approver,
                        'logo' => $this->getLogo($document->company)
                    ])
                    ->render();
            } elseif ($reservation) {

                // return response()->json($issueReceipt);
                if (Str::contains($request->type, ['reservation'])) {
                    $approver = ViewApprovalStage::where('DocumentReferenceID', $reservation->DocNum)
                        ->selectRaw("Name As user_name, ResponseDate as response_date, 'approved                      ' as status  ")
                        ->get();
                    $html = view('validation.eresv')
                        ->with([
                            'reservation' => $reservation,
                            'approver' => $approver,
                            'logo' => $this->getLogo($reservation->creator->company)
                        ])
                        ->render();
                } else {
                    $issueReceipt = Inventory::where('id', Hashids::decode($str_url)[0])
                        ->with('lineItems.resvDetail.header')
                        ->first();
                    $approver = [];
                    $html = view('validation.issue_receipt')
                        ->with([
                            'reservation' => $issueReceipt,
                            'logo' => $this->getLogo($reservation->creator->company)
                        ])
                        ->render();
                }
            } else {
                $paper = Paper::select(
                    'papers.*',
                    'B.name as paper_name',
                    'B.alias',
                    'B.alias as paper_alias',
                )
                    ->leftJoin('master_papers as B', 'b.id', 'papers.master_paper_id')
                    ->where('papers.deleted', '=', 'N')
                    ->where('papers.str_url', '=', $str_url)
                    ->first();

                if ($paper) {
                    if ($paper->alias === 'stkpd') {
                        $html = view('validation.form_duty', compact('paper'))->render();
                    } else {
                        $html = view('validation.form_other', compact('paper'))->render();
                    }
                }
            }


            return response()->json([
                'rows' => $html,
                'approver' => $approver,
                'document' => $document
            ]);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 422);
        }
    }

    private function getLogo($company)
    {
        switch ($company) {
            case 'PT IMIP':
            case 'PT BDT':
                return [
                    'header' => 'logo/IMIP-HEADER.png',
                    'footer' => 'logo/IMIP-FOOTER.png'
                ];

            case 'PT MMM':
                return [
                    'header' => 'logo/MMM-HEADER.png',
                    'footer' => 'logo/MMM-FOOTER.png'
                ];

            case 'PT BDM':
                return [
                    'header' => 'logo/BDM-HEADER.png',
                    'footer' => 'logo/BDM-FOOTER.png'
                ];
        }
    }

    public function verificationDocument(Request $request, $str_url)
    {
        try {
            $str = $request->ref;
            $paper = Document::where('external_document_number', 'LIKE', '%' . $str . '%')
                ->first();

            $html = null;
            if ($paper) {
                $html = view('validation.invoice', compact('paper'))->render();
            }

            return response()->json([
                'rows' => $html,
                'pdfSource' => config('app.url') . '/documents/contract.pdf',
                'displayContract' => ($this->getConfigByName('DisplayContract', 'GENERAL') == 'Y' ? true : false)
            ]);
        } catch (\Exception $exception) {
            return response()->json(['message' => $exception->getMessage()], 422);
        }
    }
}
