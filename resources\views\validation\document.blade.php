<p class="{{ (Str::contains($document->document_type, ['PT IMIP', 'PT BDM'])) ? 'text-left': 'text-center' }}" style="margin-top: 5px">
    {{-- <img src="{{ asset($logo['header']) }}" alt="" style="width: auto; height: 50px;"> --}}
    <img src="{{ asset($logo['header']) }}" alt="" style="width: 100%; height:auto">
</p>

<p class="text-center">

{{-- <h3 class="text-center">{{ $document->paper_name }}</h3> --}}

<p class="text-center" style="margin-top: -12px;">
    <strong style="border-bottom: 1px solid #222;">Nomor: {{ $document->external_document_number }}</strong>
</p>


<table>
   {{--  <tr>
        <td>Document Type</td>
        <td> : </td>
        <td>{{ $document->document_type }}</td>
    </tr> --}}
    <tr>
        <td>Tanggal Dokumen</td>
        <td> : </td>
        <td>{{ $document->document_date }}</td>
    </tr>
    <tr>
        <td>Customer</td>
        <td> : </td>
        <td>{{ $document->customer_name }}</td>
    </tr>

    <tr>
        <td>Catatan</td>
        <td> : </td>
        <td>{{ $document->remark }}</td>
    </tr>
</table>

<table class="mt-4">
    <tr>
        <td><strong>Details</strong></td>
        <td><strong>Status</strong></td>
    </tr>

    @foreach ($approver as $element)
        <tr>
            <td>

                {{ $element->user_name }} <br>
                ({{ date('Y-m-d H:i:s', strtotime($element->response_date)) }})
            </td>
            <td>
                @if ($element->status == 'approved                      ')
                    Signed
                @endif

            </td>
        </tr>
    @endforeach
</table>

<br>
<br>
<br>

<p class="{{ (Str::contains($document->document_type, ['PT IMIP', 'PT BDM'])) ? 'text-left': 'text-center' }}" style="margin-bottom: -2px">
    <img src="{{ asset($logo['footer']) }}" alt="" style="width: 100%;">
</p>
{{-- <p class="text-center" style="margin-bottom: -2px">
    <small>
        <b>PT. Indonesia Morowali Industrial Park</b>
    </small>
</p>
<p class="text-center" style="margin-bottom: -2px">
    <small>
        Gedung IMIP, Jl. Batu Mulia No 8, Taman Meruya Hilir Blok N, Meruya Utara, Kembangan, Kota Jakarta Barat, DKI
        Jakarta
    </small>
</p>
<p class="text-center">
    <small>
        Phone : +62 21 2941 9688 │Fax : +62 21 2941 9696 │E-mail : <EMAIL> │www.imip.co.id
    </small>
</p> --}}

