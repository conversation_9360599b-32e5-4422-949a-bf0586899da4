<?php

namespace App\Http\Controllers\Paper;

use App\Http\Controllers\Controller;
use App\Jobs\SendClinicNotificationEmail;
use App\Models\Common\Attachment;
use App\Models\Paper\Paper;
use App\Models\User;
use App\Models\Paper\PaperDetails;
use App\Models\View\ViewEmployee;
use App\Services\PaperService;
use App\Traits\CherryApproval;
use App\Traits\RolePermission;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use App\Models\Master\Airport;

class PaperApiController extends Controller
{
    use CherryApproval, RolePermission;

    public $service;

    public function __construct(PaperService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // return $this->successApps($request->all(), '', 200, $request);
            $options = json_decode($request->options);
            $paramenter = (object) $request->ParameterData[0];
            $item = json_decode($request->item);
            $pages = isset($paramenter->Page) ? (int) $paramenter->Page : 1;
            $clinic = isset($request->clinic) ? $request->clinic : 'N';
            $row_data = isset($paramenter->ItemPerPage) ? (int) $paramenter->ItemPerPage : 20;
            $sorts = 'papers.paper_no';
            $order = isset($paramenter->ShortDesc) ? 'desc' : 'asc';

            $user_id = isset($request->UserName) ? (string) $request->UserName : '';
            $swabDate = isset($request->swabDate) ? (string) $request->swabDate : '';
            $search_item = isset($paramenter->FilterParams) ? (string) $paramenter->FilterParams : '';
            $search = isset($paramenter->FilterValue) ? (string) $paramenter->FilterValue : '';
            $type = isset($paramenter->FormType) ? (string) $paramenter->FormType : '';
            $offset = $pages;
            $status = isset($paramenter->FormStatus) ? (string) $paramenter->FormStatus : 'active';

            $user = User::where('username', $request->UserName)->first();
            Auth::login($user);

            if ($status == 'All') {
                $status = '';
            }
            // return response()->json($status);

            $result = [];
            $query = Paper::select(
                'papers.*',
                'B.name as paper_name',
                'B.alias',
            )
                ->leftJoin('master_papers as B', 'b.id', 'papers.master_paper_id')
                ->where('papers.deleted', '=', 'N')
                ->orderBY($sorts, $order);

            if ($clinic == 'Y') {
                $query = $query->whereIn('B.alias', ['srm', 'srk'])
                    ->where('papers.status', '=', 'active')
                    ->where('papers.swab_date', 'LIKE', '%' . $swabDate . '%');
            } else {
                $query = $query->where('B.alias', '=', $type)
                    ->where('papers.status', 'LIKE', '%' . $status . '%');
            }

            if ($type == 'stkpd') {
                if (!$user->hasAnyRole(['Superuser', 'HRD Jakarta'])) {
                    $query = $query->where("papers.user_id", '=', $user_id);
                }
            } else {
                if (!$user->hasAnyRole(['HRD Morowali', 'Superuser', 'Admin Klinik'])) {
                    if ($user->hasAnyRole(['Admin E-FORM'])) {
                        $user_by_departemen = ViewEmployee::where("Department", "=", $user->department)
                            ->pluck("Nik");
                        $query = $query->whereIn("papers.user_id", $user_by_departemen);
                    } else {
                        $query = $query->where("papers.user_id", '=', $user_id);
                    }
                }
            }

            // return $this->successApps($user->hasAnyRole(['HRD Morowali', 'Superuser', 'Admin Klinik']), '', 200, $request);

            if ($search_item == 'Employee') {
                $query = $query->where('papers.user_name', 'LIKE', '%' . $search . '%');
            } elseif ($search_item == 'Paper No') {
                $query = $query->where('papers.paper_no', 'LIKE', '%' . $search . '%');
            } elseif ($search_item == 'Created By') {
                $query = $query->where('papers.created_name', 'LIKE', '%' . $search . '%');
            }

            $result['total'] = $query->count();

            $all_data = $query->offset($offset)
                ->limit($row_data)
                ->get();

            $master_paper = $this->service->getMasterPaper($paramenter->FormType);
            $paper_no = (isset($paramenter->FormType))
                ? $this->service->generateDocNum(date('Y-m-d H:i:s'), $paramenter->FormType, $master_paper->id) : '';

            $result = array_merge($result, [
                'rows' => $all_data,
                'str_url' => Str::random(40),
                'detail' => [],
                'paper_no' => $paper_no,
                'swabDate' => $swabDate,
            ]);
            return $this->successApps($result, '', 200, $request);
            // return response()->json($result);
        } catch (\Exception $exception) {
            return $this->errorApps($exception->getMessage(), 200, [
                'trace' => $exception->getTrace()
            ], $request);
        }
    }

    public function getMasterForm(Request $request)
    {
        $country = Storage::disk('local')->get('Data/country.json');
        $collection = collect(json_decode($country, true));
        $plucked = $collection->pluck('name');
        $airport = Airport::all()->pluck('name');


        $document_status = Paper::select('status')->distinct()->get();
        $complete_status = Paper::select('is_complete')->distinct()->get();
        $default_form = Paper::where('id', '=', 1)->first();
        $filter_status = ['All'];
        $filter_complete = ['All'];

        foreach ($document_status as $value) {
            $filter_status[] = $value->status;
        }

        foreach ($complete_status as $value) {
            if ($value->is_complete == 'Y') {
                $filter_complete[] = 'finish';
            }

            if ($value->is_complete == 'N') {
                $filter_complete[] = 'pending';
            }
        }

        $company = ViewEmployee::distinct()
            ->pluck('Company');

        $resv_for = [
            'Own Purpose',
            'Subordinate',
            'Superior'
        ];

        $cost_cover = [
            'IMIP',
            'Guest Company',
            'Contractor'
        ];

        $travel_purpose = [
            'Duty Travel',
            'Family Visit/Yearly Leave',
            'Special Permit',
            'Others Purpose'
        ];

        $visit_area = [
            'Smelter',
            'Power Plan',
            'Factory',
            'Mine Site',
            'Harbour',
            'All Area'
        ];

        $facilities = [
            'Meeting Room with',
            'LCD Projector & Dispay Screen ',
            'Transport on site ',
            'Other',
        ];

        $result = [
            'filter' => ['Paper No', 'Employee', 'Created By'],
            'document_status' => $filter_status,
            'filter_complete' => $filter_complete,
            'form' => $default_form,
            'str_url' => Str::random(40),
            'airport' => $airport,
            'resv_for' => $resv_for,
            'travel_purpose' => $travel_purpose,
            'cost_cover' => $cost_cover,
            'country' => $plucked->all(),
            // 'detail' => [],
            'company' => $company,
            // 'paper_no' => $paper_no,
            'visit_area' => $visit_area,
            'facilities' => $facilities,
            // 'swabDate' => $swabDate,
        ];

        return $this->successApps($result, '', 200, $request);
    }

    /**
     * get reference no
     *
     * @param Request $request
     * @param $username
     *
     * @return JsonResponse
     */
    public function referenceNo(Request $request, $username)
    {
        $date_check = date('Y-m-d', strtotime('-40 days'));
        $reference_no = Paper::whereRaw("master_paper_id = (SELECT id from master_papers where alias='srm')")
            ->where('clinic_response', '=', 'Negatif')
            ->where('id_card', '=', $username)
            ->where('swab_date', '>=', $date_check)
            ->select('paper_no', 'user_name')
            ->get();
        return $this->successApps($reference_no, '', 200, $request);
        // return response()->json($reference_no);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $modelData = (object) $request->ModelData;
            $parameterData = (object) $request->ParameterData[0];
            if ($parameterData->FormType == 'stkpd') {
                $check_paper = $this->checkPaperBaseIdCardAlias($modelData, $parameterData);
                if ($check_paper) {
                    return $this->errorApps('Form Surat Ini Sudah Pernah Dibuatkan
                        Untuk Karyawan Yang Bersangkutan, Silahkan Cek Kembali!', 200, [], $request);
                }
            }

            if (empty($request->Token)) {
                return $this->errorApps('Token user cherry tidak boleh kosong!', 200, [], $request);
            }

            if (empty($modelData->form['paper_date'])) {
                return $this->errorApps('Tanggal Surat Tidak Boleh Kosong!', 200, [], $request);
            }

            if (empty($modelData->form['user_name'])) {
                return $this->errorApps('Karyawan Tidak Boleh Kosong!', 200, [], $request);
            }

            $alias = $parameterData->FormType;

            if ($alias == 'sim' || $alias == 'sik' || $alias == 'srm' || $alias == 'srk') {
                if (array_key_exists('work_location', $modelData->form)) {
                    if (str_contains($modelData->form['work_location'], 'MOROWALI')) {
                        if ($alias == 'sim' && $modelData->form['swab_type'] != '3') {
                            if (empty($modelData->form['reference_no'])) {
                                return $this->errorApps('Nomor Rapid tidak boleh kosong!', 200, [], $request);
                            }
                        }
                        if (empty($modelData->form['leave_from_to']) || empty($modelData->form['reference_number'])) {
                            return $this->errorApps('Tanggal Cuti dan Nomor Cuti tidak boleh kosong!', 200, [], $request);
                        }
                        $master_paper = $this->service->getMasterPaper($parameterData->FormType);
                        $check_paper = Paper::where('master_paper_id', '=', $master_paper->id)
                            ->where('reference_number', '=', $modelData->form['reference_number'])
                            ->where('user_id', '=', $request->UserName)
                            ->where('user_name', '=', $modelData->form['user_name'])
                            ->whereNotIn('status', ['canceled', 'rejected'])
                            ->count();

                        // $check_paper = Paper::where('master_paper_id', '=', $master_paper->id)
                        //     ->where('reference_number', '=', $modelData->form['reference_number'])
                        //     ->where('user_id', '=', $request->UserName)
                        //     ->whereNotIn('status', ['canceled', 'rejected'])
                        //     ->count();
                        if ($check_paper > 0) {
                            return $this->errorApps($master_paper->name . ' dengan Nomer cuti ' . $modelData->form['reference_number'] . ' Sudah ada, Silahkan pilih nomer yang lain!', 200, [], $request);
                        }
                    }
                }
            }

            if ($alias == 'sim') {
                if (date('Y-m-d', strtotime($modelData->form['date_in'])) < date('Y-m-d', strtotime($modelData->form['paper_date']))) {
                    return $this->errorApps('Tanggal masuk kawasan harus melebihi tanggal surat!', 200, [], $request);
                }

                if (!array_key_exists('swab_type', $modelData->form)) {
                    return $this->errorApps("Field swab type tidak boleh kosong!", 200, [], $request);
                }
            }

            if ($alias == 'sik') {
                if (date('Y-m-d', strtotime($modelData->form['date_out'])) < date('Y-m-d', strtotime($modelData->form['paper_date']))) {
                    return $this->errorApps('Tanggal keluar kawasan harus melebihi tanggal surat!', 200, [], $request);
                }
            }

            // if ($alias == 'srm') {
            //     if (empty($modelData->form['date_in'])) {
            //         return $this->errorApps('Tanggal masuk kawasan tidak boleh kosong!', 200, [], $request);
            //     }

            //     if (empty($modelData->form['transportation'])) {
            //         return $this->errorApps('Transportasi tidak boleh kosong!', 200, [], $request);
            //     }

            //     if (empty($modelData->form['route'])) {
            //         return $this->errorApps('Jalur tidak boleh kosong!', 200, [], $request);
            //     }

            //     if (empty($modelData->form['reason'])) {
            //         return $this->errorApps('Keperluan masuk kawasan tidak boleh kosong!', 200, [], $request);
            //     }
            // }

            if ($alias == 'srm' || $alias == 'srk' || $alias == 'rtm' || $alias == 'rtk') {
                if (empty($modelData->form['swab_date'])) {
                    return $this->errorApps('Tanggal Swab tidak boleh kosong!', 200, [], $request);
                }
                if (empty($modelData->form['name_boss'])) {
                    return $this->errorApps('Nama Atasan tidak boleh kosong!', 200, [], $request);
                }
                if (!empty($modelData->form['swab_date'])) {
                    // if (date('Y-m-d', strtotime($modelData->form['swab_date']))
                    //     < date('Y-m-d', strtotime(Carbon::now()))) {
                    //     return $this->errorApps('Tanggal Swab tidak boleh kurang dari tannggal sekarang!');
                    // }
                    if (
                        date('Y-m-d', strtotime($modelData->form['swab_date']))
                        <= date('Y-m-d', strtotime(Carbon::now()))
                    ) {
                        if (empty($modelData->form['reason_swab'])) {
                            return $this->errorApps('Alasan tanggal Swab tidak boleh kosong!', 403, [], $request);
                        }
                    }
                }
            }

            $paper = new Paper();
            $paper = $this->service->saveDataApi($paper, $modelData, 'post', $parameterData, $request);

            // if ($alias == 'srm') {
            //     $request->merge(['alias' => 'sim', 'form.status' => 'pending']);
            //     $data = $request->all();
            //     $data['form']['reference_no'] = $paper->paper_no;
            //     $data['form']['status'] = 'pending';
            //     // $modelData->form['status'] = 'pending';
            //     $paper_in = new Paper();
            //     $paper_in = $this->saveData($paper_in, (object)$data, 'post');
            // }

            $paper = Paper::leftJoin('master_papers', 'papers.master_paper_id', 'master_papers.id')
                ->select('papers.*', 'master_papers.name as paper_type', 'master_papers.alias as paper_alias')
                ->where('papers.id', '=', $paper->id)
                ->first();
            /**
             * Check if paper has attachment
             */
            $this->checkAttachment($paper);

            // if ($alias == 'srm') {
            //     $attachment = Attachment::where('source_id', '=', $paper->id)->count();
            //     if ($attachment == 0) {
            //         return $this->errorApps("Attachment tidak boleh kosong!");
            //     }
            // }

            // if ($alias == 'srm' || $alias == 'sim') {
            //     $attachment = Attachment::where('source_id', '=', $paper->id)->count();
            //     if ($attachment == 0) {
            //         return $this->errorApps("Attachment tidak boleh kosong!");
            //     }
            // }

            if ($alias == 'sim') {
                if ($modelData->form['swab_type'] != '3') {
                    $attachment = Attachment::where('source_id', '=', $paper->id)->count();
                    if ($attachment == 0) {
                        return $this->errorApps("Attachment tidak boleh kosong!", 200, [], $request);
                    }
                }
            }

            if (!Str::contains($paper->paper_alias, ['stkpd'])) {
                if ($request->details) {
                    foreach ($request->details as $detail) {
                        $dataDetail = new PaperDetails();
                        $this->service->saveDetails($detail, $dataDetail, 'create', $paper->id);
                    }
                }
                if (!empty($modelData->details)) {
                    foreach ($modelData->details as $detail) {
                        $dataDetail = new PaperDetails();
                        $this->service->saveDetails($detail, $dataDetail, 'create', $paper->id);
                    }
                }

                $user = User::where('username', $request->UserName)->first();
                $user->cherry_token = $request->Token;
                $user->save();


                Auth::login($user);

                if (!Str::contains($paper->paper_alias, ['ibk'])) {
                    $response_approval = $this->submitPaperApproval($paper, $request);

                    if ($response_approval['error']) {
                        return $this->errorApps($response_approval['message'], 200, [], $request);
                    } else {
                        DB::commit();
                        return $this->success([], $response_approval['message'], 200, $request);
                    }
                } else {
                    DB::commit();
                    return $this->success([], 'Data saved!');
                }
            } else {
                DB::commit();
                return $this->success([], 'Data saved!', 200, $request);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorApps($exception->getMessage(), 200, [
                'trace' => $exception->getTrace(),
            ], $request);
        }
    }

    /**
     * @param $request
     * @param $parameterData
     *
     * @return mixed
     */
    protected function checkPaperBaseIdCardAlias($request, $parameterData)
    {
        $master_paper = $this->getMasterPaper($parameterData->FormType);
        return Paper::where('id_card', '=', $request->form['id_card'])
            ->where('master_paper_id', '=', $master_paper->id)
            ->where('status', '<>', 'canceled')
            ->first();
    }


    /**
     * @param $paper
     */
    protected function checkAttachment($paper)
    {
        $attachment = Attachment::where('str_url', '=', $paper->str_url);
        $count = $attachment->count();
        if ($count > 0) {
            $attachment->update([
                'source_id' => $paper->id
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function show(Request $request, $id): JsonResponse
    {
        try {
            $paper = Paper::where('papers.id', '=', $id)
                ->select(
                    'papers.*',
                    'B.name as flight_origin',
                    'C.name as flight_destination',
                    'D.name as flight_origin_approve',
                    'E.name as flight_destination_approve'
                )
                ->leftJoin('airports as B', 'papers.flight_origin', 'B.id')
                ->leftJoin('airports as C', 'papers.flight_destination', 'C.id')
                ->leftJoin('airports as D', 'papers.flight_origin_approve', 'D.id')
                ->leftJoin('airports as E', 'papers.flight_destination_approve', 'E.id')
                ->first();

            $result = [
                'form' => $paper,
                'detail' => PaperDetails::where('paper_id', $id)->get()
            ];
            return $this->successApps($result, '', 200, $request);
            // return response()->json($result);
        } catch (\Exception $exception) {
            return $this->errorApps($exception->getMessage(), 200, [
                'trace' => $exception->getTrace()
            ], $request);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     *
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $modelData = (object) $request->ModelData;
            $parameterData = (object) $request->ParameterData[0];
            $paper = Paper::where('id', '=', $id)->first();
            $this->service->saveDataApi($paper, $modelData, 'update', $parameterData, $request);

            if ($modelData->details) {
                foreach ($modelData->details as $detail) {
                    $dataDetail = PaperDetails::where('id', '=', $detail['id'])->first();
                    $this->service->saveDetails($detail, $dataDetail, 'update', $paper->id);
                }
            }
            DB::commit();

            /**
             * check response dari klinik apakah karyawan FIT atau UNFIT
             * lalu kirim notifikasi email ke created surat
             */
            $this->service->sendEmail($modelData, $paper);

            /**
             * Cancel Dukumen
             */
            $this->service->cancelDocument($modelData);


            return $this->successApps([], 'Data Saved', 200, $request);
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorApps($exception->getMessage(), 200, [
                'trace' => $exception->getTrace(),
            ], $request);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        try {
            Paper::where('id', $id)->delete();

            return $this->successApps([], 'Form deleted', 200, $request);
        } catch (\Exception $exception) {
            return $this->errorApps($exception->getMessage(), 200, [
                'trace' => $exception->getTrace(),
            ], $request);
        }
    }

    /**
     * @param Request $request
     *
     * @return string
     *
     * @throws \PhpOffice\PhpWord\Exception\CopyFileException
     * @throws \PhpOffice\PhpWord\Exception\CreateTemporaryFileException
     * @throws \Exception
     */
    public function print(Request $request)
    {
        $paramenter = (object) $request->ParameterData[0];

        $paper = Paper::where('id', $paramenter->FormId)->first();
        try {

            if (!$paper) {
                return $this->errorApps('Form tidak ditemukan!', '404', [], $request);
            }
            $request->merge([
                'item' => [
                    'id' => $paramenter->FormId
                ],
                'type' => $paper->master->alias
            ]);
            $data = (object) $request->all();

            // return $this->errorApps('', 200, [$data->item], $request);
            $document = $this->service->printDocument($data, true);
            $base64 = chunk_split(base64_encode(file_get_contents($document['pdf_file_name'])));

            return $this->successApps([
                'base64' => $base64
            ], 'Download SUccess', 200, $request);
            // return response()->download($document['pdf_file_name'],
            // $document['file_export_name'] . '.pdf', $document['headers']);
            // return $this->success('Form deleted', 200, $request);
        } catch (\Exception $exception) {
            return $this->errorApps($exception->getMessage(), 200, [
                // 'paper' => $paper->master->alias,
                'trace' => $exception->getTrace(),
            ], $request);
        }
    }


    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function listAttachment(Request $request)
    {
        $paramenter = (object) $request->ParameterData[0];
        if (is_string($paramenter->FormId)) {
            $attachment = Attachment::where('str_url', '=', $paramenter->FormId);
        } else {
            $attachment = Attachment::where('source_id', '=', $paramenter->FormId);
        }

        return $this->successApps([
            'rows' => $attachment->where('type', 'eform')->get(),
            'total' => $attachment->where('type', 'eform')->count()
        ], '', 200, $request);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeAttachment(Request $request)
    {
        // $paper = Paper::find($request->FormId);
        // if (!$paper) {
        //     return $this->errorApps('Form tidak ditemukan!', '404', [], $request);
        // }
        $validator = Validator::make($request->all(), [
            'File.*' => 'required|mimes:pdf,docx,docx,png,jpg,jpeg|max:8048',
        ]);

        if ($validator->fails()) {
            return $this->errorApps($validator->errors(), 200, [], $request);
        }
        try {

            $user = User::where('username', $request->UserName)->first();

            $data_file = $request->file('File');

            $extension = $data_file->getClientOriginalExtension();

            // $destination_path = public_path('/Attachment/docs');

            // if (!file_exists($destination_path)) {
            //     if (!mkdir($destination_path, 0777, true) && !is_dir($destination_path)) {
            //         throw new \RuntimeException(
            //             sprintf(
            //                 'Directory "%s" was not created',
            //                 $destination_path
            //             )
            //         );
            //     }
            // }

            $origin_name = $data_file->getClientOriginalName();
            $name_no_ext = strtoupper(Str::slug(pathinfo($origin_name, PATHINFO_FILENAME))) . time();
            $file_name = $name_no_ext . '.' . $extension;

            $destination_path = custom_disk_path("/Attachment/docs", "sftp");
            $data_file->storeAs($destination_path, $file_name, 'sftp');
            // $data_file->move($destination_path, $file_name);

            $data = [
                'file_name' => $file_name,
                'file_path' => config('app.url') . '/Attachment/docs/' . $file_name,
                'source_id' => (int) $request->FormId,
                'str_url' => $request->FormId,
                'created_by' => $user->id,
                'type' => 'eform'
            ];

            $attach = Attachment::create($data);

            $count_attachment = Attachment::where('type', '=', 'eform')
                ->where('source_id', '=', (int) $request->FormId)
                ->count();

            return $this->successApps([
                'count' => $count_attachment
            ], 'Document Uploaded!', 200, $request);
        } catch (\Exception $exception) {
            return $this->errorApps($exception->getMessage(), 200, [
                'trace' => $exception->getTrace(),
            ], $request);
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyAttachment(Request $request)
    {
        try {
            $paramenter = (object) $request->ParameterData[0];
            $attachment = Attachment::where('id', '=', $paramenter->AttachmentId)
                ->first();

            $user = User::where('username', $request->UserName)->first();

            if ($attachment) {
                if ($attachment->created_by != $user->id) {
                    return $this->error('Not authorized to delete this file!');
                }

                $file = '/Attachment/docs/' . $attachment->file_name;
                custom_disk_delete($file);
                // unlink(public_path() . $file);
                Attachment::where('id', '=', $attachment->id)
                    ->delete();


                return $this->successApps('', 'File deleted!', 200, $request);
            } else {
                return $this->errorApps('File not found', '404', [], $request);
            }
        } catch (\Exception $exception) {
            return $this->errorApps($exception->getMessage(), 200, [
                'trace' => $exception->getTrace(),
            ], $request);
        }
    }
}
