<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\User\UserWorkLocation
 *
 * @property int $id
 * @property int $user_id
 * @property string $work_location
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkLocation whereWorkLocation($value)
 * @mixin \Eloquent
 */
class UserWorkLocation extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $connection = 'sqlsrv';
}
