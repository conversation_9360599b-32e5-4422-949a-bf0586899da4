###> docker configuration ###
WEB_PORT_HTTP=8001

APP_NAME="IMIP ERP"
APP_ENV=production
APP_KEY=base64:Qj8B6Qq12q8M3+dvIApBfxpvLz1aEuSQmJsYM9EVRa0=
APP_DEBUG=false
APP_URL=https://sbo2.imip.co.id:3001/backendcore

FRONT_URL=http://sbo2.imip.co.id:3001/eaccess

EPORTAL_URL="https://eportal.imip.co.id"
EPORTALAPI_URL="http://**************/backend"

CHERRY_TOKEN="http://***************/api/common/RequestAuthenticationToken"
CHERRY_REQ="http://***************/api/common/servicerequest"
CHERRY_CHECK_EMPLOYEE="http://***************/api/common/EmployeeSearch"

ELO_API_URL="http://**********:9090/rest-ELOIMIP/api"
ELO_USERNAME="administrator"
ELO_PASSWORD="Imip@2024"

ELASTIC_HOST=http://**********:9200
ELASTIC_LOGS_INDEX=backendcore_logs
ELASTIC_API_KEY=ZFNFV3lKRUJNeFFtNXU3TmRzMXc6aVM1MF9aV2lRdm1wYnVjQU9WTmgwUQ==
# LOG_CHANNEL=es-log
LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlsrv
DB_HOST=**************
DB_PORT=1433
DB_DATABASE=BC_DEV
DB_USERNAME=BCDEV_ADMIN
DB_PASSWORD="Le@86vRz"

DB_CONNECTION3=sqlsrv2
DB_HOST3=***************
DB_PORT3=1433
DB_DATABASE3=cherry_production
DB_USERNAME3=ck_admin
DB_PASSWORD3="ck_admin!@#"

DB_CONNECTION4=sqlsrv4
DB_HOST4=**************
DB_PORT4=1433
DB_DATABASE4=EKB_PRD
DB_USERNAME4=EKB_PRD
DB_PASSWORD4=3kbPRD@Imip

LARAVEL_ODBC_DSN="hanab1imipresv"
LARAVEL_ODBC_HOST=hanab1imipnew:30015
LARAVEL_ODBC_DATABASE=IMIP_ERESV_LIVE
LARAVEL_ODBC_DATABASE2="IMIP_LIVE"
LARAVEL_ODBC_USERNAME=IMIP_ERESV
LARAVEL_ODBC_PASSWORD="Ereserve#1234"
ODBC_URL="https://***************"

DB_SCHEMA="IMIP_ERESV_LIVE"
DB_SAP="IMIP_LIVE"
SERVICE_LAYER_USER=RESV
SERVICE_LAYER_PASSWORD="imip#1234"

SESSION_DOMAIN=localhost
SANCTUM_STATEFUL_DOMAINS=localhost

BROADCAST_DRIVER=ably
CACHE_DRIVER=redis
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=**********
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_CLIENT=predis


# MAIL_HOST=smtp.corp.imip.co.id
# MAIL_HOST=***************
# MAIL_HOST=***************
# MAIL_HOST=mail.imip.co.id

# MAIL_MAILER=smtp
# MAIL_HOST=webmail.imip.co.id
# MAIL_PORT=587
# MAIL_USERNAME="<EMAIL>"
# MAIL_PASSWORD="0kE$ds43Xpan!049"
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS="<EMAIL>"


MAIL_MAILER=smtp
MAIL_HOST="smtp.office365.com"
MAIL_PORT=587
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="M0r0wal!_@2022"
MAIL_ENCRYPTION="tls"
MAIL_FROM_ADDRESS="<EMAIL>"

# MAIL_MAILER = smtp
# MAIL_HOST = "smtp.office365.com"
# MAIL_PORT = 587
# MAIL_USERNAME = "<EMAIL>"
# MAIL_PASSWORD = "M0r0wal!_@2022"
# MAIL_ENCRYPTION = "tls"
# MAIL_FROM_ADDRESS = "<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=fMuyd4SfGzzeUFlTnJu8
AWS_SECRET_ACCESS_KEY=KPVcSW1djLO8E9vZhPjQZcpmap5rtRjb4Wc8ulUs
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET="backendcore"
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_ENDPOINT=http://**********:9000
AWS_URL=http://**********:9000


PUSHER_APP_ID=*******
PUSHER_APP_KEY=dad72f867dbbab83f5f5
PUSHER_APP_SECRET=5413f170d33b8d20d591
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


FTP_HOST="***************"
FTP_USERNAME="ftpuser"
FTP_PASSWORD="B@hodop!"

SFTP_HOST=**********
SFTP_USERNAME=eresv
SFTP_PASSWORD="eresv#2024"

SUPERAPPS_TOKEN="Y0hKdlpIVmpkR2x2Ymw5emRYQmxjbUZ3Y0hOZmJXbDBYM05sWTNKbGRGOXJaWGs9fGh0dHA6Ly9zYm8yLmltaXAuY28uaWQ6MzAwMC9iYWNrZW5kY29yZQ=="


TELEGRAM_LOGGER_BOT_TOKEN="**********************************************"
TELEGRAM_LOGGER_CHAT_ID="-4094119059"

NGINX_PORT=8101
PRODUCTION="1"
PHP_OPCACHE_ENABLE=1


ABLY_KEY="hk1mNQ.I8szVQ:H6mGIW2Z3YpQO3RgsuAGWuuuhmJm8fArws3IrdgzfHQ"