<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_companies', function (Blueprint $table) {
            $table->index(['user_id', 'company_id']);
        });
        Schema::table('user_divisions', function (Blueprint $table) {
            $table->index(['user_id', 'division_name']);
        });
        Schema::table('user_work_locations', function (Blueprint $table) {
            $table->index(['user_id', 'work_location', 'created_by', 'updated_by']);
        });
        Schema::table('user_whs', function (Blueprint $table) {
            $table->index(['user_id', 'db_code', 'whs_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('table', function (Blueprint $table) {
            //
        });
    }
};
