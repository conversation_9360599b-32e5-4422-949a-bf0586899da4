name: Backendcore-dev Deployment

on:
  push:
    branches: [dev]
  pull_request:
    types: [closed]
    branches: [dev]

jobs:
  deploy:
    # Only run the job when a PR is merged (not just when it's closed)
    if: github.event_name != 'pull_request' || github.event.pull_request.merged == true
    runs-on: [self-hosted, windows]

    steps:
      - name: Configure Git safe directories
        run: |
          git config --global --add safe.directory "D:/laragon-new/www/backendcore_dev"
        shell: cmd

      - name: Check if repository exists and update
        run: |
          echo "Start: %TIME%"
          if exist "D:\laragon-new\www\backendcore_dev\.git" (
            echo "Repository exists, checking for changes"
            cd D:\laragon-new\www\backendcore_dev
            git fetch origin dev
            
            rem Check if composer.json has changed without writing to a file
            git diff --name-only FETCH_HEAD HEAD | findstr /i "composer.json" > nul
            if not errorlevel 1 (
              echo "composer.json has changed, setting COMPOSER_CHANGED=true"
              echo "COMPOSER_CHANGED=true" >> %GITHUB_ENV%
            ) else (
              echo "composer.json has not changed"
              echo "COMPOSER_CHANGED=false" >> %GITHUB_ENV%
            )
            
            rem Now proceed with the pull
            git pull
          ) else (
            echo "Repository does not exist, cloning it"
            cd D:\laragon-new\www
            git clone https://github.com/itimip/backendcore-laravel.git backendcore_dev
            cd backendcore_dev
            git checkout dev
            echo "Fresh clone, setting COMPOSER_CHANGED=true"
            echo "COMPOSER_CHANGED=true" >> %GITHUB_ENV%
          )
          echo "End: %TIME%"
        shell: cmd

      # - name: Run Composer Install
      #   if: env.COMPOSER_CHANGED == 'true'
      #   run: |
      #     echo "Starting Composer Install: %TIME%"
      #     cd D:\laragon-new\www\backendcore_dev
      #     composer install
      #     echo "Composer Install Completed: %TIME%"
      #   shell: cmd

      # - name: Run Laravel Migration
      #   run: |
      #     echo "Starting Laravel Migration: %TIME%"
      #     cd D:\laragon-new\www\backendcore_dev
      #     php artisan migrate --force
      #     echo "Laravel Migration Completed: %TIME%"
      #   shell: cmd

      # - name: Run Laravel Optimalization
      #   run: |
      #     echo "Starting Laravel Optimalization: %TIME%"
      #     cd D:\laragon-new\www\backendcore_dev
      #     php artisan config:cache
      #     echo "Laravel Optimalization Completed: %TIME%"
      #   shell: cmd