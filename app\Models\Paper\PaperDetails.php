<?php

namespace App\Models\Paper;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Paper\PaperDetails
 *
 * @property int $id
 * @property string|null $name_title
 * @property string|null $name
 * @property string|null $position
 * @property float|null $body_weight
 * @property string|null $departing_city
 * @property string|null $arrival_date
 * @property string|null $arrival_flight_no
 * @property string|null $arrival_time
 * @property string|null $departure_date
 * @property string|null $departure_flight_no
 * @property string|null $departure_time
 * @property string|null $destination_city
 * @property string|null $transport_to
 * @property string|null $transport_from
 * @property string|null $notes
 * @property string|null $nationality
 * @property string|null $id_card
 * @property string|null $employee_type
 * @property string|null $company
 * @property string|null $seat_no
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $paper_id
 * @property string|null $status
 * @property string|null $room_no
 * @property string|null $alternative_room_no
 * @property string|null $national_id
 * @property string|null $phone_no
 * @property string|null $department
 * @property string|null $address
 * @property string|null $item_code
 * @property string|null $item_name
 * @property string|null $uom
 * @property string|null $qty
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails query()
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereAlternativeRoomNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereArrivalDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereArrivalFlightNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereArrivalTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereBodyWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereDepartingCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereDepartureDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereDepartureFlightNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereDepartureTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereDestinationCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereEmployeeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereIdCard($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereItemCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereItemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereNameTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereNationalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereNationality($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails wherePaperId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails wherePhoneNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereRoomNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereSeatNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereTransportFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereTransportTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereUom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PaperDetails whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class PaperDetails extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
}
