<?php

namespace App\Models\Task;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Task\TaskTagPeople
 *
 * @property int $user_id
 * @property int $task_id
 * @method static \Illuminate\Database\Eloquent\Builder|TaskTagPeople newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskTagPeople newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskTagPeople query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskTagPeople whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskTagPeople whereUserId($value)
 * @mixin \Eloquent
 */
class TaskTagPeople extends Model
{
    use HasFactory;

    public $timestamps = false;
    protected $table = 'task_tag_people';
    protected $guarded = [];
}
