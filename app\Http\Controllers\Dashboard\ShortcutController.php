<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ShortcutController extends Controller
{
    /**
     * Generate shortcut items based on user roles and applications.
     *
     * @param Request $request The incoming request data
     * @return \Illuminate\Http\JsonResponse The JSON response containing the generated shortcut items
     */
    public function index(Request $request)
    {
        $apps = $request->apps;
        $reservation = [
            [
                'name' => 'Create Reservation',
                'path' => '/reservation/form?status=add',
                'icon' => 'mdi-package-variant-plus',
                'color' => 'primary'
            ],
            [
                'name' => 'Reservation List',
                'path' => '/reservation/request',
                'icon' => 'mdi-list-box-outline',
                'color' => 'blue'
            ],
            [
                'name' => 'Request Item Code',
                'path' => '/reservation/requestitem',
                'icon' => 'mdi-shape-plus',
                'color' => 'info'
            ],
            [
                'name' => 'Report',
                'path' => '/report/list',
                'icon' => 'mdi-chart-box-outline',
                'color' => 'secondary'
            ],
        ];

        $itemSafety = [
            [
                'name' => 'Create Goods Issue',
                'path' => '/inventory/goodissue',
                'icon' => 'mdi-form-select',
                'color' => 'info'
            ],
            [
                'name' => 'List Goods Issue',
                'path' => '/inventory/viewgi',
                'icon' => 'mdi-cube-send',
                'color' => 'warning'
            ],
        ];

        $itemSign = [
            [
                'name' => 'E-Sign Invoice',
                'path' => '/document/esign',
                'icon' => 'mdi-file-sign',
                'color' => 'primary'
            ],
            [
                'name' => 'E-Sign Other',
                'path' => '/document/esign-other',
                'icon' => 'mdi-file-sign',
                'color' => 'success'
            ],
            [
                'name' => 'E-Sign Internal',
                'path' => '/document/internal',
                'icon' => 'mdi-file-sign',
                'color' => 'info'
            ],
            [
                'name' => 'Create Invoice',
                'path' => '/document/invoice',
                'icon' => 'mdi-invoice-plus-outline',
                'color' => 'warning'
            ]
        ];

        $item = [];

        if ($apps == 'E-RESERVATION') {
            if ($request->user()->hasAnyRole(['Superuser'])) {
                $item = array_merge($reservation, $itemSafety);
            } elseif ($request->user()->hasAnyRole(['Admin E-RESERVATION'])) {
                $item = $reservation;
            } elseif ($request->user()->hasAnyRole(['Admin E-RESEVATION SAFETY INVENTORY'])) {
                $item = array_merge($reservation, $itemSafety);
            }
        }

        if ($apps == 'E-METERAI') {
            if ($request->user()->hasAnyRole(['Superuser'])) {
                $item = array_merge($itemSign);
            } elseif ($request->user()->hasAnyRole(['Admin E-Meterai'])) {
                $item = $itemSign;
            }
        }
        return response()->json($item);
    }
}
