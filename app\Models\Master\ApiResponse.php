<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Master\ApiResponse
 *
 * @property int $id
 * @property string $type
 * @property string $message
 * @property string $url
 * @property string $params
 * @property string $response
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $reservation_number
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereParams($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereReservationNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiResponse whereUrl($value)
 * @mixin \Eloquent
 */
class ApiResponse extends Model
{
    use HasFactory;
    protected $guarded = [];

    protected $connection = 'sqlsrv';
}
