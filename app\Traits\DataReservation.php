<?php

namespace App\Traits;

trait DataReservation
{
    use ConnectHana;
    public function reservationHeaderByToken($token)
    {
        $db_name = $this->schemaDb();

        $sql = '
            SELECT T0."*"
            FROM ' . $db_name . '."RESV_H" AS T0
            WHERE T0."Token" = \''.$token.'\'
        ';

        $rs = odbc_exec($this->connectHana(), $sql);

        // return odbc_fetch_array( $rs );

        if (!$rs) {
            exit("Error in SQL");
        }
        $customer = [];

        while (odbc_fetch_row($rs)) {
            $customer = [
                "DocNum" => odbc_result($rs, "DocNum"),
                "DocDate" => odbc_result($rs, "DocDate"),
                "RequiredDate" => odbc_result($rs, "RequiredDate"),
                "Requester" => odbc_result($rs, "Requester"),
                "Division" => odbc_result($rs, "Division"),
                "Department" => odbc_result($rs, "Department"),
                "Company" => odbc_result($rs, "Company"),
                "Memo" => odbc_result($rs, "Memo"),
                "Canceled" => odbc_result($rs, "Canceled"),
                "DocStatus" => odbc_result($rs, "DocStatus"),
                "ApprovalStatus" => odbc_result($rs, "ApprovalStatus"),
                "ApprovalKey" => odbc_result($rs, "ApprovalKey"),
                "isConfirmed" => odbc_result($rs, "isConfirmed"),
                "ConfirmDate" => odbc_result($rs, "ConfirmDate"),
                "ConfirmBy" => odbc_result($rs, "ConfirmBy"),
                "CreateDate" => odbc_result($rs, "CreateDate"),
                "CreateTime" => odbc_result($rs, "CreateTime"),
                "CreatedBy" => odbc_result($rs, "CreatedBy"),
                "UpdateDate" => odbc_result($rs, "UpdateDate"),
                "UpdateTime" => odbc_result($rs, "UpdateTime"),
                "UpdatedBy" => odbc_result($rs, "UpdatedBy"),
                "U_DocEntry" => odbc_result($rs, "U_DocEntry"),
                "RequestType" => odbc_result($rs, "RequestType"),
                "U_NIK" => odbc_result($rs, "U_NIK"),
                "WhsCode" => odbc_result($rs, "WhsCode"),
                "WhTo" => odbc_result($rs, "WhTo"),
                "Token" => odbc_result($rs, "Token"),
                "CreatedName" => odbc_result($rs, "CreatedName"),
                "RequesterName" => odbc_result($rs, "RequesterName"),
                "UrgentReason" => odbc_result($rs, "UrgentReason"),
                "ItemType" => odbc_result($rs, "ItemType"),
            ];
        }

        return $customer;
    }
}
