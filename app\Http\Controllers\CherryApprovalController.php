<?php

namespace App\Http\Controllers;

use App\Models\Resv\ResvDetail;
use App\Models\Resv\ResvHeader;
use App\Models\User;
use App\Notifications\FailedProcessPrNotification;
use App\Services\CherryApprovalService;
use App\Services\ProcessPostSapS4Service;
use App\Services\SapS4Service;
use App\Traits\AppConfig;
use App\Traits\Approval;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\ApprovalEngineService;

class CherryApprovalController extends Controller
{
    use Approval;
    use AppConfig;

    public $service;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(CherryApprovalService $service)
    {
        $this->service = $service;
        // $this->middleware('guest');
    }

    /**
     * @param Request $request
     */
    public function callback(Request $request)
    {
        //        $token = $request->bearerToken();
        //        if ($token != config('app.access_token_1')) {
        //            return $this->error('Request not authorized!', 401);
        //        }
        // info("callback response", [
        //     "json" => json_encode($request->all())
        // ]);
        $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');

        if ($checkUseNewApproval == '1') {
            $document_id = $request->DocumentReferenceID ?? str_replace('ERESV', '', $request->ReferenceId);

            $header = ResvHeader::where('DocNum', '=', $document_id)->first();
            $approvalEngineService = new ApprovalEngineService();
            // $approvalList = $approvalEngineService->approvalList($header, "Id");
            $status = $request->StatusId ?? ($request->Status == "APPROVED" ? "Approved" : "Rejected");
            Log::info('process callback reservasi ' . $document_id . " Status : " . $status . " status from approval engine : " .  $request->Status  , []);
            // $status = $request->StatusId ?? $approvalList[0]["Status"] == "APPROVED" ? "Approved" : "Rejected";
        } else {
            $document_id = $request->DocumentReferenceID;
            $status = $request->StatusId;
        }

        DB::beginTransaction();
        try {

            if ($document_id) {
                $paper = DB::connection('sqlsrv')
                    ->table('papers')
                    ->where('paper_no', '=', $document_id)->first();

                $materai = DB::connection('sqlsrv')
                    ->table('documents')
                    ->where('document_number', '=', $document_id)->first();

                if ($status == 'Approved') {
                    if ($paper) {
                        // throw new \Exception('err', 1);
                        DB::connection('sqlsrv')
                            ->table('papers')
                            ->where('paper_no', '=', $document_id)
                            ->update([
                                'status' => 'active'
                            ]);
                    } elseif ($materai) {
                        DB::connection('sqlsrv')
                            ->table('documents')
                            ->where('document_number', '=', $document_id)
                            ->update([
                                'status' => 'approved'
                            ]);
                    } else {
                        $disableInput = $this->getConfigByName('DisableInput', 'GENERAL');

                        ResvHeader::where('DocNum', '=', $document_id)
                            ->update([
                                'ApprovalStatus' => 'Y',
                                'DocStatus' => 'O'
                            ]);


                        if ($disableInput == '1') {
                            return $this->error('System is under maintenance!!');
                        }


                        $dataHeader = ResvHeader::select(
                            "resv_headers.*",
                            "resv_headers.Company as CompanyName"
                        )
                            ->where("resv_headers.DocNum", "=", intval($document_id))
                            ->first();

                        if ($dataHeader) {
                            // Initialize service sap s4
                            $service = new SapS4Service();
                            $service->login();

                            // initialize post service
                            $postService = new ProcessPostSapS4Service();

                            // get document flow
                            $flow = $service->getFlow($dataHeader->DocNum);

                            $girNo = null;
                            $prNo = null;
                            $poNo = null;
                            $grNo = null;
                            $giNo = null;


                            if ($flow) {
                                // Log::info('callback flow', [
                                //     $flow
                                // ]);
                                // throw new \Exception(json_encode($flow));
                                if (array_key_exists('DATA', $flow)) {
                                    if ($flow['DATA'] != 'NULL') {
                                        $girNo = (array_key_exists('RESERVASI', $flow['DATA'][0])) ? $flow['DATA'][0]['RESERVASI'] : null;
                                        // $girNo =  null;
                                        $prNo = (array_key_exists("PR", $flow['DATA'][0])) ? $flow['DATA'][0]['PR'] : null;
                                    } else {
                                        // $girNo = $dataHeader->SAP_GIRNo;
                                        // $prNo = $dataHeader->SAP_PRNo;
                                    }
                                    // $poNo = $flow['DATA'][0]['PO'];
                                    // $grNo = $flow['DATA'][0]['GR'];
                                    // $giNo = $flow['DATA'][0]['GI'];
                                }
                            }

                            // Log::info('approve reservasi ' . $request->DocumentReferenceID, [
                            //     // 'document_id' => $request->DocumentReferenceID,
                            //     // 'status' => $request->StatusId,
                            //     // 'disableInput' => $disableInput,
                            //     // 'flow' => array_change_key_case($flow),
                            //     // 'prNo' => $prNo,
                            //     // 'girNo' => $girNo,
                            // ]);

                            $db_name = config('app.db_sap');

                            $dataDetails = ResvDetail::where("U_DocEntry", "=", $dataHeader->U_DocEntry)
                                ->get();
                            $check_db = DB::connection('sqlsrv')
                                ->table('resv_headers')
                                ->where('U_DocEntry', '=', $dataHeader->U_DocEntry)
                                ->first();

                            // if ($this->getConfigByName('TestingS4', 'SAPS4') == 'Y') {
                            //     $response = $postService->store($dataHeader, $dataDetails);
                            // }

                            // check if given data already exist in database
                            if (!empty($girNo) && $girNo && $dataHeader->DocNum) {
                                return response()->json([
                                    'result' => true,
                                    'message' => 'Data Saved Successfully!',
                                    'result_data_api' => 'GIR NO already added!',
                                    'GIR NO' => $girNo,
                                    'PR NO' => $prNo,
                                    'resv no' => $dataHeader->DocNum
                                ]);
                            } elseif (!empty($prNo)) {
                                return response()->json([
                                    'result' => true,
                                    'prNo' => $prNo,
                                    'message' => 'Data Saved Successfully!',
                                    'result_data_api' => 'PR NO already added!'
                                ]);
                            } else {
                                if ($check_db->SAP_SONo) {
                                    return response()->json([
                                        'result' => true,
                                        'message' => 'Data Saved Successfully!',
                                        'result_data_api' => 'SO NO already added!'
                                    ]);
                                } else {
                                    if ($check_db->DocumentType == 'Item') {
                                        if ($check_db->RequestType != 'Restock') {
                                            if ($check_db->RequestType == 'Sales' && $check_db->UsageFor == 'External') {
                                                $this->service->createSalesOrder($dataHeader, $dataDetails, $request);
                                            } else {
                                                // check category APD
                                                if (str($dataHeader->WorkLocation)->contains(['IMIP MOROWALI', 'BDT MOROWALI'])) {
                                                    if ($dataHeader->CategoryType == 'APD') {
                                                        foreach ($dataDetails as $detail) {
                                                            $itemCode = $detail->ItemCode;
                                                            $materials = $service->getMaterial(1, 20, $itemCode, $detail->ItemGroup, null, $detail->ItemCategory, $dataHeader->WhsCode);
                                                            if (array_key_exists("DATA", $materials)) {
                                                                $arr_count = [];
                                                                foreach ($materials['DATA'] as $item) {
                                                                    $arr_count[] = [
                                                                        "Available" => $item['LABST'],
                                                                    ];
                                                                }

                                                                if ($arr_count[0]['Available'] == 0 && $detail->RequestType == 'NPB') {
                                                                    $message = 'Stock untuk item ' . $detail->ItemName . ' kosong!';
                                                                    $result = false;

                                                                    throw new \Exception($message, 1);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                if (str($dataHeader->Requester)->contains(['88100102'])) {
                                                    if ($dataHeader->CategoryType == 'APD') {
                                                        foreach ($dataDetails as $detail) {
                                                            $itemCode = $detail->ItemCode;
                                                            $materials = $service->getMaterial(1, 20, $itemCode, $detail->ItemGroup, null, $detail->ItemCategory, $dataHeader->WhsCode);
                                                            if (array_key_exists("DATA", $materials)) {
                                                                $arr_count = [];
                                                                foreach ($materials['DATA'] as $item) {
                                                                    $arr_count[] = [
                                                                        "Available" => $item['LABST'],
                                                                    ];
                                                                }

                                                                if ($arr_count[0]['Available'] == 0 && $detail->RequestType == 'NPB') {
                                                                    $message = 'Stock untuk item ' . $detail->ItemName . ' kosong!';
                                                                    $result = false;

                                                                    throw new \Exception($message, 1);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                $response = $postService->store($dataHeader, $dataDetails);
                                                if ($response['error']) {
                                                    DB::commit();
                                                    $user = User::where("username", $dataHeader->CreatedBy)->first();
                                                    $user->notify(new FailedProcessPrNotification($dataHeader));
                                                    return response()->json([
                                                        'error' => true,
                                                        'url' => $response['url'],
                                                        'params' => $response['params'],
                                                        'response' => $response['response'],
                                                    ]);
                                                }

                                                if ($response['error'] == false) {
                                                    $user = User::where("username", $dataHeader->CreatedBy)->first();
                                                    if ($user->hasAnyRole(['E-RESERVATION Jakarta Item Access'])) {
                                                        if (!empty($response['response']['DATA_MESSAGE'][0]['NOPR'])) {
                                                            // Log::info('pr:send site' . $dataHeader->DocNum);
                                                            Artisan::call('pr:email', [
                                                                'docnum' => $dataHeader->DocNum,
                                                            ]);
                                                        }
                                                    }
                                                    if ($check_db->ItemType == 'Asset' && $check_db->WhsCode == 'IG03') {
                                                        // if (!empty($response['response']['DATA_MESSAGE'][0]['NOPR'])) {
                                                        // Log::info('pr:send site' . $dataHeader->DocNum);
                                                        Artisan::call('pr:email', [
                                                            'docnum' => $dataHeader->DocNum,
                                                        ]);
                                                        // }
                                                    }
                                                }
                                                // return $this->createGoodsIssueRequest($dataHeader, $dataDetails, $request);
                                            }
                                        } else {
                                            $response = $postService->store($dataHeader, $dataDetails);
                                            DB::commit();
                                        }
                                    } elseif ($check_db->DocumentType == 'Service') {
                                        $response = $postService->store($dataHeader, $dataDetails);

                                        if ($response['error']) {
                                            DB::commit();
                                            $user = User::where("username", $dataHeader->CreatedBy)->first();
                                            $user->notify(new FailedProcessPrNotification($dataHeader));
                                            return response()->json([
                                                'error' => true,
                                                'url' => $response['url'],
                                                'params' => $response['params'],
                                                'response' => $response['response'],
                                            ]);
                                        }

                                        // throw new \Exception(json_encode($pr), 1);
                                        if ($response['error'] == false) {
                                            DB::commit();
                                            // Log::info('pr:send ' . $dataHeader->DocNum);
                                            sleep(5);
                                            // Artisan::call('cache:clear');
                                            // Artisan::call('config:cache');
                                            if (!empty($response['response']['DATA_MESSAGE'][0]['NOPR'])) {
                                                Artisan::call('pr:email', [
                                                    'docnum' => $dataHeader->DocNum,
                                                ]);
                                            } else {
                                                $user = User::where("username", $dataHeader->CreatedBy)->first();
                                                $user->notify(new FailedProcessPrNotification($dataHeader));
                                            }
                                        }
                                        // return $pr;
                                    }
                                }

                                if ($check_db->RequestType != 'Restock') {
                                    $this->service->insertSafetyAndGaData($dataHeader, $dataDetails);
                                }
                            }
                        }

                        // dd($this->createGoodsIssueRequest($dataHeader, $dataDetails, $request));
                    }
                } elseif ($status == 'Rejected') {
                    if ($paper) {
                        DB::connection('sqlsrv')
                            ->table('papers')
                            ->where('paper_no', '=', $document_id)
                            ->update([
                                'status' => 'rejected'
                            ]);
                    } elseif ($materai) {
                        DB::connection('sqlsrv')
                            ->table('documents')
                            ->where('document_number', '=', $document_id)
                            ->update([
                                'status' => 'rejected'
                            ]);
                    } else {
                        Log::info('reject reservasi ' . $request->DocumentReferenceID . ", status : " . $request->StatusId);

                        $dataHeader = ResvHeader::select(
                            "resv_headers.*",
                            "resv_headers.Company as CompanyName",
                        )
                            ->where("resv_headers.DocNum", "=", intval($document_id))
                            ->first();

                        if ($dataHeader) {
                            $check_db = DB::connection('sqlsrv')
                                ->table('resv_headers')
                                ->where('U_DocEntry', '=', $dataHeader->U_DocEntry)
                                ->first();
                            if ($check_db->SAP_GIRNo) {
                                return response()->json([
                                    'result' => true,
                                    'message' => 'Data Saved Successfully!',
                                    'result_data_api' => 'GIR NO already added!'
                                ]);
                            } else {
                                DB::connection('sqlsrv')
                                    ->table('resv_headers')
                                    ->where('DocNum', '=', $document_id)
                                    ->update([
                                        'ApprovalStatus' => 'N'
                                    ]);
                            }
                        }
                    }
                }
                DB::commit();
                return response()->json([
                    'error' => false,
                    'message' => 'Document updated!'
                ]);
            } else {
                return response()->json([
                    'error' => true,
                    'message' => 'No Document Found!'
                ], 422);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::info('Error callback cheerry ' . $document_id . " message: " . (json_decode($exception->getMessage())) ?: $exception->getMessage());
            return response()->json([
                'error' => true,
                'message' => (json_decode($exception->getMessage())) ?: $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
                // 'trace' => $exception->getTrace()
            ], 422);
        }
    }
}
