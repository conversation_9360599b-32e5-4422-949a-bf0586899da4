<?php
namespace App\Models\View;

use App\Models\Master\Permission;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * App\Models\View\CheckItem
 *
 * @method static \Illuminate\Database\Eloquent\Builder|CheckItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CheckItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CheckItem query()
 * @mixin \Eloquent
 */
class CheckItem extends Model
{
    public $timestamps = false;
    public $incrementing = false;
    protected $connection = 'sqlsrv2';
    protected $table = 'VIEW_CHECK_ITEM';
    protected $primaryKey = 'LineEntry';
}
