<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\Inventory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON>\Hashids\Facades\Hashids;

class IssueReceiptController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $year_local = date('Y');
        $pages = isset($request->page) ? (int) $request->page : 1;
        $filter = isset($request->filter) ? (string) $request->filter : $year_local;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "created_at";
        $order = isset($request->sortBy[0]) ? $request->sortBy[0]['value'] : 'desc';
        $search = isset($request->search) ? strtolower((string) $request->search) : "";
        $search_status = isset($request->searchStatus) ? (string) $request->searchStatus : "";
        $select_data = isset($request->searchItem) ? (string) $request->searchItem : "DocNum";
        $offset = $pages;
        $user_id = $request->user()->username;

        $result = array();
        $query = Inventory::with(['lineItems'])
            ->when($search, function ($query) use ($select_data, $search) {
                $data_query = $query;
                switch ($select_data) {
                    case 'DocNum':
                        $data_query->whereRaw('"DocNum" LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Company':
                        $data_query->whereRaw('LOWER("Company") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Req Name':
                        $data_query->whereHas("lineItems", function ($query) use ($search) {
                            $query->whereHas("resvDetail", function ($query2) use ($search) {
                                $query2->where("EmployeeName", "LIKE", "%" . $search . '%');
                            });
                        });
                        // $data_query->whereRaw('LOWER("RequesterName") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Resv Number':
                        $data_query->whereHas("lineItems", function ($query) use ($search) {
                            $query->where("resv_number", "LIKE", "%" . $search . '%');
                        });
                        break;
                    case 'Req Type':
                        $data_query->whereRaw('LOWER("RequestType") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Req Date':
                        $data_query->whereRaw('LOWER("RequiredDate") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'App Status':
                        $data_query->whereRaw('LOWER("ApprovalStatus") LIKE( \'%' . $search . '%\') ');
                        break;
                    case 'Remark':
                        $data_query->whereRaw('LOWER("Memo") LIKE( \'%' . $search . '%\') ');
                        break;
                }

                return $data_query;
            });
        // ->when($search_status, function ($query) use ($search_status) {
        //     $data_query = $query;
        //     switch ($search_status) {
        //         case '-':
        //             $data_query->whereRaw('"ApprovalStatus" = \'-\' ');
        //             break;
        //         case 'Waiting':
        //             $data_query->whereRaw('"ApprovalStatus" = \'W\' ');
        //             break;
        //         case 'Approved':
        //             $data_query->whereRaw('"ApprovalStatus" = \'Y\' ');
        //             break;
        //         case 'Rejected':
        //             $data_query->whereRaw('"ApprovalStatus" = \'N\' ');
        //             break;
        //         case 'All':
        //             $data_query->whereRaw('"ApprovalStatus" LIKE \'%%\' ');
        //             break;
        //     }
        //     return $data_query;
        // });

        $result["total"] = $query->count();

        $all_data = $query->orderBY($sorts, $order)
            ->paginate($row_data)
            ->items();

        $result['rows'] = $all_data;


        $filter = [
            "DocNum",
            "Req Name",
            "Resv Number",
        ];


        $result = array_merge($result, [
            "filter" => $filter,
        ]);

        return response()->json($result);
    }


    public function show(Request $request, string $hash_id): JsonResponse
    {
        try {
            $type = $request->type;
            if ($hash_id != 'undefined' && $hash_id != 'null') {
                $id = Hashids::decode($hash_id)[0];
            } else {
                $id = Inventory::orderBy('id', 'asc')->first()->id;
            }
            // throw new \Exception($id);

            if ($type == 'next') {
                $id++;
            } else {
                $id = $id - 1;
            }

            $header = Inventory::where('id', $id)->first();
            if ($header) {
                return $this->success([
                    'header' => $header,
                    'lineItems' => $header->lineItems
                ]);
            }
            throw new \App\Exceptions\CustomException('Data not found');
        } catch (\Exception $e) {
            throw new \App\Exceptions\CustomException($e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        $status = $request->status;
        $header = Inventory::where('id', $id)->first();

        switch ($status) {
            case 'Cancel':
                $header->status = 'C';
                break;
            case 'Close':
                $header->status = 'Y';
                break;
        }

        $header->save();
    }
}
