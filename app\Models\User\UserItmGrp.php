<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\User\UserItmGrp
 *
 * @property int $id
 * @property string $item_group
 * @property string $item_group_name
 * @property string $db_code
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp whereDbCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp whereItemGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp whereItemGroupName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserItmGrp whereUserId($value)
 * @mixin \Eloquent
 */
class UserItmGrp extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $connection = 'sqlsrv';
}
