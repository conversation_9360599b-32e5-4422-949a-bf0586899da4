<?php

namespace App\Models\Inventory;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use Vinkla\Hashids\Facades\Hashids;

/**
 * App\Models\Inventory\Inventory
 *
 * @property int $id
 * @property int $doc_number
 * @property int $created_by
 * @property int|null $updated_by
 * @property string $post_date
 * @property string $notes
 * @property string $status
 * @property string $whs_code
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $doc_type
 * @property string $print_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read User|null $createdUser
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Inventory\InventoryDetail> $lineItems
 * @property-read int|null $line_items_count
 * @property-read User|null $updatedUser
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory query()
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereDocNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereDocType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory wherePostDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory wherePrintAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereWhsCode($value)
 * @property string|null $department
 * @property string $prefix
 * @property string $suffix
 * @property-read User|null $creator
 * @property-read mixed $created_name
 * @property-read mixed $doc_num
 * @property-read mixed $hash_id
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory wherePrefix($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Inventory whereSuffix($value)
 * @mixin \Eloquent
 */
class Inventory extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'doc_num',
        'created_name',
        'status',
        'hash_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'post_date' => 'date:Y-m-d',
    ];

    public function getHashIdAttribute()
    {
        return Hashids::encode($this->attributes['id']);
    }

    public function getDocNumAttribute()
    {
        return $this->attributes['prefix']
            . sprintf('%05d', $this->attributes['doc_number'])
            . $this->attributes['suffix'];
    }

    public function getCreatedNameAttribute()
    {
        return ($this->createdUser) ? $this->createdUser->name : null;
    }

    public function getStatusAttribute()
    {
        switch ($this->attributes['status']) {
            case 'O':
                return 'Open';
            case 'Y':
                return 'Closed';
            case 'C':
                return 'Canceled';
            default:
                return 'Draft';
        }
    }

    public function lineItems()
    {
        return $this->hasMany(InventoryDetail::class, 'header_id', 'id');
    }

    public function createdUser()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function updatedUser()
    {
        return $this->belongsTo(User::class, 'updated_by', 'id');
    }
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
}
