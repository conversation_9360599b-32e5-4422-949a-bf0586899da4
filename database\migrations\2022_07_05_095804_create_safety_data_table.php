<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSafetyDataTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Schema::connection('laravelOdbc')->create('safety_data', function (Blueprint $table) {
        //     $table->id();
        //     $table->date('date_out')->nullable();
        //     $table->string('item_code', 20)->nullable();
        //     $table->string('uom', 20)->nullable();
        //     $table->string('item_name')->nullable();
        //     $table->string('id_card', 20)->nullable();
        //     $table->string('employee_name', 200)->nullable();
        //     $table->string('company', 200)->nullable();
        //     $table->string('department', 200)->nullable();
        //     $table->string('notes', 200)->nullable();
        //     $table->decimal('qty', 10, 2)->nullable();
        //     $table->timestamps();
        // });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('safety_data');
    }
}
