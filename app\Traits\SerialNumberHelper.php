<?php

namespace App\Traits;

use App\Events\Documents\BatchProcessEvent;
use App\Models\Common\SignLogging;
use Carbon\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait SerialNumberHelper
{
    use ApiResponse;

    /**
     * Summary of storeSerialNumber
     * @param mixed $image
     * @param mixed $serial
     * @param mixed $fileName
     * @param mixed $document
     * @throws \Exception
     * @return void
     */
    public function storeSerialNumber($image, $serial, $fileName, $document)
    {
        $image = str_replace('data:image/png;base64,', '', $image);
        $image = str_replace(' ', '+', $image);
        $imageName = $serial . '.' . 'png';
        $qrImagePath = '/images/stamp/' . $imageName;
        // put stamp file to local server
        custom_disk_put($qrImagePath, base64_decode($image));
        // File::put(public_path($qrImagePath), base64_decode($image));
        create_file_delete_job('/Attachment/docs/' . $fileName->file_name);
        $pdf_file = public_path('/Attachment/docs/' . $fileName->file_name);


        // store stamp and unsigned file to shared server
        $stamp = '/STAMP/' . $imageName;
        custom_disk_put($stamp, custom_disk_get($qrImagePath), 'ftp');
        // Storage::disk('ftp')->put($stamp, File::get(public_path($qrImagePath)));

        if (!custom_disk_check($stamp, 'ftp')) {
            custom_disk_put($stamp, custom_disk_get($qrImagePath), 'ftp');
        }
        // if (!Storage::disk('ftp')->exists($stamp)) {
        //     Storage::disk('ftp')->put($stamp, File::get(public_path($qrImagePath)));
        // }
        // pdf file
        // $doc = '/UNSIGNED/' . basename($fileName->file_name, '.pdf') . '_' . $serial . '.pdf';
        $doc = '/UNSIGNED/' . basename($fileName->file_name, '.pdf') . '.pdf';
        custom_disk_put($doc, custom_disk_get($pdf_file), 'ftp');
        // Storage::disk('ftp')->put($doc, File::get($pdf_file));

        if (!custom_disk_check($doc, 'ftp')) {
            custom_disk_put($doc, custom_disk_get($pdf_file), 'ftp');
        }
        // if (!Storage::disk('ftp')->exists($doc)) {
        //     Storage::disk('ftp')->put($doc, File::get($pdf_file));
        // }
        if (!custom_disk_check($doc, 'ftp')) {
            throw new \Exception('E-METERAI: ' . $doc . ' file not exist', 1);
        }
        // if (!Storage::disk('ftp')->exists($doc)) {
        //     throw new \Exception('E-METERAI: ' . $doc . ' file not exist', 1);
        // }
        if (!custom_disk_check($stamp, 'ftp')) {
            throw new \Exception('E-METERAI: ' . $stamp . ' file not exist', 1);
        }
        // if (!Storage::disk('ftp')->exists($stamp)) {
        //     throw new \Exception('E-METERAI: ' . $stamp . ' file not exist', 1);
        // }

        // event(new BatchProcessEvent($document->created_by, $document->id));
    }
    /**
     * Summary of generateQrImage
     * @param mixed $token
     * @param mixed $document
     * @param mixed $dataLog
     * @param mixed $dataCheck
     * @param mixed $fileName
     * @throws \Exception
     * @return void
     */
    public function generateQrImage($token, $document, $dataLog, $dataCheck, $fileName, $batch, $serialTable, $snColumn)
    {
        $url = $this->getConfigByName('GenerateQrImage');
        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withToken($token)
            ->get($url . '?serialnumber=' . $serialTable->$snColumn . '&onprem=true');

        if ($response->failed()) {
            $mergeResponse = array_merge($dataLog, [
                'log_type' => 'request',
                'request_type' => 'QR_IMAGE',
                'payload_request' => $url . '?serialnumber=' . $serialTable->$snColumn . '&onprem=true',
                'payload_response' => $response->body(),
                'response_code' => null,
                'response_time' => Carbon::now(),
                'status' => 'FAILED',
                'serial_number' =>  $serialTable->$snColumn,
            ]);
            SignLogging::create($mergeResponse);

            $dataUpdate = [
                'batch_id' => $batch->id,
                'status' => 'Processing ' . $batch->progress() . '%',
                'callback_message' => 'E-METERAI: ',
                'callback_trace' => $response
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            throw new \Exception('E-METERAI: ' . $response['result']['err'], 1);
        }
        $serial = $serialTable->$snColumn;

        Log::info('from serial number helper, Serial Number in update qr code', [
            'serial' => $serialTable->$snColumn,
        ]);

        if (!array_key_exists('base64', $response->collect()['result'])) {
            throw new \Exception($response->body(), 1);
            // throw new \Exception('E-METERAI: ' . implode(" ", $response->body()) , 1);
        }

        $image = $response->collect()['result']['base64'];

        $dataUpdate = [
            'batch_id' => $batch->id,
            'status' => 'Processing ' . $batch->progress() . '%',
            'callback_message' => 'Success ',
            'callback_trace' => 'Success Generate Serial Number!'
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);

        $this->storeSerialNumber($image, $serial, $fileName, $document);
    }

    /**
     * Summary of submitSerialNumber
     * @param mixed $token
     * @param mixed $url
     * @param mixed $params
     * @param mixed $dataLog
     * @param mixed $dataCheck
     * @param mixed $document
     * @param mixed $snColumn
     * @param mixed $fileName
     * @throws \Exception
     * @return void
     */
    public function submitSerialNumber($token, $url, $params, $dataLog, $dataCheck, $document, $snColumn, $fileName, $batch, $serialTable)
    {
        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            // ->withOptions(["verify" => false])
            ->withToken($token)
            ->post($url, $params);

        if ($response->failed()) {
            $this->failedSubmitSerialNumber($response, $dataLog, $params, $dataCheck, $batch);
        }

        $response = $response->collect();

        if ($response['message'] == 'success') {
            $this->successSubmitSerialNumber($document, $response, $snColumn, $dataLog, $params, $batch, $dataCheck, $fileName, $serialTable);
        } else {
            $dataUpdate = [
                'batch_id' => $batch->id,
                'status' => 'Processing ' . $batch->progress() . '%',
                'callback_message' => 'E-METERAI: ' . $response['result']['err'],
                'callback_trace' => $response
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            throw new \Exception('E-METERAI: ' . $response['result']['err'], 1);
        }
    }
    /**
     * Summary of successSubmitSerialNumber
     * @param mixed $document
     * @param mixed $response
     * @param mixed $snColumn
     * @param mixed $dataLog
     * @param mixed $params
     * @param mixed $batch
     * @param mixed $dataCheck
     * @param mixed $fileName
     * @return void
     */
    public function successSubmitSerialNumber($document, $response, $snColumn, $dataLog, $params, $batch, $dataCheck, $fileName, $serialTable)
    {
        $this->createLog(
            $document->id,
            'document',
            'E-meterai - Generate Serial Number: ' . $response['result']['sn'],
            $this->userId
        );

        // $document = Document::find($document->id);
        $serialTable->$snColumn = $response['result']['sn'];
        $serialTable->save();

        Log::info('from serial number helper, Serial Number in sucess serial number', [
            'serial' => $response['result']['sn'],
            'serial_from_table' => $serialTable->$snColumn,
            // 'serial_table' => $serialTable,
        ]);


        $serial = $serialTable->$snColumn;
        $image = $response['result']['image'];

        $mergeResponse = array_merge($dataLog, [
            'log_type' => 'callback',
            'request_type' => 'SN',
            'payload_request' => json_encode($params),
            'payload_response' => $response['message'],
            // 'response_code' => $response['statusCode'],
            'response_code' => '',
            'response_time' => Carbon::now(),
            'status' => 'NOT_STAMP',
            'serial_number' => $response['result']['sn'],
        ]);
        SignLogging::create($mergeResponse);

        $dataUpdate = [
            'batch_id' => $batch->id,
            'status' => 'Processing ' . $batch->progress() . '%',
            'callback_message' => 'Success ',
            'callback_trace' => 'Success Generate Serial Number!'
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);

        $this->storeSerialNumber($image, $serial, $fileName, $document);
    }
    /**
     * Summary of failedSubmitSerialNumber
     * @param mixed $response
     * @param mixed $dataLog
     * @param mixed $params
     * @param mixed $dataCheck
     * @throws \Exception
     * @return never
     */
    public function failedSubmitSerialNumber($response, $dataLog, $params, $dataCheck, $batch)
    {
        $res = $response->collect();
        $mergeResponse = array_merge($dataLog, [
            'log_type' => 'request',
            'request_type' => 'SN',
            'payload_request' => json_encode($params),
            'payload_response' => $response->body(),
            //'response_code' => $res['statusCode'],
            'response_code' => '',
            'response_time' => Carbon::now(),
            'status' => 'FAILED',
            'serial_number' => (isset($res['result']['sn'])) ? $res['result']['sn'] : null,
        ]);
        SignLogging::create($mergeResponse);

        Log::info('from serial number helpder, failed serial number', [
            'serial' => $response->body(),
        ]);

        $dataUpdate = [
            'batch_id' => $batch->id,
            'status' => 'Processing ' . $batch->progress() . '%',
            'callback_message' => 'E-METERAI: ',
            'callback_trace' => $response
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        throw new \Exception('E-METERAI: ' . $response['result']['err'], 1);
    }

    /**
     * Summary of updateDataSerial
     * @param mixed $dataSerial
     * @param mixed $fileName
     * @param mixed $document
     * @param mixed $token
     * @param mixed $dataLog
     * @param mixed $dataCheck
     * @param mixed $snColumn
     * @return mixed
     */
    public function updateDataSerial($dataSerial, $fileName, $document, $token, $dataLog, $dataCheck, $snColumn, $batch, $serialTable)
    {
        $urlUpdateStamp = $this->getConfigByName('UpdateDataStamp') . $dataSerial->serial_number;
        $params = [
            'idfile' => $fileName->id,
            'namadoc' => $document->type_no,
            'namafile' => $fileName->file_name,
            'nilaidoc' => $document->value,
            'namejidentitas' => $document->identity_type,
            'noidentitas' => $document->identity_number,
            'namedipungut' => $document->identity_name,
            'snOnly' => false,
            'nodoc' => $document->document_number,
            'tgldoc' => $document->document_date,
        ];
        $response = Http::withoutVerifying()
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->post($urlUpdateStamp, $params);

        Log::error('Failed Generate Serial Number', [
            'error' => $response->collect(),
        ]);

        if ($response->failed()) {
            $this->failedUpdateStamp($dataLog, $params, $response, $dataSerial, $dataCheck, $batch);
            return false;
        } else {
            $this->successUpdateDataStamp($document, $token, $dataLog, $dataCheck, $snColumn, $fileName, $batch, $serialTable);
        }
    }
    /**
     * Summary of failedUpdateStamp
     * @param mixed $dataLog
     * @param mixed $params
     * @param mixed $response
     * @param mixed $dataSerial
     * @param mixed $dataCheck
     * @throws \Exception
     * @return never
     */
    public function failedUpdateStamp($dataLog, $params, $response, $dataSerial, $dataCheck, $batch)
    {
        $mergeResponse = array_merge($dataLog, [
            'log_type' => 'request',
            'request_type' => "UPDATE SN",
            'payload_request' => json_encode($params),
            'payload_response' => $response->body(),
            //'response_code' => $res['statusCode'],
            'response_code' => '',
            'response_time' => Carbon::now(),
            'status' => 'FAILED',
            'serial_number' => $dataSerial->serial_number,
        ]);
        SignLogging::create($mergeResponse);

        $dataSerial->is_used = 'no';
        $dataSerial->save();

        $dataUpdate = [
            'name' => 'Generate Serial Number',
            'batch_id' => $batch->id,
            'status' => 'Processing ' . $batch->progress() . ' %',
            'callback_message' => 'E-METERAI: ',
            'callback_trace' => $response->body()
        ];
        $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        throw new \Exception($response->body(), 1);
    }
    /**
     * Summary of successUpdateDataStamp
     * @param mixed $document
     * @param mixed $token
     * @param mixed $dataLog
     * @param mixed $dataCheck
     * @param mixed $snColumn
     * @throws \Exception
     * @return mixed
     */
    public function successUpdateDataStamp($document, $token, $dataLog, $dataCheck, $snColumn, $fileName, $batch, $serialTable)
    {
        $serial = $serialTable->$snColumn;
        $imageName = $serial . '.' . 'png';
        $stamp = '/STAMP/' . $imageName;
        // throw new \Exception($file_name, 1);

        $url = $this->getConfigByName('GenerateQrImage');
        $response = Http::withoutVerifying()
            ->withToken($token)
            ->get($url . '?serialnumber=' . $serialTable->$snColumn . '&onprem=true');

        if ($response->failed()) {
            $mergeResponse = array_merge($dataLog, [
                'log_type' => 'request',
                'request_type' => 'QR_IMAGE',
                'payload_request' => $url . '?serialnumber=' . $serialTable->$snColumn . '&onprem=true',
                'payload_response' => $response->body(),
                'response_code' => null,
                'response_time' => Carbon::now(),
                'status' => 'FAILED',
                'serial_number' => $serialTable->$snColumn,
            ]);
            SignLogging::create($mergeResponse);
            $dataUpdate = [
                'name' => 'Generate Serial Number',
                'batch_id' => $batch->id,
                'status' => 'Processing ' . $batch->progress() . ' %',
                'callback_message' => 'E-METERAI: ',
                'callback_trace' => $response->body()
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            throw new \Exception(json_encode($response->body()), 1);
        }
        // throw new \Exception('E-METERAI: ' . json_encode($response['result']), 1);
        // throw new \Exception(json_encode($response->collect()), 1);
        if (is_array($response->collect()['result'])) {
            // code...
            $image = $response->collect()['result']['base64'];

            Log::info('from serial number helper, Serial Number in update stamp', [
                'serial' => $serialTable->$snColumn,
            ]);

            $this->storeSerialNumber($image, $serial, $fileName, $document);
        } else {
            throw new \Exception(json_encode($response->body()), 1);
        }
    }
}
