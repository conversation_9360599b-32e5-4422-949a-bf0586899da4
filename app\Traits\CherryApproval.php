<?php

namespace App\Traits;

use App\Models\Common\Attachment;
use App\Models\Common\Vehicle;
use App\Models\Paper\Paper;
use App\Models\Resv\ResvHeader;
use App\Models\Master\ResvSapUsage;
use App\Models\User;
use App\Models\View\ViewEmployee;
use App\Models\View\ViewApprovalStage;
use App\Services\ApprovalEngineService;
use App\Services\ReservationDataService;
use App\Services\ReservationValidateDataService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

trait CherryApproval
{
    use AppConfig;
    /**
     * @param $header
     * @param $details
     * @param $request
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function submitApproval($header, $details, $request, $approvalType = 'e-reservation')
    {
        $form = $header;
        $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');
        $checkApproval = ViewApprovalStage::where('DocumentReferenceID', $form->DocNum)->first();
        // if ($checkApproval) {
        //     ResvHeader::where("DocNum", $form->DocNum)->update([
        //         "ApprovalStatus" => "W"
        //     ]);
        //     throw new \RuntimeException("Document already submitted to cherry");
        //     // $this->success([], "Document already submitted to cherry");
        // }

        // if ($form->DocNum == '240817529') {
        //     throw new \RuntimeException("Document already submitted to cherry");
        // }

        // return response()->json(config('app.cherry_service_req'), 422);
        if ($checkUseNewApproval == '1') {
            $approvalEngineService = new ApprovalEngineService();
            $list_code = $approvalEngineService->getDocumentType();
            $authUser = User::where('id', Auth::user()->id)->first();
            $username = ($authUser->hasAnyRole(['Superuser'])) ? $header->CreatedBy : Auth::user()->username;
            $createdUser = User::where('username', $username)->first();
        } else {
            $cherry_token = Auth::user()->cherry_token;
            $authUser = User::where('id', Auth::user()->id)->first();
            $username = ($authUser->hasAnyRole(['Superuser'])) ? $header->CreatedBy : Auth::user()->username;
            $createdUser = User::where('username', $username)->first();
            $cherry_token = ($authUser->hasAnyRole(['Superuser'])) ? $createdUser->cherry_token : $cherry_token;
            // throw new \Exception(config('app.cherry_service_req'), 1);
            $list_code = Http::withoutVerifying()
                ->timeout(90)
                ->retry(3, 100)
                ->withOptions(["verify" => false])
                ->post(config('app.cherry_service_req'), [
                    'CommandName' => 'GetList',
                    'ModelCode' => 'ExternalDocuments',
                    'UserName' => $username,
                    'Token' => $cherry_token,
                    'ParameterData' => []
                ]);

            if ($list_code->failed()) {
                throw new \Exception($list_code->body(), 1);
            }

            if ($list_code->collect()['MessageType'] == 'error') {
                throw new \Exception($list_code->collect()['Message'], 1);
            }
        }



        // return $this->error('', '422', [$list_code->collect()['Data']]);

        $item_groups = [];
        $item_groups_bdm = [];
        $count_item_it = 0;
        $count_item_it_bdm = 0;
        $count_sub_group_network = 0;
        $count_sub_group_other = 0;
        $is_request_it = false;
        $is_request_it_bdm = false;

        // if (auth()->user()->username == '88101989') {


        // throw new \Exception(json_encode($details), 1);

        if ($approvalType == 'e-reservation') {
            if (Str::contains($header->WorkLocation, ['BDM MOROWALI'])) {
                if ($form->WhsCode == 'BG02') {
                    foreach ($details as $index => $items) {
                        $item_groups_bdm[] = $items['ItemGroup'];
                        if ($items['ItemGroup'] == 'ZITS') {
                            $count_item_it_bdm++;
                        }
                    }
                }
                if ($count_item_it_bdm > 0) {
                    if ($count_item_it_bdm == count($item_groups_bdm)) {
                        $is_request_it_bdm = true;
                    }
                }
            }



            if ($form->RequestType != 'Restock') {
                foreach ($details as $index => $items) {
                    $item_groups[] = $items['ItemGroup'];
                    if ($items['ItemGroup'] == 'ZITS') {
                        $count_item_it++;
                    }

                    if ($items['ItemGroup'] == 'ZAST' && Str::contains($items['SubGroup'], ['89112'])) {
                        $count_item_it++;
                    }

                    if (Str::contains($items['SubGroup'], ['89112'])) {
                        // $count_sub_group_network++;
                        $count_sub_group_other++;
                    }

                    if ($items['SubGroup'] == '66103') {
                        $count_sub_group_network++;
                    } else {
                        $count_sub_group_other++;
                    }
                }

                if ($count_item_it > 0) {
                    if ($count_item_it == count($item_groups)) {
                        $is_request_it = true;
                    }
                }
            }

            $count_item_ga = 0;
            $item_groups_ga = [];

            $item_check_npb = [];
            $count_item_npb = 0;
            $count_item_spb = 0;

            $item_groups_safety = [];
            $count_item_safety = 0;
            $count_item_seragam = 0;

            $item_groups_ga_in_safety = [];
            $count_item_ga_in_safety = 0;

            $item_group_bdm = [];
            $count_item_bdm = 0;

            $validateService = new ReservationValidateDataService();
            foreach ($details as $index => $items) {
                $item_groups_ga[] = $items['ItemGroup'];
                if ($items['SubGroup'] == '66201') {
                    $count_item_ga++;
                }

                $item_group_bdm[] = $items['ItemGroup'];
                if (array_key_exists('SubGroup', $items)) {
                    if (str($items['SubGroup'])->contains($validateService->itemGroupSapBDM() && str($form->Company)->contains(["BDM", "BDW"]))) {
                        $count_item_bdm++;
                    }
                }

                if ($items['SubGroup'] == '66202' && $items["ItemCategory"] == 'RS') {
                    $count_item_ga++;
                }

                // valiate item GA yang ada di item group safety
                $item_groups_ga_in_safety[] = $items['U_AppResBy'];
                if ($items['U_AppResBy'] == 'GA') {
                    $count_item_ga_in_safety++;
                }

                // validate item safety
                $item_groups_safety[] = $items['U_AppResBy'];
                if ($items['U_AppResBy'] == 'HSE' || $header->CategoryType == 'APD') {
                    $count_item_safety++;
                }

                if ($items['SubGroup'] == '67104' && $items["ItemCategory"] == 'RS') {
                    $count_item_seragam++;
                }

                //check npb or spb
                if ($items['NPB'] == 'Y') {
                    $count_item_npb++;
                }

                if ($items['SPB'] == 'Y') {
                    $count_item_spb++;
                }
            }
            $is_request_ga = false;
            if ($count_item_ga > 0) {
                if ($count_item_ga == count($item_groups_ga)) {
                    $is_request_ga = true;
                }
            }

            $is_request_item_bdm = false;
            if ($count_item_bdm > 0) {
                if ($count_item_bdm == count($item_group_bdm)) {
                    $is_request_item_bdm = true;
                }
            }

            $is_request_ga_seragam = false;
            if ($count_item_seragam > 0) {
                if ($count_item_seragam == count($item_groups_ga)) {
                    $is_request_ga_seragam = true;
                }
            }

            $is_request_ga_in_safety = false;
            if ($count_item_ga_in_safety > 0) {
                if ($count_item_ga_in_safety == count($item_groups_ga_in_safety)) {
                    $is_request_ga_in_safety = true;
                }
            }

            $is_request_safety = false;
            if ($count_item_safety > 0) {
                if ($form->CategoryType == 'APD') {
                    $is_request_safety = true;
                }
                // if ($count_item_safety == count($item_groups_safety)) {
                //     $is_request_safety = true;
                // }
            }

            // info("check safety", [
            //     "is_request_safety" => $is_request_safety,
            //     "count_item_safety" => $count_item_safety,
            // ]);

            // throw new \Exception($count_item_safety, 1);

            $is_carpool = false;
            $vehicle = Vehicle::where('vehicle_no', '=', $form->VehicleNo)->first();
            if ($vehicle) {
                if ($vehicle->is_carpool == 'Yes') {
                    $is_carpool = true;
                }
            }
        }



        // return $this->error($form->RequestType);
        // return $this->error('', '422', [$list_code->collect()['Data']]);
        $reservation_code = null;
        // if ($form->Requester == '88104271') {
        //     $reservation_code = $this->checkExternalDocument(collect($list_code->collect()['Data']), $form, $details, $is_request_safety);
        //     // return $this->error($reservation_code);
        // } else
        if ($approvalType == 'e-meterai') {
            $reservation_code = null;
            foreach ($list_code->collect()['Data'] as $datum) {
                if ($datum['Name'] == 'TESTING E-SIGN') {
                    $reservation_code = $datum['Code'];
                    break;
                }
            }
        } elseif ($form->DocumentType == 'Service') {
            $reservation_code = null;
            foreach ($list_code->collect()['Data'] as $datum) {
                if ($count_item_spb > 0 && $datum['Name'] == 'E-RESV SPB JAKARTA') {
                    $reservation_code = $datum['Code'];
                    break;
                }

                if ($count_item_npb > 0 && $datum['Name'] == 'E-RESV NPB JAKARTA') {
                    $reservation_code = $datum['Code'];
                    break;
                }
            }
            // return $this->error($reservation_code);`
        } elseif ($authUser->hasAnyRole(['Admin E-RESERVATION JETTY BDM IMIP'])) {
            foreach ($list_code->collect()['Data'] as $datum) {
                if ($form->Company == 'IMIP_LIVE') {
                    // if ($is_request_item_bdm && $count_item_spb > 0 && $datum['Name'] == 'E-RESV SPB BDM ITEM GROUP') {

                    //     $reservation_code = $datum['Code'];
                    //     break;
                    // } elseif ($is_request_item_bdm && $count_item_npb > 0 && $datum['Name'] == 'E-RESV NPB BDM ITEM GROUP') {
                    //     $reservation_code = $datum['Code'];
                    //     break;
                    // } else
                    if ($count_item_spb > 0 && $datum['Name'] == 'E-RESV SPB IMIP') {

                        $reservation_code = $datum['Code'];
                        break;
                    } elseif ($count_item_npb > 0 && $datum['Name'] == 'E-RESV NPB IMIP') {
                        $reservation_code = $datum['Code'];
                        break;
                    }
                } elseif ($form->Company == 'BDM_LIVE') {
                    // if ($is_request_item_bdm && $count_item_spb > 0 && $datum['Name'] == 'E-RESV SPB BDM ITEM GROUP') {

                    //     $reservation_code = $datum['Code'];
                    //     break;
                    // } elseif ($is_request_item_bdm && $count_item_npb > 0 && $datum['Name'] == 'E-RESV NPB BDM ITEM GROUP') {
                    //     $reservation_code = $datum['Code'];
                    //     break;
                    // } else
                    if ($count_item_spb > 0 && $datum['Name'] == 'E-RESV SPB BDM') {

                        $reservation_code = $datum['Code'];
                        break;
                    } elseif ($count_item_npb > 0 && $datum['Name'] == 'E-RESV NPB BDM') {

                        $reservation_code = $datum['Code'];
                        break;
                    }
                } else if ($form->Company == 'BDW_LIVE') {
                    // if ($is_request_item_bdm && $count_item_spb > 0 && $datum['Name'] == 'E-RESV SPB BDM ITEM GROUP') {

                    //     $reservation_code = $datum['Code'];
                    //     break;
                    // } elseif ($is_request_item_bdm && $count_item_npb > 0 && $datum['Name'] == 'E-RESV NPB BDM ITEM GROUP') {
                    //     $reservation_code = $datum['Code'];
                    //     break;
                    // } else
                    if ($count_item_spb > 0 && $datum['Name'] == 'E-RESV SPB BDM') {

                        $reservation_code = $datum['Code'];
                        break;
                    } elseif ($count_item_npb > 0 && $datum['Name'] == 'E-RESV NPB BDM') {

                        $reservation_code = $datum['Code'];
                        break;
                    }
                }
            }
        } else {
            $check_true = [];
            // Log::info('check request: ' . $header->DocNum, [
            //     'is_request_ga' => $is_request_ga,
            //     'count_item_npb' => $count_item_npb,
            //     'is_request_safety' => $is_request_safety,
            //     'is_request_it' => $is_request_it,
            //     'count_item_spb' => $count_item_spb,
            //     'is_request_ga_in_safety' => $is_request_ga_in_safety,
            //     'docNum' => $header->DocNum,
            //     'workLocation' => $header->WorkLocation,
            //     'check SPB JKT FUEL' => ($form->RequestType == 'Restock' && $count_item_spb > 0 && Str::contains($header->WorkLocation, ['IMIP JAKARTA', 'BDM JAKARTA']))
            // ]);
            foreach ($list_code->collect()['Data'] as $datum) {
                // if (
                //     $is_request_item_bdm &&
                //     $is_request_it_bdm &&
                //     str($form->ItemType)->contains(['Non Ready Stock', 'Asset', 'Service']) &&
                //     $count_item_spb > 0 &&
                //     $datum['Name'] == 'E-RESV SPB ITEM GROUP ASSET SERVICE BDM'
                // ) {

                //     $reservation_code = $datum['Code'];
                //     break;
                // } else
                if (
                    $is_request_it_bdm &&
                    str($form->ItemType)->contains(['Non Ready Stock', 'Asset', 'Service']) &&
                    $count_item_spb > 0 &&
                    $datum['Name'] == 'E-RESV SPB ASSET SERVICE IT BDM'
                ) {

                    $reservation_code = $datum['Code'];
                    break;
                }
                // elseif (
                //     $is_request_item_bdm &&
                //     $is_request_it_bdm &&
                //     str($form->ItemType)->contains(['Non Ready Stock', 'Asset', 'Service']) &&
                //     $count_item_npb > 0 &&
                //     $datum['Name'] == 'E-RESV NPB ITEM GROUP ASSET SERVICE BDM'
                // ) {

                //     $reservation_code = $datum['Code'];
                //     break;
                // }
                elseif (
                    $is_request_it_bdm &&
                    str($form->ItemType)->contains(['Non Ready Stock', 'Asset', 'Service']) &&
                    $count_item_npb > 0 &&
                    $datum['Name'] == 'E-RESV NPB ASSET SERVICE IT BDM'
                ) {

                    $reservation_code = $datum['Code'];
                    break;
                }
                // elseif (
                //     $is_request_item_bdm &&
                //     $form->RequestType == 'Restock' &&
                //     Str::contains($header->WorkLocation, ['BDM MOROWALI', 'BDW MOROWALI']) &&
                //     $form->ItemType == 'Ready Stock' &&
                //     $datum['Name'] == 'E-RESV NPB BDM ITEM GROUP'
                // ) {

                //     $reservation_code = $datum['Code'];
                //     break;
                // }
                elseif (
                    $form->RequestType == 'Restock' &&
                    Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'BDM MOROWALI', 'BDW MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI']) &&
                    $form->ItemType == 'Ready Stock' &&
                    $datum['Name'] == 'E-RESV SPB RESTOCK'
                ) {

                    $reservation_code = $datum['Code'];
                    break;
                } elseif (
                    $form->RequestType == 'Restock' &&
                    $count_item_spb > 0 &&
                    Str::contains($header->WorkLocation, ['IMIP JAKARTA', 'BDM JAKARTA']) &&
                    $datum['Name'] == 'E-RESV NPB JAKARTA'
                ) {

                    $reservation_code = $datum['Code'];
                    break;
                } elseif (
                    $form->RequestType == 'Restock' &&
                    $count_item_npb > 0 &&
                    Str::contains($header->WorkLocation, ['IMIP JAKARTA', 'BDM JAKARTA']) &&
                    $datum['Name'] == 'E-RESV NPB JAKARTA'
                ) {

                    $reservation_code = $datum['Code'];
                    break;
                }
                // elseif $is_carpool && $datum['Name'] == 'NPB PERTALITE') {
                //     $reservation_code = $datum['Code'];
                // }
                // elseif (
                //     $form->CategoryType == 'Fuel' &&
                //     $vehicle &&
                //     $vehicle->is_carpool == 'Yes' &&
                //     Str::contains($header->WorkLocation, ['BDM MOROWALI', 'BDW MOROWALI']) &&
                //     $is_request_item_bdm &&
                //     $form->ItemType == 'Ready Stock' && $datum['Name'] == 'E-RESV NPB BDM ITEM GROUP'
                // ) {

                //     $reservation_code = $datum['Code'];
                //     break;
                // }
                elseif (
                    $form->CategoryType == 'Fuel' &&
                    $vehicle &&
                    $vehicle->is_carpool == 'Yes' &&
                    Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'BDM MOROWALI', 'BDW MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI']) &&
                    $form->ItemType == 'Ready Stock' && $datum['Name'] == 'NPB PERTALITE'
                ) {

                    $reservation_code = $datum['Code'];
                    break;
                }
                //  elseif($datum['Name'] == 'E-RESV NPB JAKARTA') {
                //     $reservation_code = $datum['Code'];
                // }
                else {
                    if ($is_request_it && Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI'])) {
                        // if ($form->Requester == '88101468') {
                        //     if ($datum['Name'] == 'SPB IT REQUEST ADMIN' && ($form->ItemType == 'Non Ready Stock' || $form->ItemType == 'Asset' || $form->ItemType == 'Service')) {
                        //         $reservation_code = $datum['Code'];
                        //     } elseif ($datum['Name'] == 'NPB IT REQUEST ADMIN' && $form->ItemType == 'Ready Stock') {
                        //         $reservation_code = $datum['Code'];
                        //     }
                        // } else {
                        // }
                        if ($count_sub_group_network > 0) {
                            if ($count_item_spb > 0 && $form->ItemType == 'Asset' && $datum['Name'] == 'E-RESV ASSET') {
                                $reservation_code = $datum['Code'];
                                break;
                            } elseif ($datum['Name'] == 'SPB IT REQUEST NETWORK' && $count_item_spb > 0) {
                                $reservation_code = $datum['Code'];
                            } elseif ($datum['Name'] == 'NPB IT REQUEST NETWORK' && $count_item_npb > 0 && $is_request_it == true) {
                                $reservation_code = $datum['Code'];
                                break;
                            }
                        } elseif ($count_sub_group_other > 0) {
                            if ($count_item_spb > 0 && $form->ItemType == 'Asset' && $datum['Name'] == 'E-RESV ASSET') {
                                $reservation_code = $datum['Code'];
                                break;
                            } elseif ($datum['Name'] == 'SPB IT REQUEST HARDWARE' && $count_item_spb > 0) {
                                $reservation_code = $datum['Code'];
                                break;
                            } elseif ($datum['Name'] == 'NPB IT REQUEST HARDWARE' && $count_item_npb > 0 && $is_request_it == true) {
                                $reservation_code = $datum['Code'];
                                break;
                            }
                        }
                    } else {
                        if ($is_request_ga_in_safety && Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI'])) {
                            if ($datum['Name'] == 'E-RESV PENGAMBILAN APD' && $is_request_ga_seragam) {
                                $reservation_code = $datum['Code'];
                                break;
                            }

                            if ($datum['Name'] == 'SPB GA (Office Supplies)' && $count_item_spb > 0 && $form->ItemType == 'Asset' && $datum['Name'] == 'E-RESV ASSET') {
                                $reservation_code = $datum['Code'];
                                break;
                            }

                            if ($datum['Name'] == 'SPB GA (Office Supplies)' && $count_item_spb > 0) {
                                $reservation_code = $datum['Code'];
                                break;
                            }

                            if ($datum['Name'] == 'NPB GA (Office Supplies)' && $count_item_npb > 0) {
                                $reservation_code = $datum['Code'];
                                break;
                            }
                        } elseif (
                            ($is_request_ga || $is_request_ga_in_safety) &&
                            Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI'])
                        ) {

                            if ($datum['Name'] == 'SPB GA (Office Supplies)' && $count_item_spb > 0 && $form->ItemType == 'Asset' && $datum['Name'] == 'E-RESV ASSET') {
                                $reservation_code = $datum['Code'];
                                break;
                            }

                            if ($datum['Name'] == 'SPB GA (Office Supplies)' && $count_item_spb > 0) {
                                $reservation_code = $datum['Code'];
                                break;
                            }

                            if ($datum['Name'] == 'NPB GA (Office Supplies)' && $count_item_npb > 0) {
                                $reservation_code = $datum['Code'];
                                break;
                            }
                        } elseif (
                            $is_request_safety &&
                            Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI']) &&
                            $datum['Name'] == 'E-RESV SPB SAFETY' &&
                            $count_item_spb > 0
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        } elseif (
                            $is_request_safety &&
                            $authUser->hasAnyRole(["Admin Safety Req New Employee"]) &&
                            $header->EmployeeType == "New Employee" &&
                            Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI']) &&
                            $datum['Name'] == 'E-RESV SAFETY NEW EMPLOYEE' &&
                            $count_item_npb > 0
                        ) {
                            $reservation_code = $datum['Code'];
                            break;
                        } elseif (
                            $is_request_safety &&
                            Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI']) &&
                            $datum['Name'] == 'E-RESV NPB SAFETY' &&
                            $count_item_npb > 0
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        } elseif (
                            $is_request_safety &&
                            // Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI']) &&
                            $datum['Name'] == 'E-RESV NPB SAFETY' &&
                            str($form->Requester)->contains(['88100102']) &&
                            $count_item_npb > 0
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        } elseif (
                            $count_item_spb > 0 &&
                            Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'SMI MOROWALI', 'BMS MOROWALI']) &&
                            $form->ItemType == 'Asset' &&
                            !str($form->Department)->contains(['IT - ']) &&
                            $datum['Name'] == 'E-RESV ASSET'
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        }
                        // elseif (
                        //     $is_request_item_bdm &&
                        //     $count_item_spb > 0 &&
                        //     Str::contains($header->WorkLocation, ['BDM MOROWALI']) &&
                        //     $datum['Name'] == 'E-RESV SPB BDM ITEM GROUP'
                        // ) {

                        //     $reservation_code = $datum['Code'];
                        //     break;
                        // }
                        elseif (
                            $count_item_spb > 0 &&
                            Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'BDM MOROWALI', 'BMS MOROWALI']) &&
                            $datum['Name'] == 'E-RESERVATION SPB'
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        }
                        // elseif (
                        //     $is_request_item_bdm &&
                        //     $count_item_npb > 0 &&
                        //     $form->CategoryType != 'Fuel' &&
                        //     Str::contains($header->WorkLocation, ['BDM MOROWALI']) &&
                        //     $datum['Name'] == 'E-RESV NPB BDM ITEM GROUP'
                        // ) {

                        //     $reservation_code = $datum['Code'];
                        // }
                        elseif (
                            $count_item_npb > 0 &&
                            $form->CategoryType != 'Fuel' &&
                            !$is_request_safety &&
                            Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'BDM MOROWALI', 'BMS MOROWALI']) &&
                            $datum['Name'] == 'E-RESERVATION NPB'
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        }
                        // elseif (
                        //     $is_request_item_bdm &&
                        //     $count_item_npb > 0 &&
                        //     $form->CategoryType == 'Fuel' &&
                        //     $vehicle->is_carpool == 'No' &&
                        //     Str::contains($header->WorkLocation, ['BDM MOROWALI']) &&
                        //     $datum['Name'] == 'E-RESV NPB BDM ITEM GROUP'
                        // ) {

                        //     $reservation_code = $datum['Code'];
                        // }
                        elseif (
                            $count_item_npb > 0 &&
                            !$is_request_safety &&
                            $form->CategoryType == 'Fuel' &&
                            $vehicle->is_carpool == 'No' &&
                            Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI', 'SECURITY MOROWALI', 'BDM MOROWALI', 'BMS MOROWALI']) &&
                            $datum['Name'] == 'E-RESERVATION NPB'
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        } elseif (
                            $count_item_npb > 0 &&
                            Str::contains($header->WorkLocation, ['IMIP JAKARTA', 'BDM JAKARTA']) &&
                            !str($form->Requester)->contains(['88100102']) &&
                            $datum['Name'] == 'E-RESV NPB JAKARTA'
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        } elseif (
                            $count_item_spb > 0 &&
                            Str::contains($header->WorkLocation, ['IMIP JAKARTA', 'BDM JAKARTA']) &&
                            !str($form->Requester)->contains(['88100102']) &&
                            $datum['Name'] == 'E-RESV SPB JAKARTA'
                        ) {

                            $reservation_code = $datum['Code'];
                            break;
                        }


                        // {


                        // if ($datum['Name'] == 'E-RESERVATION NPB' && $form->ItemType == 'Ready Stock') {
                        //     $reservation_code = $datum['Code'];
                        // } elseif ($datum['Name'] == 'E-RESERVATION SPB' && ($form->ItemType == 'Non Ready Stock' || $form->ItemType == 'Asset' || $form->ItemType == 'Service')) {
                        //     $reservation_code = $datum['Code'];
                        // }
                        // }
                    }

                    // $check_true[] = ($datum['Name'] == 'SPB IT REQUEST ADMIN' && ($form->ItemType == 'Non Ready Stock' || $form->ItemType == 'Asset' || $form->ItemType == 'Service') && $form->Requester == '88101468');
                }


                // return response()->json(, 422);

                // if ($datum['Name'] == 'E-RESERVATION URGENT' && $form->RequestType == 'Urgent') {
                //     $reservation_code = $datum['Code'];
                // } elseif ($datum['Name'] == 'E-RESERVATION NORMAL') {
                //     $reservation_code = $datum['Code'];
            }
        }
        // return $this->error($form->ItemType);
        // return $this->error($reservation_code);

        if (empty($reservation_code)) {
            throw new \Exception('External Document Code cannot empty!', 1);
        }

        if (!$reservation_code) {
            return $this->error('External Document Code cannot empty!', '422');
        }

        if ($approvalType == 'e-reservation') {
            $username = ($authUser->hasAnyRole(['Superuser'])) ? $header->CreatedBy : Auth::user()->username;
            $company_code = ($authUser->hasAnyRole(['Superuser'])) ? $createdUser->company_code : Auth::user()->company_code;

            $employee = ViewEmployee::where('Nik', '=', $form->Requester)->first();

            $employee_code = $employee->EmployeeCode;
            $company_code = $employee->CompanyCode;

            // throw new \Exception(Auth::user()->employee_code, 1);

            $employee_code = (empty($employee_code)) ? Auth::user()->employee_code : $employee_code;

            if ($form->CategoryType == 'APD') {
                // if (!Str::contains($form->Department, ['EXTERNAL - COMDEV', 'EXTERNAL - COMREL', 'EXTERNAL - MEDIA RELATIONSHIP'])) {
                if (!Str::contains($form->Department, ['EXTERNAL - COMREL', 'EXTERNAL - MEDIA RELATIONSHIP'])) {
                    // $employee_code =  Auth::user()->employee_code;
                }
            }

            $movementType = ResvSapUsage::where('movement_type', $header->Usage)->first();

            // else if ($form->WhsCode == 'IG03' || $form->WhsCode == 'MW-ZZ') {
            // else if ($form->WhsCode == 'MW-IT' || $form->WhsCode == 'MW-ZZ') {

            //     $employee_code =  Auth::user()->employee_code;
            // }

            $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');

            $document_content = view('email.approval_resv', [
                'details' => $details,
                'movementType' => $movementType,
                'header' => $header,
                'newApproval' => $checkUseNewApproval
            ])->render();

            // $document_content = \Illuminate\Mail\Markdown::parse(
            //     view('email.approval_resv_markdown', [
            //         'details' => $details,
            //         'movementType' => $movementType,
            //         'header' => $header,
            //         'newApproval' => $checkUseNewApproval,
            //         'countOrder' => collect($details)->where('OrderId', '!=', null)->count()
            //     ])->render()
            // );
        } else {
            $document_content = view('email.approval_esign', [
                'details' => $details,
                'document' => $header,
            ])->render();
        }



        if ($checkUseNewApproval == '1') {
            $approvalEngineService = new ApprovalEngineService();
            $response = $approvalEngineService->submitApproval(
                $header,
                $reservation_code,
                $document_content,
                $approvalType
            );
        } else {
            // if (auth()->user()->username == '88101989') {
            //     Log::info('Params submit cherry, DocNum: ' . $form->DocNum, [
            //         'CommandName' => 'Submit',
            //         'ModelCode' => 'GADocuments',
            //         'UserName' => $username,
            //         'Token' => $cherry_token,
            //         'ParameterData' => [],
            //         'ModelData' => [
            //             'TypeCode' => $reservation_code,
            //             'CompanyCode' => $company_code,
            //             'Date' => date('m/d/Y'),
            //             'EmployeeCode' => $employee_code,
            //             'DocumentReferenceID' => $form->DocNum,
            //             'CallBackAccessToken' => config('app.access_token_1'),
            //             'Notes' => $form->Memo,
            //             'is_request_safety' => $is_request_safety,
            //             'is_request_it' => $is_request_it,
            //             'count_item_spb' => $count_item_spb,
            //             'count_item_npb' => $count_item_npb,
            //             'WorkLocation' => $header->WorkLocation,
            //             'ItemType' => $form->ItemType,
            //             // 'CheckRole' => ($authUser->hasAnyRole(['Admin E-RESERVATION JETTY BDM IMIP']))
            //         ]
            //     ]);

            //     throw new \Exception("error");
            // }
            // return response()->json($document_content);
            $response = Http::withoutVerifying()
                ->timeout(90)
                ->retry(3, 100)
                ->withOptions(["verify" => false])
                ->post(config('app.cherry_service_req'), [
                    'CommandName' => 'Submit',
                    'ModelCode' => 'GADocuments',
                    'UserName' => $username,
                    'Token' => $cherry_token,
                    'ParameterData' => [],
                    'ModelData' => [
                        'TypeCode' => $reservation_code,
                        'CompanyCode' => $company_code,
                        'Date' => date('m/d/Y'),
                        'EmployeeCode' => $employee_code,
                        'DocumentReferenceID' => $form->DocNum,
                        'CallBackAccessToken' => config('app.access_token_1'),
                        'DocumentContent' => $document_content,
                        'Notes' => $form->Memo
                    ]
                ]);

            Log::info('Params submit cherry, DocNum: ' . $form->DocNum, [
                'CommandName' => 'Submit',
                'ModelCode' => 'GADocuments',
                'UserName' => $username,
                'Token' => $cherry_token,
                'ParameterData' => [],
                'ModelData' => [
                    'TypeCode' => $reservation_code,
                    'CompanyCode' => $company_code,
                    'Date' => date('m/d/Y'),
                    'EmployeeCode' => $employee_code,
                    'DocumentReferenceID' => $form->DocNum,
                    'CallBackAccessToken' => config('app.access_token_1'),
                    'Notes' => $form->Memo,
                    'is_request_safety' => $is_request_safety,
                    'is_request_it' => $is_request_it,
                    'count_item_spb' => $count_item_spb,
                    'count_item_npb' => $count_item_npb,
                    'WorkLocation' => $header->WorkLocation,
                    'ItemType' => $form->ItemType,
                    // 'CheckRole' => ($authUser->hasAnyRole(['Admin E-RESERVATION JETTY BDM IMIP']))
                ]
            ]);

            if ($response->failed()) {
                throw new \Exception($response->body(), 1);
            }
            // return response()->json($response->collect(), 422);

            if ($response['MessageType'] == 'error') {
                $docNum = $form->DocNum;
                $error = "Document Number $docNum";
                $checkApproval = ViewApprovalStage::where('DocumentReferenceID', $form->DocNum)->first();
                Log::info("Error submit cherry: " . $response->collect()['Message'], [
                    'check_error_message' => Str::contains($response->collect()['Message'], [$error]),
                    'message' => $error,
                    'message_cherry' => $response->collect()['Message'],
                    'checkApprovalId' => ($checkApproval) ? $checkApproval->DocumentReferenceID : null
                ]);
                // if (str($response->collect()['Message'])->contains("Document Number $docNum already exist!  Code: 2012")) {
                if (Str::contains($response->collect()['Message'], [$error])) {
                    if ($checkApproval) {
                        ResvHeader::where("DocNum", $form->DocNum)->update([
                            "ApprovalStatus" => "W"
                        ]);
                        // throw new \RuntimeException("Document already submitted to cherry");
                        return $this->success([
                            "U_DocEntry" => $form->U_DocEntry
                        ], ($form->U_DocEntry != 'null' ? "Data updated!" : "Data inserted!"));
                    } else {
                        ResvHeader::where("DocNum", $form->DocNum)->update([
                            "ApprovalStatus" => "W"
                        ]);
                        // throw new \RuntimeException("Document already submitted to cherry");
                        return $this->success([
                            "U_DocEntry" => $form->U_DocEntry
                        ], ($form->U_DocEntry != 'null' ? "Data updated!" : "Data inserted!"));
                    }
                } else {
                    throw new \Exception($response->collect()['Message'], 1);
                }
                // return $this->error($response->collect()['Message'], '422');
            }
        }



        if ($approvalType == 'e-reservation') {
            ResvHeader::where('U_DocEntry', '=', $form->U_DocEntry)
                ->update([
                    'ApprovalStatus' => 'W'
                ]);

            return $this->success([
                "U_DocEntry" => $form->U_DocEntry
            ], ($form->U_DocEntry != 'null' ? "Data updated!" : "Data inserted!"));
        } else {
            $header->status = 'pending';
            $header->save();
            return $this->success([], "Data submitted to cherry");
        }
    }

    protected function checkExternalDocument($collection, $form, $details, $is_request_safety)
    {
        $reservation_code = null;

        $count_item_npb = 0;
        $count_item_spb = 0;
        foreach ($details as $index => $items) {
            //check npb or spb
            if ($items['NPB'] == 'Y') {
                $count_item_npb++;
            }

            if ($items['SPB'] == 'Y') {
                $count_item_spb++;
            }
        }
        if ($is_request_safety) {
            if ($count_item_spb > 0) {
                $filtered = $collection->where('Name', "E-RESV SPB SAFETY");
                $reservation_code = $filtered->first()['Code'];
            }

            if ($count_item_npb > 0) {
                $filtered = $collection->where('Name', "E-RESV NPB SAFETY");
                $reservation_code = $filtered->first()['Code'];
            }
        } else {
            if ($count_item_npb > 0) {
                if ($form->Division == 'IT - SAP') {
                    $count_sub_group_hardware = 0;
                    foreach ($details as $index => $items) {
                        if ($items['SubGroup'] == '66101') {
                            $count_sub_group_hardware++;
                        }
                    }

                    // if ($count_sub_group_hardware > 0) {
                    //      $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (HARDWARE)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // } else {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (SAP)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // }
                    $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (SAP)");
                    $reservation_code = $filtered->first()['Code'];
                }

                if ($form->Division == 'IT - NETWORK') {
                    $count_sub_group_hardware = 0;
                    foreach ($details as $index => $items) {
                        if ($items['SubGroup'] == '66101') {
                            $count_sub_group_hardware++;
                        }
                    }

                    // if ($count_sub_group_hardware > 0) {
                    //      $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (HARDWARE)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // } else {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (NETWORK)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // }
                    $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (NETWORK)");
                    $reservation_code = $filtered->first()['Code'];
                }

                if ($form->Division == 'IT - APPLICATION DEVELOPMENT') {
                    $count_sub_group_hardware = 0;
                    foreach ($details as $index => $items) {
                        if ($items['SubGroup'] == '66101') {
                            $count_sub_group_hardware++;
                        }
                    }

                    // if ($count_sub_group_hardware > 0) {
                    //      $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (HARDWARE)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // } else {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (APP DEV)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // }
                    $filtered = $collection->where('Name', "E-RESV YUSTIN NPB (APP DEV)");
                    $reservation_code = $filtered->first()['Code'];
                }
            } elseif ($count_item_spb > 0) {
                // if ($data['Name'] == 'E-RESV YUSTIN SPB (SAP)' && $form->Division == 'IT - SAP') {
                // $reservation_code = $data['Name'];
                if ($form->Division == "IT - SAP") {
                    $count_sub_group_hardware = 0;
                    foreach ($details as $index => $items) {
                        if ($items['SubGroup'] == '66101') {
                            $count_sub_group_hardware++;
                        }
                    }

                    // if ($count_sub_group_hardware > 0) {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (HARDWARE)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // } else {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (SAP)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // }
                    $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (SAP)");
                    $reservation_code = $filtered->first()['Code'];
                }

                if ($form->Division == 'IT - NETWORK') {
                    $count_sub_group_hardware = 0;
                    foreach ($details as $index => $items) {
                        if ($items['SubGroup'] == '66101') {
                            $count_sub_group_hardware++;
                        }
                    }

                    // if ($count_sub_group_hardware > 0) {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (HARDWARE)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // } else {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (NETWORK)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // }
                    $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (NETWORK)");
                    $reservation_code = $filtered->first()['Code'];
                }

                if ($form->Division == 'IT - APPLICATION DEVELOPMENT') {
                    $count_sub_group_hardware = 0;
                    foreach ($details as $index => $items) {
                        if ($items['SubGroup'] == '66101') {
                            $count_sub_group_hardware++;
                        }
                    }

                    // if ($count_sub_group_hardware > 0) {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (HARDWARE)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // } else {
                    //     $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (APP DEV)");
                    //     $reservation_code = $filtered->first()['Code'];
                    // }
                    $filtered = $collection->where('Name', "E-RESV YUSTIN SPB (APP DEV)");
                    $reservation_code = $filtered->first()['Code'];
                }
            }
        }



        return $reservation_code;
    }

    /**
     * @param $form
     * @param $request
     *
     * @return array
     * @throws \Exception
     */
    public function submitPaperApproval($form, $request)
    {
        $cherry_token = Auth::user()->cherry_token;
        $list_code = Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'GetList',
            'ModelCode' => 'ExternalDocuments',
            'UserName' => Auth::user()->username,
            'Token' => $cherry_token,
            'ParameterData' => [],
        ]);

        $reservation_code = null;
        if ($list_code->collect()['MessageType'] == 'error') {
            throw new \Exception($list_code->collect()['Message'], 1);
        }
        //throw new \Exception(json_encode($list_code->collect()['Data']), 1);
        foreach ($list_code->collect()['Data'] as $datum) {
            if ($datum['Name'] == 'E-FORM ENTRY' && $form->paper_alias == 'sim') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM EXIT' && $form->paper_alias == 'sik') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM EXIT TAMU' && $form->paper_alias == 'skt') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM IZIN MASUK KAWASAN UNTUK TAMU' && $form->paper_alias == 'smt') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM ENTRY TAMU' && $form->paper_alias == 'smt') {
                $reservation_code = $datum['Code'];
                // } elseif ($datum['Name'] == 'E-FORM RAPID & ENTRY' && $form->paper_alias == 'srm') {
            } elseif ($datum['Name'] == 'E-FORM RAPID' && $form->paper_alias == 'srm') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM FLIGHT SEAT RESERVATION' && $form->paper_alias == 'fsr') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM COMPANY GUEST SITE VISIT' && $form->paper_alias == 'gsv') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM BOOKING RK' && $form->paper_alias == 'abr') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM RAPID MASUK KAWASAN' && ($form->paper_alias == 'rtm' || $form->paper_alias == 'rtk')) {
                $reservation_code = $datum['Code'];
            } elseif (
                $datum['Name'] == 'E-FORM RAPID' &&
                ($form->paper_alias == 'srk')
            ) {
                $reservation_code = $datum['Code'];
            }
        }
        //throw new \Exception(json_encode($reservation_code), 1);
        if (!$reservation_code) {
            throw new \Exception("Skema tidak boleh kosong", 1);
        }

        $username = $form->created_by;

        $user_name = ($form->for_self == 'Karyawan') ? $form->user_name : $form->created_name;

        if ($form->paper_alias == 'smt') {
            $user_name = $form->created_name;
        }

        $employee = ViewEmployee::where('Nik', '=', $form->created_by)->first();

        $employee_code = $employee->EmployeeCode;
        $company_code = $employee->CompanyCode;

        $attachment = Attachment::where("source_id", "=", $form->id)
            ->where("type", "=", "eform")
            ->get();

        $clinic_response = Paper::where("paper_no", "=", $form->reference_no)->first();

        // throw new \Exception($reservation_code, 1);

        $document_content = view('email.approval_eform', [
            'form' => $form,
            'paper' => $form,
            'attachment' => $attachment,
            'clinic_response' => $clinic_response,
        ])->render();

        // throw new \Exception(json_encode($list_code->collect()['Data']), 1);

        //return response()->json($document_content);
        //return response()->json($reservation_code);
        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->post(config('app.cherry_service_req'), [
                'CommandName' => 'Submit',
                'ModelCode' => 'GADocuments',
                'UserName' => $username,
                'Token' => $cherry_token,
                'ParameterData' => [],
                'ModelData' => [
                    'TypeCode' => $reservation_code,
                    'CompanyCode' => $company_code,
                    'Date' => date('m/d/Y'),
                    'EmployeeCode' => $employee_code,
                    'DocumentReferenceID' => $form->paper_no,
                    'CallBackAccessToken' => config('app.access_token_1'),
                    'DocumentContent' => $document_content,
                    'Notes' => (isset($form->reason)) ? $form->reason : $form->notes
                ]
            ]);

        info('submit eform', [
            'CommandName' => 'Submit',
            'ModelCode' => 'GADocuments',
            'UserName' => $username,
            'Token' => $cherry_token,
            'ParameterData' => [],
            'ModelData' => [
                'TypeCode' => $reservation_code,
                'CompanyCode' => $company_code,
                'Date' => date('m/d/Y'),
                'EmployeeCode' => $employee_code,
                'DocumentReferenceID' => $form->paper_no,
                'CallBackAccessToken' => config('app.access_token_1'),
                'DocumentContent' => $document_content,
                'Notes' => (isset($form->reason)) ? $form->reason : $form->notes
            ]
        ]);

        if ($response['MessageType'] == 'error') {
            return [
                'error' => true,
                'message' => $response->collect()['Message']
            ];
        }

        //return response()->json($response->collect());

        Paper::where('id', '=', $form->id)
            ->update([
                'status' => 'pending'
            ]);
        return [
            'error' => false,
            'message' => $response->collect()['Message']
        ];
    }

    /**
     * @param $form
     * @param $request
     * @param $modelData
     *
     * @return array
     */
    public function submitPaperApprovalSuperApps($form, $modelData, $request): array
    {
        $cherry_token = $modelData->CherryToken;
        $username = $request->UserName;
        $company_code = $modelData->CompanyCode;
        $employee_code = $modelData->EmployeeCode;
        $list_code = Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'GetList',
            'ModelCode' => 'ExternalDocuments',
            'UserName' => $username,
            'Token' => $cherry_token,
            'ParameterData' => [],
        ]);

        if ($list_code->failed()) {
            return [
                'error' => true,
                'message' => $list_code->body()
            ];
        }

        $reservation_code = '';
        //return response()->json($list_code->collect()['Data'] );
        foreach ($list_code->collect()['Data'] as $datum) {
            if ($datum['Name'] == 'E-FORM ENTRY' && $form->paper_alias == 'sim') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM EXIT' && $form->paper_alias == 'sik') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM EXIT TAMU' && $form->paper_alias == 'skt') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM IZIN MASUK KAWASAN UNTUK TAMU' && $form->paper_alias == 'smt') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM ENTRY TAMU' && $form->paper_alias == 'smt') {
                $reservation_code = $datum['Code'];
                // } elseif ($datum['Name'] == 'E-FORM RAPID & ENTRY' && $form->paper_alias == 'srm') {
            } elseif ($datum['Name'] == 'E-FORM RAPID' && $form->paper_alias == 'srm') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM FLIGHT SEAT RESERVATION' && $form->paper_alias == 'fsr') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM COMPANY GUEST VISIT' && $form->paper_alias == 'gsv') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM BOOKING RK' && $form->paper_alias == 'abr') {
                $reservation_code = $datum['Code'];
            } elseif ($datum['Name'] == 'E-FORM RAPID MASUK KAWASAN' && ($form->paper_alias == 'rtm' || $form->paper_alias == 'rtk')) {
                $reservation_code = $datum['Code'];
            } elseif (
                $datum['Name'] == 'E-FORM RAPID' &&
                ($form->paper_alias == 'srk')
            ) {
                $reservation_code = $datum['Code'];
            }
        }
        $user_name = ($form->for_self == 'Karyawan') ? $form->user_name : $form->created_name;

        if ($form->paper_alias == 'smt') {
            $user_name = $form->created_name;
        }

        $response = Http::get(config('app.cherry_service_employee'), [
            'username' => $username,
            'token' => $cherry_token,
            'companyCode' => $company_code,
            'q' => $user_name,
            'locationCode' => 'undefined'
        ]);

        if ($response->failed()) {
            return [
                'error' => true,
                'message' => $response->body()
            ];
        }


        $employee_codes = '';
        foreach ($response->collect() as $item) {
            if ($form->for_self == 'Karyawan') {
                // if ($item['Nik'] == $form->id_card) {
                //     $employee_codes = $item['EmployeeCode'];
                // }
                if (array_key_exists('Nik', $item)) {
                    if ($item['Nik'] == $form->id_card) {
                        $employee_codes = $item['EmployeeCode'];
                    }
                } else {
                    $employee_codes = null;
                }
            } else {
                if ($item['Nik'] == $form->user_id) {
                    $employee_codes = $item['EmployeeCode'];
                }
            }
        }

        $employee_code = (empty($employee_codes)) ? $employee_codes : $employee_code;

        $attachment = Attachment::where("source_id", "=", $form->id)
            ->where("type", "=", "eform")
            ->get();

        $clinic_response = Paper::where("paper_no", "=", $form->reference_no)->first();

        $document_content = view('email.approval_eform', [
            'form' => $form,
            'attachment' => $attachment,
            'clinic_response' => $clinic_response,
        ])->render();

        //return response()->json($document_content);
        //return response()->json($reservation_code);
        $response = Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'Submit',
            'ModelCode' => 'GADocuments',
            'UserName' => $username,
            'Token' => $cherry_token,
            'ParameterData' => [],
            'ModelData' => [
                'TypeCode' => $reservation_code,
                'CompanyCode' => $company_code,
                'Date' => date('m/d/Y'),
                'EmployeeCode' => $employee_code,
                'DocumentReferenceID' => $form->paper_no,
                'CallBackAccessToken' => config('app.access_token_1'),
                'DocumentContent' => $document_content,
                'Notes' => (isset($form->reason)) ? $form->reason : $form->notes
            ]
        ]);

        if ($response->failed()) {
            return [
                'error' => true,
                'message' => $response->body()
            ];
        }

        if ($response['MessageType'] == 'error') {
            return [
                'error' => true,
                'message' => $response->collect()['Message']
            ];
        }

        //return response()->json($response->collect());

        Paper::where('id', '=', $form->id)
            ->update([
                'status' => 'pending'
            ]);
        return [
            'error' => false,
            'message' => $response->collect()['Message']
        ];
    }
}
