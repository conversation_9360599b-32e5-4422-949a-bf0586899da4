<?php

namespace App\Services\Inventory;

use App\Models\Common\InventoryDeptMap;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\InventoryDetail;
use Illuminate\Support\Facades\Log;

class IssueReceiptService
{
    /**
     * Process the header information and update the inventory if it exists, otherwise create a new inventory entry.
     *
     * @param array $form The form data containing 'id', 'department', 'post_date', 'notes', and 'doc_type'
     * @return Inventory The updated or newly created inventory entry
     */
    public function processHeader($form)
    {
        $header = Inventory::where("id", $form['id'])->first();
        $prefix = InventoryDeptMap::where('department', $form['department'])->first();
        $suffix = 'HS';
        $data = [
            'post_date' => $form['post_date'],
            'notes' => $form['notes'],
            'doc_type' => $form['doc_type'],
            'status' => $form['status'],
            'department' => $form['department'],
            'prefix' => $prefix->prefix,
            'suffix' => $suffix,
        ];

        Log::info('check doc num issue receipt ' . $this->generateDocNum($form, $prefix));

        if ($header) {
            $data = array_merge($data, [
                'updated_by' => auth()->user()->id
            ]);

            $header->update($data);
        } else {
            $data = array_merge($data, [
                "doc_number" => $this->generateDocNum($form, $prefix),
                'created_by' => auth()->user()->id
            ]);

            $header = Inventory::create($data);
        }

        return $header;
    }

    /**
     * Generate document number based on form data and prefix.
     *
     * @param array $form description
     * @param InventoryDeptMap $prefix description
     * @return int
     */
    protected function generateDocNum($form, $prefix)
    {
        $checkExist = Inventory::where('doc_type', $form['doc_type'])
            ->where('prefix', '=', $prefix->prefix)
            ->where('suffix', '=', $form['suffix'])
            ->orderBy("id", "desc")
            ->first();

        // Log::info("check Inventory header", [
        //     // 'data' => $checkExist,
        //     'doc type' => $form['doc_type'],
        //     'prefix' => $prefix->prefix,
        //     'suffix' => $form['suffix'],
        //     'doc_number' => $checkExist->doc_number
        // ]);

        if ($checkExist) {
            return ++$checkExist->doc_number;
        } else {
            return 1;
        }
    }

    /**
     * Process the detail for the given Inventory header and item.
     *
     * @param Inventory $header The Inventory header
     * @param array $item The item data
     * @return InventoryDetail The updated or newly created InventoryDetail
     */
    public function processDetail(Inventory $header, array $item)
    {
        $detail = InventoryDetail::where('id', $item['id'])->first();
        if ($detail) {
            $detail->update([
                // 'resv_detail_id' => $item['resv_detail_id'],
                // 'header_id' => $header->id,
                'item_code' => $item['item_code'],
                'item_name' => $item['item_name'],
                'uom' => $item['uom'],
                'qty' => $item['qty'],
                'notes' => $item['notes'],
                'whs_code' => $item['whs_code'],
                'updated_by' => auth()->user()->id
            ]);
        } else {
            $detail = InventoryDetail::create([
                'resv_detail_id' => $item['resv_detail_id'],
                'header_id' => $header->id,
                'item_code' => $item['item_code'],
                'resv_number' => $item['resv_number'],
                'item_name' => $item['item_name'],
                'uom' => $item['uom'],
                'qty' => $item['qty'],
                'notes' => $item['notes'],
                'whs_code' => $item['whs_code'],
                'created_by' => auth()->user()->id
            ]);
        }

        return $detail;
    }
}
