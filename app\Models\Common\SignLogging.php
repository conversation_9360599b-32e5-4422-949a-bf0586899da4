<?php

namespace App\Models\Common;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Common\SignLogging
 *
 * @property int $id
 * @property string|null $id_document
 * @property string|null $serial_number
 * @property string|null $file_path
 * @property string|null $file_name
 * @property string|null $payload_request
 * @property string|null $payload_response
 * @property string|null $response_code
 * @property string|null $status
 * @property string|null $request_time
 * @property string|null $response_time
 * @property string|null $batch_id
 * @property int|null $request_sn
 * @property int|null $sequence
 * @property string $log_type
 * @property string $request_type
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging query()
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereIdDocument($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereLogType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging wherePayloadRequest($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging wherePayloadResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereRequestSn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereRequestTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereRequestType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereResponseCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereResponseTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereSequence($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereSerialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SignLogging whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SignLogging extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $connection = 'sqlsrv';
}
