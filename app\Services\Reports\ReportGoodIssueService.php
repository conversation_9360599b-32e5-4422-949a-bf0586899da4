<?php

namespace App\Services\Reports;

use App\Services\Reports\ReportService;
use Illuminate\Support\Facades\DB;

class ReportGoodIssueService
{
    public function show(string $dateFrom, string $dateTo, string $docType)
    {
        $rows = DB::select("EXEC SP_Report_Inventory ?,?,?,?", [
            $dateFrom,
            $dateTo,
            'MW-HSE,IG04',
            $docType
        ]);

        $service = new ReportService();
        $rows = $service->getS4Docs($rows);

        return $rows;
    }

    public function header()
    {
        return [
            "Date",
            "No Reservation",
            "Item Code",
            "Name",
            "NIK",
            "Description",
            "No GIR",
            "HS GIR",
            "Quantity",
            "UoM",
            "Department",
            "Replacement",
            "Request Date",
            "Expired Date",
            "Notes",
            "Employee Type",
            "Category APD",
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "post_date",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
                'type' => 'date',
                'height' => 26,
                'dateFormat' => 'YYYY-MM-DD',
                'correctFormat' => true,
                'datePickerConfig' => [
                    'firstDay' => 0,
                    'showWeekNumber' => true,
                    'numberOfMonths' => 1,
                ],
            ],
            [
                'data' => "resv_number",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "item_code",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "EmployeeName",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "EmployeeId",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],

            [
                'data' => "item_name",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SAP_GIRNo",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "formatted_doc_number",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "qty",
                'width' => 50,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "uom",
                'width' => 50,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Department",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Replacement",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "DocDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ExpiredDate",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "notes",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "EmployeeType",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "CategoryAPD",
                'width' => 180,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value->post_date,
                $value->resv_number,
                $value->item_code,
                $value->EmployeeName,
                $value->EmployeeId,
                $value->item_name,
                $value->SAP_GIRNo,
                $value->formatted_doc_number,
                $value->qty,
                $value->uom,
                $value->Department,
                $value->Replacement,
                $value->DocDate,
                $value->ExpiredDate,
                $value->notes,
                $value->EmployeeType,
                $value->CategoryAPD,
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
