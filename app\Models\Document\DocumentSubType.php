<?php

namespace App\Models\Document;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Document\DocumentSubType
 *
 * @property int $id
 * @property string $document_type
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentSubType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentSubType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentSubType query()
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentSubType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentSubType whereDocumentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentSubType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentSubType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DocumentSubType whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DocumentSubType extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $connection = 'sqlsrv';

    protected $casts = [
        'id' => 'integer',
    ];
}
