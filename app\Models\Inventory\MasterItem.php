<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Inventory\MasterItem
 *
 * @property int $id
 * @property string $item_code
 * @property string $item_name
 * @property string $uom
 * @property string|null $item_category
 * @property string|null $item_subcategory
 * @property float|null $on_hand_qty
 * @property float|null $committed_qty
 * @property float|null $ordered_qty
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereCommittedQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereItemCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereItemCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereItemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereItemSubcategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereOnHandQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereOrderedQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereUom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MasterItem whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class MasterItem extends Model
{
    use HasFactory;
}
