<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Http\Requests\Inventory\StoreIssueReceiptRequest;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\InventoryDetail;
use App\Services\Inventory\IssueReceiptService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Vinkla\Hashids\Facades\Hashids;

class IssueReceiptFormController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $doc_number = $request->doc_number;
        $header = Inventory::where(
            DB::raw(
                "CONCAT(prefix,FORMAT(doc_number, '00000'), suffix)"
            ),
            $doc_number
        )
            ->first();

        if ($header) {
            return $this->success([
                'header' => $header,
                'lineItems' => $header->lineItems
            ]);
        }
        throw new \Exception('Please check your input!');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreIssueReceiptRequest $request)
    {
        $disableInput = $this->getConfigByName('DisableInput', 'GENERAL');
        if ($disableInput == '1') {
            return $this->error('System is under maintenance due SAP Data Migration!!');
        }

        DB::beginTransaction();

        try {
            $details = collect($request->details);
            $form = $request->form;
            $service = new IssueReceiptService();
            $header = $service->processHeader($form);

            foreach ($details as $item) {
                $service->processDetail($header, $item);
            }

            DB::commit();
            $header = Inventory::where('id', $header->id)->first();
            return $this->success(['id' => $header->doc_num], 'Data has been saved');
        } catch (\Exception $th) {
            DB::rollback();
            throw new \Exception($th->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $header = Inventory::where('id', Hashids::decode($id))->first();

        if ($header) {
            return $this->success([
                'header' => $header,
                'lineItems' => $header->lineItems
            ]);
        }
        throw new \Exception('Data not found');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, int $id)
    {
        try {
            //code...
            $data = json_decode($request->data);
            $row = InventoryDetail::where('id', $data->id)->first();
            if ($row) {
                $row->delete();
                return response()->json(['status' => 'success', 'message' => 'Data has been deleted']);
            } else {
                return response()->json(['status' => 'error', 'message' => 'Data not found']);
            }
        } catch (\Exception $th) {
            throw new \Exception($th->getMessage());
        }
    }
}
