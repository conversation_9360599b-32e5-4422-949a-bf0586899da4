<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\ReservationDetails
 *
 * @property-read mixed $n_p_b
 * @property-read mixed $s_p_b
 * @property-read \App\Models\Resv\ReservationHeader|null $header
 * @method static \Illuminate\Database\Eloquent\Builder|ReservationDetails newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReservationDetails newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ReservationDetails query()
 * @mixin \Eloquent
 */
class ReservationDetails extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    public $incrementing = false;
    protected $connection = 'sqlsrv2';
    protected $table = 'RESV_D';
    protected $primaryKey = 'LineEntry';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $appends = [
        'NPB',
        'SPB',
    ];

    public function getNPBAttribute()
    {
        return ($this->RequestType == 'NPB' ? 'Y' : 'N');
    }

    public function getSPBAttribute()
    {
        return ($this->RequestType == 'SPB' ? 'Y' : 'N');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function header()
    {
        return $this->belongsTo(ReservationHeader::class, 'U_DocEntry', 'U_DocEntry');
    }
}
