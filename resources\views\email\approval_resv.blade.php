<div
    style="font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'; padding: 0px 0px; max-width: 100%;">
    <div style="width:100%; background:#fff; border-radius:12px; box-shadow:0 2px 2px rgba(0,0,0,0.07); margin-bottom:16px; font-size:12px; padding:16px;">
        @if ($newApproval)
            <div style="display:flex; margin-bottom:8px;">
                <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                    Doc Number
                </span>
                <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
                <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                    {{ $header->DocNum }}
                </span>
            </div>
            <div style="display:flex; margin-bottom:8px;">
                <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                    Department
                </span>
                <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
                <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                    {{ $header->Department }}
                </span>
            </div>
            <div style="display:flex; margin-bottom:8px;">
                <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                    Item Request Type
                </span>
                <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
                <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                    {{ $details[0]['RequestType'] }}
                </span>
            </div>
        @endif
        <div style="display:flex; margin-bottom:8px;">
            <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                Request Type
            </span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                {{ $header->RequestType }}
            </span>
        </div>
        <div style="display:flex; margin-bottom:8px;">
            <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                Requester
            </span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                {{ $header->RequesterName }} ({{ $header->Requester }})
            </span>
        </div>
        <div style="display:flex; margin-bottom:8px;">
            <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                Item Type
            </span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                {{ $header->ItemType }}
            </span>
        </div>
        <div style="display:flex; margin-bottom:8px;">
            <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                Warehouse
            </span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                {{ $header->WhsCode }}
            </span>
        </div>

        @if ($header->WorkLocation == 'IMIP MOROWALI')
            @if ($header->Department == "IT - NETWORK")
                <div style="display:flex; margin-bottom:8px;">
                    <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                        Movement Type
                    </span>
                    <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
                    <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                        {{ $movementType->movement_type }} - {{ $movementType->description }}
                    </span>
                </div>
            @endif

            @if ($header->CategoryType == "APD")
                <div style="display:flex; margin-bottom:8px;">
                    <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                        Replacement
                    </span>
                    <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
                    <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top;">
                        {{ $header->Replacement }}
                    </span>
                </div>
            @endif
        @endif
        <div style="display:flex; margin-bottom:8px;">
            <span style="text-align:left; font-weight:700; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; width:250px;">
                Notes
            </span>
            <span style="font-weight:400; color:#222; font-size:12px; white-space:nowrap; vertical-align:top; margin:0 8px; width: 10px;">:</span>
            <span style="font-weight:400; color:#222; font-size:12px; vertical-align:top;">
                {{ $header->Memo }}
            </span>
        </div>
    </div>
</div>

<!-- Details Table -->
<div
    style="overflow-x:auto; max-width:100%; font-family: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';">
    <table style="width:100%; border-collapse:separate; border-spacing:0 14px; font-size:12px;">
        @php
            $countOrder = 0;
        @endphp

        @foreach ($details as $key => $value)
            @php
                if (isset($value['OrderId'])) {
                    $countOrder = $countOrder + 1;
                }
            @endphp
        @endforeach

        <thead>
            <tr>
                <th
                    style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; border-radius:8px 0 0 8px; font-size:12px; white-space:nowrap;">
                    #</th>
                @if($header->DocumentType != 'Service')
                    <th
                        style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                        Item Code</th>
                @endif
                <th
                    style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:normal; width: 200px;">
                    Item Name</th>
                @if ($header->ItemType == "Asset")
                    <th
                        style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                        Asset Code</th>
                    <th
                        style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:normal; width: 200px;">
                        Asset Name</th>
                @endif
                @if ($countOrder > 0)
                    <th
                        style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                        Order</th>
                @endif
                <th
                    style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                    Required Date</th>
                <th
                    style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                    UoM</th>
                <th
                    style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                    Req Qty</th>
                <th
                    style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                    Req Date</th>
                <th
                    style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; border-radius:0 8px 8px 0; white-space:nowrap;">
                    Notes</th>

                @if ($header->CategoryType == "APD")
                    <th
                        style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                        Employee Name</th>
                    <th
                        style="padding:8px 6px; font-weight:600; color:#222; background:#f3f4f6; font-size:12px; white-space:nowrap;">
                        Employee Id</th>
                @endif
            </tr>
        </thead>

        <tbody>
            @foreach ($details as $key => $value)
                <tr style="background:#fff; border-radius:12px; box-shadow:0 2px 2px rgba(0,0,0,0.07);">
                    <td style="padding:3px 6px; text-align:center; white-space:nowrap;">
                        <span
                            style="display:inline-block; width:20px; height:20px; background:#BFDDFB; color:#222; border-radius:50%; font-weight:700; line-height:20px; font-size:12px;">{{ $key + 1 }}</span>
                    </td>
                    @if($header->DocumentType != 'Service')
                        @if($header->ItemType == 'Non Ready Stock')
                            <td
                                style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                                <a href="{{ $value["U_ATTACH"] }}">{{ $value["ItemCode"] }}</a>
                            </td>
                        @else
                            <td
                                style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                                {{ $value["ItemCode"] }}
                            </td>
                        @endif
                    @endif

                    @if($header->DocumentType == 'Service')
                        <td
                            style="padding:3px 6px; color:#222; font-weight:600; word-break:break-word;white-space:normal;width: 200px;">
                            <a style="display:block; font-size:12px; word-break:break-word;width: 250px;"
                                href="{{ $value["U_ATTACH"] }}">{{ $value["ItemName"] }}</a>
                        </td>
                    @else
                        <td
                            style="padding:3px 6px; color:#222; font-weight:600; word-break:break-word;white-space:normal;width: 200px;">
                            <span style="display:block; font-size:12px; word-break:break-word;width: 250px;">
                                {{ $value["ItemName"] }}
                            </span>
                        </td>
                    @endif

                    @if ($header->ItemType == "Asset")
                        <td
                            style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                            {{ $value["AssetCode"] }}
                        </td>
                        <td
                            style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                            <span style="display:block; font-size:12px; word-break:break-word;width: 250px;">
                                {{ $value["AssetName"] }}
                            </span>
                        </td>
                    @endif
                    @if (isset($value['OrderId']))
                        <td
                            style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                            {{ $value['OrderId'] }}
                        </td>
                    @endif
                    <td
                        style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                        {{ $header->RequiredDate }}
                    </td>
                    <td
                        style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                        {{ $value["UoMCode"] }}
                    </td>
                    <td
                        style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                        {{ floatval($value["ReqQty"]) }}
                    </td>
                    <td
                        style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                        {{ $value["ReqDate"] }}
                    </td>
                    <td
                        style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                        {{ (array_key_exists('ReqNotes', $value)) ? $value["ReqNotes"] : '' }}
                    </td>

                    @if ($header->CategoryType == "APD")
                        <td
                            style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                            {{ (array_key_exists('EmployeeName', $value)) ? $value["EmployeeName"] : '' }}
                        </td>
                        <td
                            style="padding:3px 6px; text-align:center; color:#222; font-weight:500; font-size:12px; white-space:nowrap;">
                            {{ (array_key_exists('EmployeeId', $value)) ? $value["EmployeeId"] : '' }}
                        </td>
                    @endif
                </tr>
            @endforeach
        </tbody>
    </table>
</div>

