<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\ViewEmployee
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ViewEmployee newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewEmployee newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewEmployee query()
 * @mixin \Eloquent
 */
class ViewEmployee extends Model
{
    protected $connection = 'sqlsrv5';
    protected $table = 'vw_employee_masterdata';

    protected $casts = [
        'BirthDate' => 'date'
    ];

    // Define the hidden attributes
    protected $hidden = [
        'Photo',
    ];
}
