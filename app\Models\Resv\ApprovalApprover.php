<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\ApprovalApprover
 *
 * @property-read \App\Models\Resv\Stages|null $stages
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalApprover query()
 * @mixin \Eloquent
 */
class ApprovalApprover extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $connection = 'sqlsrv2';
    protected $table = 'U_WTM2';
    protected $primaryKey = 'U_DocEntry';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function stages(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Stages::class, "U_WstCode", "U_WstCode");
    }
}
