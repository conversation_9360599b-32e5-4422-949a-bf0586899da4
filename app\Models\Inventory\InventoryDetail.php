<?php

namespace App\Models\Inventory;

use App\Models\Resv\ResvDetail;
use App\Models\Resv\ResvHeader;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * App\Models\Inventory\InventoryDetail
 *
 * @property int $id
 * @property int $resv_detail_id
 * @property int $header_id
 * @property string $item_code
 * @property string $item_name
 * @property string $uom
 * @property float $qty
 * @property string $notes
 * @property string $whs_code
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail query()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereHeaderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereItemCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereItemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereResvDetailId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereUom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereWhsCode($value)
 * @property string|null $resv_number
 * @property-read mixed $employee_id
 * @property-read mixed $employee_name
 * @property-read \App\Models\Inventory\Inventory|null $header
 * @property-read ResvDetail|null $resvDetail
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDetail whereResvNumber($value)
 * @mixin \Eloquent
 */
class InventoryDetail extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    use HasFactory;

    protected $guarded = [];

    protected $appends = [
        'employee_id',
        'employee_name'
    ];

    public function getEmployeeIdAttribute()
    {
        return $this->resvDetail->EmployeeId;
    }

    public function getEmployeeNameAttribute()
    {
        return $this->resvDetail->EmployeeName;
    }

    public function resvDetail()
    {
        return $this->belongsTo(ResvDetail::class, 'resv_detail_id', 'LineEntry');
    }

    public function header()
    {
        return $this->belongsTo(Inventory::class, 'header_id', 'id');
    }
}
