<?php

namespace App\Models\Common;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Common\SafetyData
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData query()
 * @property int $U_DocEntry
 * @property string|null $date_out
 * @property string|null $item_code
 * @property string|null $uom
 * @property string|null $item_name
 * @property string|null $id_card
 * @property string|null $employee_name
 * @property string|null $company
 * @property string|null $department
 * @property int|null $created_by
 * @property string|null $notes
 * @property float|null $qty
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property int|null $header_id
 * @property int|null $detail_id
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereDateOut($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereDetailId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereEmployeeName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereHeaderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereIdCard($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereItemCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereItemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereUDocEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereUom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SafetyData extends Model
{
    protected $connection = 'sqlsrv';

    use HasFactory;

    protected $guarded = [];

    //    public $incrementing = false;
    // protected $table = 'RESV_H';
    public $timestamps = false;
    protected $primaryKey = 'U_DocEntry';
}
