<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('inventory_dept_maps', function (Blueprint $table) {
            // $table->dropIndex(['department', 'prefix']);
            $table->unique('department');
            $table->unique('prefix');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventory_dept_maps', function (Blueprint $table) {
            //
        });
    }
};
