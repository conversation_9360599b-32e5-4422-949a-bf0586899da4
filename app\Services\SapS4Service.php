<?php

namespace App\Services;

use App\Models\User;
use App\Traits\AppConfig;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SapS4Service
{
    use AppConfig;
    /**
     * Logs in the user and retrieves the SAP S4 cookie.
     *
     * @return string The SAP S4 cookie value.
     */
    public function login()
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getToken = $this->getConfigByName('SapTokenUrl', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $response = Http::withoutVerifying()
            ->withOptions(["verify" => false])
            ->withHeaders([
                'x-csrf-token' => 'fetch'
            ])
            ->withBasicAuth($this->getConfigByName('UsernameS4', 'SAPS4'), $this->getConfigByName('PasswordS4', 'SAPS4'))
            ->get($baseUrl . $getToken, [
                'sap-client' => $clientId
            ]);
        // Log::info("response login sap", [
        //     "url" => $baseUrl . $getToken,
        //     'response' => $response->headers()['set-cookie']
        // ]);

        $this->storeConfig('SapS4Token', $response->header('x-csrf-token'), 'SAPS4');

        // Safely handle cookie storage
        $cookies = $response->headers()['set-cookie'] ?? [];
        if (empty($cookies)) {
            Log::error('No cookies received from SAP S4 login', [
                "cookies" => $cookies
            ]);
            return '';  // Return empty string if no cookies available
        }

        // Extract and concatenate all cookies into a single string
        $cookieString = implode('; ', array_map(function ($cookie) {
            return explode(';', $cookie, 2)[0]; // Extract the main key-value pair
        }, $cookies));

        // $this->storeConfig('SapS4Cookie', explode(";", $response->headers()['set-cookie'][1])[0], 'SAPS4');
        // $this->storeConfig('SapS4CookieContex', explode(";", $response->headers()['set-cookie'][0])[0], 'SAPS4');

        // return explode(";", $response->headers()['set-cookie'][1])[0];

        // Store the main cookie
        // $mainCookie = isset($cookies[2]) ? explode(";", $cookies[2])[0] : explode(";", $cookies[0])[0];
        $mainCookie = isset($cookies[1]) ? explode(";", $cookies[1])[0] : explode(";", $cookies[0])[0];
        // info("cookie " , [
        //      'cookie' =>   $cookies
        // ] );
        // $this->storeConfig('SapS4Cookie', $mainCookie, 'SAPS4');
        $this->storeConfig('SapS4Cookie', $cookieString, 'SAPS4');

        // Store the context cookie
        $contextCookie = isset($cookies[0]) ? explode(";", $cookies[0])[0] : '';
        $this->storeConfig('SapS4CookieContex', $contextCookie, 'SAPS4');

        return $mainCookie;

        // $this->storeConfig('SapS4Cookie', $response->header('set-cookie'), 'SAPS4');
        // $this->storeConfig('SapS4Cookie', serialize( $response->headers()['set-cookie']), 'SAPS4');
        // return serialize( $response->headers()['set-cookie']);
    }

    /**
     * Retrieves a list of materials based on specified criteria.
     *
     * @param int $page The page number of the results. Default is 1.
     * @param int $perPage The number of materials per page. Default is 20.
     * @param string|null $itemCode The code of the material. Default is null.
     * @param string|null $itemGroup The group of the material. Default is null.
     * @param string|null $itemName The name of the material. Default is null.
     * @param string|null $itemType The type of the material. Default is null.
     * @param string|null $whs The warehouse of the material. Default is null.
     * @return array The list of materials.
     */
    public function getMaterial($page = 1, $perPage = 20, $itemCode = null, $itemGroup = null, $itemName = null, $itemType = null, $whs = null)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetItemUrl', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "ItemCode" => ($itemCode) ? (is_array($itemCode) ? $itemCode : [$itemCode]) : [],
                    "MatType" => ($itemGroup) ? (is_array($itemGroup) ? $itemGroup : [$itemGroup]) : [],
                    "MatDesc" => ($itemName) ? $itemName : "",
                    "ProdHier" => [],
                    "ItemGroup" => ($itemType) ? ((is_array($itemType)) ? $itemType : [$itemType]) : [],
                    "Sloc" => ($whs) ? ((is_array($whs)) ? $whs : [$whs]) : [],
                    "ItemCount" => $perPage,
                    "Page" => $page
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        curl_close($curl);

        // Log::info('params material', [
        //     "ItemCode" => ($itemCode) ? [$itemCode] : [],
        //     "MatType" => ($itemGroup) ? (is_array($itemGroup) ? $itemGroup : [$itemGroup]) : [],
        //     "MatDesc" => ($itemName) ? $itemName : "",
        //     "ProdHier" => [],
        //     "ItemGroup" => ($itemType) ? ((is_array($itemType)) ? $itemType : [$itemType]) : [],
        //     "Sloc" => ($whs) ? ((is_array($whs)) ? $whs : [$whs]) : [],
        //     "ItemCount" => $perPage,
        //     "Page" => $page
        //     // 'response' => $response
        // ]);

        return json_decode($response, true);
    }


    public function getStatusRequestItem($item)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetRequestItemStatus', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "requestnum" => $item,
                    "Plant" => ["IM02"],
                    "Sloc" => ["IG01", "IG03", "IG02"]
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        curl_close($curl);

        // Log::info('params get status request item', [
        //     "response" =>  json_decode($response, true),
        //     "requestnum" => $item,
        //     "Plant" => ["IM02"],
        //     "Sloc" => ["IG01"],
        //     "URL" => $baseUrl . $getAction . '?sap-client=' . $clientId
        // ]);
        return json_decode($response, true);
    }

    public function getPlanByCompany($company)
    {
        if ($company == 'PT IMIP') {
            return ['IM01', 'IM02', 'IM03'];
        } else if ($company == 'PT BDM') {
            // return ['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03'];
            return ['BD01', 'BD02', 'BD03'];
        } else if ($company == 'PT BDW') {
            return ['BW01', 'BW02', 'BW03'];
        } else {
            return [];
        }
    }

    protected function getPlanByHeaderRequest($company, $header)
    {
        if ($header) {
            return $this->getPlanByCompany($header);
        }
        return $this->getPlanByCompany($company);
    }

    /**
     * Retrieves a SLOC / Warehouse from the API.
     *
     * @param int $page The page number of the plan to retrieve. Default is 1.
     * @param int $perPage The number of items to retrieve per page. Default is 100.
     * @param string|null $itemCode The item code to filter the plan by. Default is null.
     * @return array The retrieved plan as an associative array.
     */
    public function getSloc($page = 1, $perPage = 100, $itemCode = null, $company, User $user, $header = null)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetWarehouseUrl', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');


        $plant = function ($company, $user, $header) {
            if ($user) {
                if ($user->hasAnyRole(['Superuser'])) {
                    return array_merge(['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03'], ['IM01', 'IM02', 'IM03']);
                } elseif ($user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP', 'Admin E-RESERVATION JETTY BDM IMIP'])) {
                    return $this->getPlanByHeaderRequest($company, $header);
                } elseif ($company == 'PT IMIP') {
                    return $this->getPlanByHeaderRequest($company, $header);
                } else if ($company == 'PT BDM') {
                    return $this->getPlanByHeaderRequest($company, $header);
                } else if ($company == 'PT BDW') {
                    return $this->getPlanByHeaderRequest($company, $header);
                } else {
                    return [];
                }
            } elseif ($company == 'PT IMIP') {
                return $this->getPlanByCompany($company);
                // return ['IM01', 'IM02', 'IM03'];
            } else if ($company == 'PT BDM') {
                return $this->getPlanByCompany($company);
                // return ['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03'];
            } else if ($company == 'PT BDW') {
                return $this->getPlanByCompany($company);
                // return ['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03'];
            } else {
                return [];
            }
        };

        try {
            $response = Http::withoutVerifying()
                ->withOptions([
                    "verify" => false,
                    "timeout" => 0,
                    "http_version" => CURL_HTTP_VERSION_1_1,
                    "follow_redirects" => true,
                    "max_redirects" => 10
                ])
                ->withHeaders([
                    'x-csrf-token' => $this->getConfigByName('SapS4Token', 'SAPS4'),
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Basic ' . $this->getConfigByName('SapS4Token', 'SAPS4'),
                    'Cookie' => $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ])
                ->withBody(json_encode([
                    "Plant" => $plant($company, $user, $header),
                    "ItemCount" => $perPage,
                    "Page" => $page
                ]), 'application/json')
                ->get($baseUrl . $getAction . '?sap-client=' . $clientId);

            $result = $response->json();

            if (!$result) {
                Log::debug('Error get Sloc, params get sloc', [
                    "Plant" => $plant($company, $user, $header),
                    "ItemCount" => $perPage,
                    "Page" => $page,
                    "URL" => $baseUrl . $getAction . '?sap-client=' . $clientId,
                    'x-csrf-token' => $this->getConfigByName('SapS4Token', 'SAPS4'),
                    'Authorization' => 'Basic ' . $this->getConfigByName('SapS4Token', 'SAPS4'),
                    "Cookie" => $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId,
                    "Status" => $response->status(),
                    "Response" => $response->body()
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::debug('Exception in getSloc', [
                "Plant" => $plant($company, $user, $header),
                "ItemCount" => $perPage,
                "Page" => $page,
                "URL" => $baseUrl . $getAction . '?sap-client=' . $clientId,
                "Exception" => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Retrieves an asset from the SAP S4 system.
     *
     * @param int $page The page number of the asset to retrieve.
     * @param int $perPage The number of assets to retrieve per page.
     * @param string|null $itemCode The item code of the asset to retrieve.
     * @return array The retrieved asset as an associative array.
     */
    public function getAsset($page = 1, $perPage = 100, $itemCode = null, $assetName = null)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetAssetUrl', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "AssetCode" => ($itemCode) ? $itemCode : "",
                    "ASSETDESC" => ($assetName) ? $assetName : '',
                    "ItemCount" => $perPage,
                    "Page" => $page
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        // Log::info('params asset ', [
        //     'params' => [
        //         "Bukrs" => $this->getBukrs(),
        //         "ASSETDESC" => ($assetName) ? $assetName : '',
        //         "ItemCount" => $perPage,
        //         "Page" => $page
        //     ],
        //     // 'response' => $response,
        //     'url' => $baseUrl . $getAction . '?sap-client=' . $clientId
        // ]);

        curl_close($curl);
        return json_decode($response, true);
    }

    /**
     * Retrieves an item group from the SAP system.
     *
     * @param int $page The page number of the item group to retrieve.
     * @param int $perPage The number of items per page.
     * @param string|null $itemCode The item code to filter the item group by.
     * @return array The item group retrieved from the SAP system.
     */
    public function getItemGroup($page = 1, $perPage = 100, $itemCode = null)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetItemGroupUrl', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "BUKRS" => $this->getBukrs(),
                    "ItemCount" => $perPage,
                    "Page" => $page
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        // Log::info('params item group ', [
        //     'params' => [
        //         "BUKRS" => $this->getBukrs(),
        //         "ItemCount" => $perPage,
        //         "Page" => $page
        //     ],
        //     // 'response' => $response,
        //     'url' => $baseUrl . $getAction . '?sap-client=' . $clientId
        // ]);

        curl_close($curl);
        return json_decode($response, true);
    }

    public function getBukrs()
    {
        $user = User::where('id', "=", auth()->user()->id)->first();
        if ($user) {
            if ($user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP'])) {
                return ['IM', 'BD'];
            } elseif ($user->hasAnyRole(['Superuser'])) {
                return ['IM'];
            } elseif (Str::contains($user->location, ['IMIP', 'BDT'])) {
                return ['IM'];
            } elseif (Str::contains($user->location, ['BDM'])) {
                return ['BD'];
            } else {
                return ['IM'];
            }
        } else {
            return ['IM'];
        }
    }

    /**
     * Transforms the item data from S4 format to a specific format.
     *
     * @param array $items The item data in S4 format.
     * @return array The transformed item data.
     */
    public function transformItemDataFromS4($items)
    {
        $data = [];
        foreach ($items['DATA'] as $index => $item) {
            $data[] = [
                "RowNo" => ++$index,
                "Keys" => $index . $item["MATNR"],
                "ItemCode" => $item["MATNR"],
                // "ItemName" => $item['MAKTX'],
                "ItemName" => $item['MATDESC1'],
                "OnHand" => $item['LABST'],
                "ItmsGrpCod" => $item['MTART'],
                "ItmsGrpNam" => $item['MTBEZ'],
                "Available" => $item['LABST'],
                "InvntryUom" => $item['MEINS'],
                "U_SubGroup" => $item['MATKL'],
                "SubGroupName" => $item['WGBEZ60'],
                "InvntItem" => null,
                "U_Department" => $item['Z_DEPARTMENT'],
                "U_Period" => $item['Z_PERIODE_PENGAMBILAN'],
                "U_Category" => $item['Z_CATEGORY_SAFETY'],
                "U_AppResBy" => $item['Z_APPROVAL_RESERVED_BY'],
                "WhsCode" => $item['LGORT'],
                "MinLevel" => null,
                "U_ItemType" => $item['EXTWG'],
                "MFRPN" => $item['MFRPN'],
                "MATNR" => $item['MATNR'],
            ];
        }

        return $data;
    }

    public function transformDataDetail($items)
    {
        $data = [];
        foreach ($items['DATA'] as $index => $item) {
            $data[] = [
                "LineNumber" => $item["I_ERESERVASI"],
                "NoGIR" => $item["RESERVASI"],
                "NoPR" => $item["PR"],
                "NoPO" => $item["PO"],
                "NoGR" => $item["GR"],
                "NoGI" => $item["GI"],
            ];
        }

        return $data;
    }

    public function transformItemDataFromS4ReportStock($items, $displayNetwork)
    {
        $data = [];
        foreach ($items['DATA'] as $index => $item) {
            if (!$displayNetwork) {
                if ($item['MATKL'] != '66103') {
                    $data[] = [
                        "ItemCode" => $item["MATNR"],
                        // "ItemName" => $item['MAKTX'],
                        "ItemName" => $item['MATDESC1'],
                        "InvntryUom" => $item['MEINS'],
                        "MinLevel" => null,
                        "Available" => $item['LABST'],
                        "SubGroupName" => $item['WGBEZ60']
                    ];
                }
            } else {
                $data[] = [
                    "ItemCode" => $item["MATNR"],
                    // "ItemName" => $item['MAKTX'],
                    "ItemName" => $item['MATDESC1'],
                    "InvntryUom" => $item['MEINS'],
                    "MinLevel" => null,
                    "Available" => $item['LABST'],
                    "SubGroupName" => $item['WGBEZ60']
                ];
            }
        }

        return $data;
    }


    /**
     * Retrieves the flow for a given document number.
     *
     * @param int|array $docNum The document number to retrieve the flow for.
     * @return array The flow data as an associative array.
     */
    public function getFlow(int|array $docNum)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetFlowUrl', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();


        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "ERESERVATION" => ($docNum) ? ((is_array($docNum)) ? $docNum : [$docNum]) : [],
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        curl_close($curl);

        // Log::info('params flow documment production', [
        //     'url' => $baseUrl . $getAction . '?sap-client=' . $clientId,
        //     "params" => json_encode([
        //         "ERESERVATION" => ($docNum) ? ((is_array($docNum)) ? $docNum : [$docNum]) : [],
        //     ]),
        //     'response' => json_decode($response, true),
        //     // 'response' => json_decode($response, true),
        //     'docnum' => $docNum
        // ]);

        return json_decode($response, true);
    }

    public function getFlowDetail(int|array $docNum)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetFlowDetail', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();


        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "ERESERVATION" => ($docNum) ? ((is_array($docNum)) ? $docNum : [$docNum]) : [],
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        curl_close($curl);

        // Log::info('params flow documment production', [
        //     'url' => $baseUrl . $getAction . '?sap-client=' . $clientId,
        //     "params" => json_encode([
        //         "ERESERVATION" => ($docNum) ? ((is_array($docNum)) ? $docNum : [$docNum]) : [],
        //     ]),
        //     'response' => json_decode($response, true),
        //     // 'response' => json_decode($response, true),
        //     'docnum' => $docNum
        // ]);

        return json_decode($response, true);
    }

    public function getGoodIssue(int|array $docNum)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetGoodIssue', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();


        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "ERESERVATION" => ($docNum) ? ((is_array($docNum)) ? $docNum : [$docNum]) : [],
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        curl_close($curl);

        // Log::info('params flow documment production', [
        //     'url' => $baseUrl . $getAction . '?sap-client=' . $clientId,
        //     "params" => json_encode([
        //         "ERESERVATION" => ($docNum) ? ((is_array($docNum)) ? $docNum : [$docNum]) : [],
        //     ]),
        //     'response' => json_decode($response, true),
        //     // 'response' => json_decode($response, true),
        //     'docnum' => $docNum
        // ]);

        return json_decode($response, true);
    }

    public function mergeReservationToSapFlow($data)
    {
        $docNum = $data->pluck('DocNum');
        $flow = $this->getFlow($docNum);

        $rows = [];
        foreach ($flow['DATA'] as $key => $item) {
            $rows[] = [
                "SAP_GIRNo" => $item['RESERVASI'],
            ];
        }
    }

    /**
     * Retrieves an IO order from the SAP system.
     *
     * @param int $page The page number of the IO order to retrieve.
     * @param int $perPage The number of IO orders to retrieve per page.
     * @param string|null $itemCode The item code of the IO order to retrieve.
     * @return array The retrieved IO order.
     */
    public function getIoOrder($page = 1, $perPage = 100, $itemCode = null)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetIoUrl', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "BUKRS" => $this->getBukrs(),
                    "ItemCount" => $perPage,
                    "Page" => $page
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        // Log::info('params io order', [
        //     'url' => $baseUrl . $getAction . '?sap-client=' . $clientId,
        //     "params" => json_encode([
        //         "BUKRS" => $this->getBukrs(),
        //         "ItemCount" => $perPage,
        //         "Page" => $page
        //     ]),
        //     // 'response' => $response,
        // ]);

        curl_close($curl);
        return json_decode($response, true);
    }

    public function getCustomer($page = 1, $perPage = 100000, $itemCode = null)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetCustomer', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "BUKRS" => $this->getBukrs(),
                    "ItemCount" => $perPage,
                    "Page" => $page
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        // Log::info('params io order', [
        //     'url' => $baseUrl . $getAction . '?sap-client=' . $clientId,
        //     "params" => json_encode([
        //          "BUKRS" => [$this->getBukrs()],
        //             "ItemCount" => $perPage,
        //             "Page" => $page
        //     ]),
        //     // 'response' => $response,
        // ]);

        curl_close($curl);
        return json_decode($response, true);
    }

    /**
     * Retrieves an IO order from the SAP system.
     *
     * @param int $page The page number of the IO order to retrieve.
     * @param int $perPage The number of IO orders to retrieve per page.
     * @param string|null $itemCode The item code of the IO order to retrieve.
     * @return array The retrieved IO order.
     */
    public function getUom($page = 1, $perPage = 100, $itemCode = null)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $getAction = $this->getConfigByName('SapGetUomUrl', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $getAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => json_encode([
                    "BUKRS" => $this->getBukrs(),
                    "ItemCount" => $perPage,
                    "Page" => $page
                ]),
                CURLOPT_HTTPHEADER => array(
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);


        // Log::info('params uom', [
        //     "BUKRS" => $this->getBukrs(),
        //     "ItemCount" => $perPage,
        //     "Page" => $page,
        //     // 'response' => $response
        // ]);

        curl_close($curl);
        return json_decode($response, true);
    }
}
