<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\NewEmployee
 *
 * @method static \Illuminate\Database\Eloquent\Builder|NewEmployee newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NewEmployee newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NewEmployee query()
 * @mixin \Eloquent
 */
class NewEmployee extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    public $incrementing = false;
    protected $connection = 'sqlsrv2';
    protected $table = 'new_employee';
    protected $primaryKey = 'U_DocEntry';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
