<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;
use App\Notifications\NotifPurchaseRequest;
use App\Models\Resv\ResvHeader;
use App\Models\Resv\ResvDetail;
use App\Models\Resv\NewEmployee;
use App\Services\ReservationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\View\ViewEmployee;

class SendPurchaseRequestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pr:email {docnum}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send email purchase requests';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $db_name = config('app.db_sap');
        Log::info('Docnum reservasi to send email PR: ' . $this->argument('docnum'));
        $header = ResvHeader::select(
            "resv_headers.*",
            "resv_headers.Company as CompanyName"
        )
            ->where("resv_headers.DocNum", "=", $this->argument('docnum'))
            ->first();
        $subject = "[E-RESERVATION] Purchase Request: " . $header->Memo;

        $data_details = ResvDetail::where("U_DocEntry", "=", $header->U_DocEntry)
            ->get();
        // $service = new ReservationService();
        // $attachment = public_path($service->printDocument($header, 'all', $header->Requester));
        $content = [
            'header' => $header,
            'detail' => $data_details,
            'subject' => $subject,
            // 'attachment' => $attachment,
        ];

        $receiver = [
            '<EMAIL>',
            '<EMAIL>'
        ];

        // $receiver = [
        //     '<EMAIL>',
        // ];

        if ($header->WhsCode == 'IH02') {
            $receiver = array_merge($receiver, ['<EMAIL>']);
        }


        // $receiver = [
        //     '<EMAIL>',
        // ];

        $requester = ViewEmployee::where('Nik', $header->Requester)->first();

        if ($requester) {
            if ($requester->OfficeEmailAddress) {
                $cc_email = [
                    '<EMAIL>',
                    '<EMAIL>',
                    $requester->OfficeEmailAddress
                ];
            } else {
                $cc_email = [
                    '<EMAIL>',
                    '<EMAIL>',
                ];
            }
        }

        // $cc_email = array_merge($cc_email, )

        $receiver_name = ucwords(strtolower('DADI RAHAYU, NANDAVRILLA SAPUTRA'));
        Log::info('prepare notif PR', [
            'header' => $header->DocNum,
            // 'detail' => $data_details,
            'subject' => $subject,
            // 'attachment' => $attachment,
            'cc' => array_combine($cc_email, $cc_email),
            'receiver' => array_combine($receiver, $receiver)
        ]);

        dispatch(
            new \App\Jobs\ProcessSendEmailPurchaseRequest(
                $content,
                $receiver,
                $receiver_name,
                $cc_email
            )
        );
        // Notification::route('mail', $receiver)
        //     ->notify(new \App\Notifications\NotifPurchaseRequest($receiver_name, $content, $cc_email));
    }
}
