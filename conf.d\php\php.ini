# https://www.php.net/manual/en/opcache.configuration.php
# zend_extension=opcache.so
# opcache.enable=${PHP_OPCACHE_ENABLE}
# opcache.enable_cli=${PHP_OPCACHE_ENABLE_CLI}
# opcache.validate_timestamp=${PHP_OPCACHE_VALIDATE_TIMESTAMP}
# opcache.revalidate_freq=${PHP_OPCACHE_REVALIDATE_FREQ}
# opcache.memory_consumption=128

[PHP]

;;;;;;;;;;;;;;;;;;;
; Resource Limits ;
;;;;;;;;;;;;;;;;;;;
memory_limit = 256M
max_execution_time = 1000
max_input_time = 300

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; File Uploads and Post Size ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
post_max_size = 100M
upload_max_filesize = 100M

; Realpath Cache
realpath_cache_size = 4096k
realpath_cache_ttl = 600

; Output Buffering
output_buffering = On

;;;;;;;;;;;;;;;;;;;;
; Session Handling ;
;;;;;;;;;;;;;;;;;;;;
session.gc_maxlifetime = 1440
session.save_path = "/tmp"

;;;;;;;;;;;;;;;;;;;;
; Security and Safety ;
;;;;;;;;;;;;;;;;;;;;
disable_functions = exec,passthru,shell_exec,system

;;;;;;;;;;;;;;;;;;;;
; File Uploads ;
;;;;;;;;;;;;;;;;;;;;
file_uploads = On

;;;;;;;;;;;;;;;;;;;;
; Error Handling and Logging ;
;;;;;;;;;;;;;;;;;;;;
log_errors = On
error_reporting = E_ALL
display_errors = Off
display_startup_errors = Off

;;;;;;;;;;;;;;;;;;;;
; Other Settings ;
;;;;;;;;;;;;;;;;;;;;
; Add any other settings you might need for your specific application

; Hide X-Powered-By header
expose_php = Off

variables_order = "EGPCS"

[JIT]
opcache.jit_buffer_size = 128M
opcache.jit = tracing

[zlib]
zlib.output_compression = On
zlib.output_compression_level = 9
