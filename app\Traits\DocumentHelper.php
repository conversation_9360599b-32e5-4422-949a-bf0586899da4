<?php

namespace App\Traits;

use App\Models\Document\Document;

trait DocumentHelper
{
    /**
     * @param $id
     * @return void
     */
    public function makeDocumentApprovePartial($id)
    {
        $document = Document::find($id);
        $document->status = 'approved - on process';
        $document->save();
    }

    public function makeDocumentApproveFailed($id)
    {
        $document = Document::find($id);
        $document->status = 'approved - failed';
        $document->save();
    }
}
