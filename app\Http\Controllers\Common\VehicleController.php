<?php

namespace App\Http\Controllers\Common;

use App\Models\Common\Vehicle;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $data = Vehicle::select(
            "id",
            "vehicle_type",
            "vehicle_no",
            "internal_no",
            "is_carpool",
        )
            ->orderBy('vehicle_no', 'desc')
            ->get();

        if (count($data) < 1) {
            $data = [
                [
                    "id" => null,
                    "vehicle_type" => null,
                    "vehicle_no" => null,
                    "internal_no" => null,
                    "is_carpool" => null,
                ]
            ];
        }

        return $this->success([
            'rows' => $data,
            'columns' => [
                [
                    'data' => 'id',
                    'width' => 50,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'vehicle_type',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'vehicle_no',
                    'width' => 30,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'internal_no',
                    'width' => 60,
                    'wordWrap' => false,
                ],
                [
                    'data' => 'is_carpool',
                    'width' => 20,
                    'wordWrap' => false,
                    'type' => 'checkbox',
                    'checkedTemplate' => 'Yes',
                    'uncheckedTemplate' => 'No',
                    'className' => 'htMiddle htCenter'
                ],
            ],
            'header' => ['Id', 'VEHICLE TYPE', 'VEHICLE NO', 'INTERNAL NO', 'IS CARPOOL'],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        try {
            foreach ($details as $detail) {
                $data = [
                    'vehicle_type' => $detail['vehicle_type'],
                    'vehicle_no' => $detail['vehicle_no'],
                    'internal_no' => $detail['internal_no'],
                    'is_carpool' => (isset($detail['is_carpool'])) ? $detail['is_carpool'] : 'No',
                    // 'created_by' => $request->user()->id,
                    'created_at' => date('Y-m-d'),
                    'updated_at' => date('Y-m-d'),
                ];

                $form = Vehicle::where('id', '=', $detail['id'])->first();

                if (!$form) {
                    $data = array_merge($data, ['created_by' => $request->user()->id]);
                    Vehicle::create($data);
                } else {
                    if ($form->created_by == $request->user()->id) {
                        $form->update($data);
                    }
                }
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /* Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = Vehicle::where('id', '=', $id)->first();
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param Config $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Config $productBrand)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            Vehicle::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
