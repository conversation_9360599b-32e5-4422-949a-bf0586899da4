<?php

namespace App\Services\Reports;

use App\Models\User\UserItmGrp;
use App\Services\SapS4Service;

class ReportStockItemService
{
    public function show($request)
    {
        $service = new SapS4Service();

        $service->login();

        $item_groups = null;
        $whs = null;
        $displayNetwork = true;
        if ($request->user()->hasRole('Admin E-RESEVATION IT HARDWARE')) {
            $item_groups = 'ZITS';
            $whs = 'IG03';
            $displayNetwork = false;
        } elseif ($request->user()->hasRole('View Report ATK')) {
            $item_groups = 'ZOFS';
            $whs = 'IG02';
        } elseif ($request->user()->hasRole('Admin E-RESEVATION SAFETY')) {
            $item_groups = 'ZSAF';
            $whs = 'IG04';
        } elseif ($request->user()->hasRole('Admin Reservasi Carpool')) {
            $item_groups = 'ZTRD';
            $whs = 'IG07';
        } elseif ($request->user()->hasRole('Superuser')) {
            $item_groups = ['ZITS', 'ZITS', 'ZOFS', 'ZTRD'];
            $whs = ['IG03', 'IG02', 'IG04', 'IG07'];
        }

        $items = $service->getMaterial(1, 10000, null, $item_groups, null, null, $whs);

        $rows = [];
        if ($items) {
            if (!array_key_exists('DATA', $items)) {
                return response()->json([
                    'rows' => [],
                    'message' => $items['MESSAGE']
                ]);
            }
            $rows = $service->transformItemDataFromS4ReportStock($items, $displayNetwork);
        }
        return $rows;
    }

    public function header()
    {
        return [
            'Item Code',
            'Item Name',
            'UoM',
            'MinLevel',
            'OnHand',
            'SubGroup Name'
        ];
    }

    public function columns()
    {
        return [
            [
                'data' => "ItemCode",
                'width' => 100,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "ItemName",
                'width' => 200,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "InvntryUom",
                'width' => 50,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "MinLevel",
                'width' => 50,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "Available",
                'width' => 50,
                'wordWrap' => false,
                'readOnly' => true,
            ],
            [
                'data' => "SubGroupName",
                'width' => 200,
                'wordWrap' => false,
                'readOnly' => true,
            ],
        ];
    }

    public function store($rows_data)
    {
        $rows = [];
        foreach ($rows_data as $value) {
            $rows[] = [
                $value['ItemCode'],
                $value['ItemName'],
                $value['InvntryUom'],
                $value['MinLevel'],
                $value['Available'],
                $value['SubGroupName'],
            ];
        }

        return [
            'header' => $this->header(),
            'rows' => $rows
        ];
    }
}
