<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\User\UserWhs
 *
 * @property int $id
 * @property string $db_code
 * @property string $whs_code
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs whereDbCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWhs whereWhsCode($value)
 * @mixin \Eloquent
 */
class UserWhs extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $connection = 'sqlsrv';
}
