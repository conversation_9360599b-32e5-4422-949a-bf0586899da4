<?php

namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Approval\Approval
 *
 * @property int $id
 * @property string $name
 * @property string $final_status
 * @property string|null $callback
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property string|null $speciment
 * @property string $show_speciment
 * @property string|null $company
 * @property array|null $department
 * @property array|null $document_type
 * @property array|null $requester
 * @property int $priority
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Approval\ApprovalApprover> $approver
 * @property-read int|null $approver_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Approval\ApprovalRule> $rules
 * @property-read int|null $rules_count
 * @method static \Illuminate\Database\Eloquent\Builder|Approval newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Approval newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Approval query()
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereCallback($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereDocumentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereFinalStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereRequester($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereShowSpeciment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereSpeciment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereUpdatedAt($value)
 * @property string|null $has_signer
 * @method static \Illuminate\Database\Eloquent\Builder|Approval whereHasSigner($value)
 * @mixin \Eloquent
 */
class Approval extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $casts = [
        'department' => 'array',
        'document_type' => 'array',
        'requester' => 'array',
    ];

    public function rules()
    {
        return $this->hasMany(ApprovalRule::class);
    }

    public function approver()
    {
        return $this->hasMany(ApprovalApprover::class);
    }
}
