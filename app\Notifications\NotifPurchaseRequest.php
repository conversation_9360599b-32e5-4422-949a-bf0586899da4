<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use App\Services\ReservationService;

class NotifPurchaseRequest extends Notification implements ShouldQueue
{
    use Queueable;

    public $content;
    public $receiver;
    public $cc_email;

    /**
     * Create a new message instance.
     *
     * @param $content
     */
    public function __construct($receiver, $content, $cc_email)
    {
        $this->content = $content;
        $this->receiver = $receiver;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        Log::info('notif PR');

        $service = new ReservationService();
        $attachment = public_path($service->printDocument($this->content['header'], 'all', $this->content['header']->Requester));

        $content = array_merge([
            'attachment' => $attachment
        ], $this->content);

        return (new MailMessage)
                    ->cc($this->cc_email)
                    // ->from('<EMAIL>')
                    // ->from('<EMAIL>')
                    ->subject($content['subject'])
                    ->attach($content['attachment'])
                    ->markdown('email.pr', [
                        'receiver' => $this->receiver,
                        'content' => $content,
                        'header' => $content['header']
                    ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
