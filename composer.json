{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "ably/ably-php": "^1.1", "doctrine/dbal": "^3.6", "elasticsearch/elasticsearch": "^8.15", "friendsofphp/php-cs-fixer": "^3.0", "grkamil/laravel-telegram-logging": "^1.10", "inertiajs/inertia-laravel": "^1.0", "intervention/image": "^2.7.2", "laravel/framework": "^11.38.2", "laravel/sanctum": "^4.0", "laravel/telescope": "^5.0", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.28", "league/flysystem-sftp-v3": "^3.0", "lsnepomuceno/laravel-a1-pdf-sign": "^1.2", "maatwebsite/excel": "^3.1", "madnest/madzipper": "^1.4", "owen-it/laravel-auditing": "^13.6.4", "phpoffice/phpword": "^1.2.0", "predis/predis": "^2.1", "pusher/pusher-php-server": "^7.2", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-permission": "^6.3.0", "squizlabs/php_codesniffer": "^3.6", "vinkla/hashids": "^12.0", "webklex/laravel-pdfmerger": "^1.3.1", "ytake/laravel-fluent-logger": "^7.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.1.0", "fakerphp/faker": "^1.9.1", "laravel/breeze": "^2.0", "laravel/pint": "^1.0.0", "laravel/sail": "^1.0.1", "laravel/telescope": "^5.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}, "github-oauth": {}}, "minimum-stability": "beta", "prefer-stable": true}