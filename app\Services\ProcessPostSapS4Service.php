<?php

namespace App\Services;

use App\Models\Common\Attachment;
use App\Models\Master\ApiResponse;
use App\Models\Master\MappingUom;
use App\Models\Master\MappingWhs;
use App\Models\View\ViewEmployee;
use App\Models\User;
use App\Traits\AppConfig;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProcessPostSapS4Service
{
    use AppConfig;
    public function store($header, $details)
    {
        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $postAction = $this->getConfigByName('SapPostData', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $curl = curl_init();

        $viewEmployee = ViewEmployee::where("Nik", $header->Requester)
            ->select("Nik", "Department")
            ->first();

        $params = array_merge($this->paramsHeaderSap($header, $viewEmployee), [
            "ITEMS" => $this->paramsDetailsSap($header, $details, $viewEmployee)
        ]);

        // Log::info("params submit s4: ", [
        //     "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
        //     "params" => $params
        // ]);

        curl_setopt_array(
            $curl,
            array(
                CURLOPT_URL => $baseUrl . $postAction . '?sap-client=' . $clientId,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => 0,
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($params),
                CURLOPT_HTTPHEADER => array(
                    'x-csrf-token: ' . $this->getConfigByName('SapS4Token', 'SAPS4'),
                    'Content-Type: application/json',
                    'Authorization: Basic ' . $this->getConfigByName('SapS4Token', 'SAPS4'),
                    'Cookie: ' . $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
                ),
            )
        );

        $response = curl_exec($curl);

        curl_close($curl);
        $response = json_decode($response, true);

        Log::info("response submit sap s4 " . $header->DocNum, [
            // 'response' => array_change_key_case($response)
        ]);

        if (!$response) {
            ApiResponse::create([
                'message' => 'Error',
                "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
                'params' => json_encode($params),
                'response' => json_encode($response),
                'reservation_number' => $header->DocNum
            ]);
        } elseif ($this->getTransType($header) == '3') {
            ApiResponse::create([
                'message' => 'Success',
                "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
                'params' => json_encode($params),
                'response' => json_encode($response),
                'reservation_number' => $header->DocNum
            ]);

            DB::connection('sqlsrv')
                ->table('resv_headers')
                ->where('U_DocEntry', '=', $header->U_DocEntry)
                ->update([
                    'ApprovalStatus' => 'Y',
                    'DocStatus' => 'O'
                ]);

            return [
                'error' => false
            ];
        } else {
            if ($response['DATA_MESSAGE'][0]['STATUS'] == 'ERROR') {
                ApiResponse::create([
                    'message' => 'Error',
                    "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
                    'params' => json_encode($params),
                    'response' => json_encode($response),
                    'reservation_number' => $header->DocNum
                ]);

                DB::connection('sqlsrv')
                    ->table('resv_headers')
                    ->where('U_DocEntry', '=', $header->U_DocEntry)
                    ->update([
                        'ApprovalStatus' => 'Y',
                        'DocStatus' => 'O'
                    ]);


                return [
                    'error' => true,
                    "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
                    "params" => $params,
                    "response" => $response
                ];

                // throw new \Exception(json_encode([
                //     "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
                //     "params" => $params,
                //     "response" => $response
                // ]));
            } else {
                if (array_key_exists('RESERVATION', $response['DATA_MESSAGE'][0])) {
                    ApiResponse::create([
                        'message' => 'Success',
                        "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
                        'params' => json_encode($params),
                        'response' => json_encode($response),
                        'reservation_number' => $header->DocNum
                    ]);

                    DB::connection('sqlsrv')
                        ->table('resv_headers')
                        ->where('U_DocEntry', '=', $header->U_DocEntry)
                        ->update([
                            'SAP_GIRNo' => $response['DATA_MESSAGE'][0]['RESERVATION'],
                            'ApprovalStatus' => 'Y',
                            'DocStatus' => 'O',
                            'ExpiredDate' => now()->addDays(7)
                        ]);

                    return [
                        'error' => false,
                        "response" => $response
                    ];
                } else {
                    ApiResponse::create([
                        'message' => 'Success',
                        "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
                        'params' => json_encode($params),
                        'response' => json_encode($response),
                        'reservation_number' => $header->DocNum
                    ]);

                    DB::connection('sqlsrv')
                        ->table('resv_headers')
                        ->where('U_DocEntry', '=', $header->U_DocEntry)
                        ->update([
                            'SAP_PRNo' => $response['DATA_MESSAGE'][0]['NOPR'],
                            'ApprovalStatus' => 'Y',
                            'DocStatus' => 'O'
                        ]);

                    return [
                        'error' => false,
                        "response" => $response
                    ];
                }
            }
        }

        // if ($this->getConfigByName('TestingS4', 'SAPS4') == 'Y') {
        //     throw new \Exception(json_encode([
        //         "url" => $baseUrl . $postAction . '?sap-client=' . $clientId,
        //         "params" => $params,
        //         "response" => $response
        //     ]));
        // }

        return [
            'error' => false
        ];
    }

    /**
     * Retrieves the transaction type based on the provided header.
     *
     * @param mixed $header The header object containing RequestType and DocumentType properties.
     * @return string The transaction type: '3' for Restock, '1' for Service, '2' for other cases.
     */
    public function getTransType($header)
    {
        if(str($header->Requester)->contains(['88100102'])) {
            return '2';
        } elseif (Str::contains($header->WorkLocation, ['JAKARTA'])) {
            return '1';
        } else {
            if ($header->CategoryType == 'Triwulan') {
                return '2';
            } elseif ($header->RequestType == 'Restock' || $header->ItemType == 'Asset' || $header->ItemType == 'Service') {
                return '3';
            } else {
                return '2';
            }
        }
    }


    /**
     * Retrieves the PR type based on the document type and item type.
     *
     * @param mixed $header The header object containing the document type and item type.
     * @return string The PR type ('ZIRG', 'ZIRA', or empty string).
     */
    protected function getPrType($header)
    {
        $user = User::where("username", $header->Requester)->first();
        if (!$user) {
            $user = User::where("username", $header->CreatedBy)->first();
        }
        if ($header->DocumentType == 'Service') {
            if ($user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP']) && $header->Company == 'IMIP_LIVE') {
                if ($header->ItemType == 'Service') {
                    return 'ZIRG';
                } else if ($header->ItemType == 'Asset') {
                    return 'ZIRA';
                }
            } elseif ($user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP']) && $header->Company == 'BDM_LIVE') {
                if ($header->ItemType == 'Service') {
                    return 'ZDRG';
                } else if ($header->ItemType == 'Asset') {
                    return 'ZDRA';
                }
            } elseif (Str::contains($header->WorkLocation, ['IMIP JAKARTA', 'BDT JAKARTA', 'RAS JAKARTA'])) {
                if ($header->ItemType == 'Service') {
                    return 'ZIRG';
                } else if ($header->ItemType == 'Asset') {
                    return 'ZIRA';
                }
            } elseif (Str::contains($header->WorkLocation, ['BDM JAKARTA'])) {
                if ($header->ItemType == 'Service') {
                    return 'ZDRG';
                } else if ($header->ItemType == 'Asset') {
                    return 'ZDRA';
                }
            } elseif (Str::contains($header->WorkLocation, ['BDW JAKARTA'])) {
                if ($header->ItemType == 'Service') {
                    return 'ZWRG';
                } else if ($header->ItemType == 'Asset') {
                    return 'ZWRA';
                }
            } else {
                return '';
            }
        } else {
            return '';
        }
    }

    /**
     * Retrieves an attachment based on the source ID and type.
     *
     * @param int $sourceId The ID of the source.
     * @param string $type The type of the attachment.
     * @return Attachment|null The attachment object if found, null otherwise.
     */
    protected function getAttachment($sourceId, $type)
    {
        return Attachment::where('source_id', '=', $sourceId)
            ->where('type', '=', $type)
            ->first();
    }

    /**
     * Converts a given date to SAP date format.
     *
     * @param string $date The date to be converted.
     * @return string The converted date in SAP date format.
     */
    protected function convertToSapDate($date)
    {
        return str_replace('-', '', $date);
    }


    protected function paramsHeaderSap($header, $viewEmployee)
    {
        $transType = $this->getTransType($header);
        $attachment = $this->getAttachment($header->U_DocEntry, 'reservation_header');
        $user = User::where("username", $header->Requester)->first();
        if (!$user) {
            $user = User::where("username", $header->CreatedBy)->first();
        }
        $slock = 'IMIP JAKARTA';
        if (Str::contains($header->WorkLocation, ['JAKARTA']) && Str::contains($header->Company, ['BDW'])) {
            $slock = 'BDW JAKARTA';
        } elseif (Str::contains($header->WorkLocation, ['MOROWALI']) && Str::contains($header->Company, ['BDW'])) {
            $slock = 'BDW MOROWALI';
        } elseif ($user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP']) && $header->Company == 'IMIP_LIVE') {
            $slock = 'IMIP JAKARTA';
        } elseif ($user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP']) && $header->Company == 'BDM_LIVE') {
            $slock = 'BDM JAKARTA';
        } elseif ($user->hasAnyRole(['Admin E-RESERVATION JETTY BDM IMIP']) && $header->Company == 'IMIP_LIVE') {
            $slock = 'IMIP MOROWALI';
        } elseif ($user->hasAnyRole(['Admin E-RESERVATION JETTY BDM IMIP']) && $header->Company == 'BDM_LIVE') {
            $slock = 'BDM MOROWALI';
        } else {
            $slock = $header->WorkLocation;
        }


        return [
            "TRANSTYPE" => $transType,
            // "ZLOC" => (Str::contains($header->WorkLocation, ['JAKARTA'])) ? 'IMIP JAKARTA' : $header->WorkLocation,
            "ZLOC" => $slock,
            "PR_TYPE" => $this->getPrType($header),
            "RS_RES_DATE" => $this->convertToSapDate($header->DocDate),
            "RS_ZZCAT_SHIFT" => "",
            "RS_ZZCAT_PORTION" => "",
            "RS_ZZDUE_DATE" => $this->convertToSapDate($header->RequiredDate),
            "RS_ZZREMARKS" => $header->Memo,
            "RS_ZZREQUESTOR" => $header->RequesterName,
            "RS_ZZDEPT" => $viewEmployee->Department,
            "PR_ZZATTACHMENT" => (isset($attachment)) ? $attachment->file_path : '',
            "RS_MOVETYPE" => ($header->Usage) ? $header->Usage : '',
            "COST_CTR" => ($header->CostCenter) ? $header->CostCenter : '',
        ];
    }

    /**
     * Retrieves the account type based on the given header and detail.
     *
     * @param mixed $header The header object.
     * @param mixed $detail The detail object.
     * @return string The account type.
     */
    protected function getAccType($header, $detail)
    {
        // if ($header->ItemType == 'Asset') {
        //     return 'A';
        // } else if($detail->OrderId) {
        //     if (Str::contains($header->WorkLocation, ['JAKARTA'])) {
        //         return 'J';
        //     } else {
        //         return 'F';
        //     }
        // } else if ($header->ItemType == 'Service') {
        //     if (Str::contains($header->WorkLocation, ['JAKARTA'])) {
        //         return 'L';
        //     } else {
        //         return 'K';
        //     }
        // } else if (Str::contains($header->ItemType, ['Ready Stock', 'Non Ready Stock'])) {
        //     if (Str::contains($header->WorkLocation, ['JAKARTA']) && $header->CostType == 'Cost') {
        //         return 'L';
        //     } else {
        //         return '';
        //     }
        // } else {
        //     return '';
        // }

        $plant = $this->getPlan($header);

        if ($header->ItemType == 'Asset') {
            return 'A';
        } else if ($detail->OrderId) {
            if (Str::contains($plant, ['IM01', 'BD01', 'BW01'])) {
                return 'F';
            } else {
                return '';
            }
        } else if ($header->ItemType == 'Service') {
            if (Str::contains($plant, ['IM01', 'BD01', 'BW01'])) {
                return 'K';
            } else {
                return 'L';
            }
        } else if (Str::contains($header->ItemType, ['Ready Stock', 'Non Ready Stock'])) {
            if (Str::contains($plant, ['IM01', 'BD01', 'BW01']) && $header->CostType == 'Cost') {
                return 'K';
            } else {
                return '';
            }
        } else {
            return '';
        }
    }

    public function mappingWarehouse($whs)
    {
        $mappingWhs = MappingWhs::where('whs_code_b1', $whs)->first();
        if ($mappingWhs) {
            return $mappingWhs->whs_code_s4;
        }
        return $whs;
    }

    protected function mappingUom($uom)
    {
        $mappingUom = MappingUom::where('uom_code_b1', Str::upper($uom))->first();
        if ($mappingUom) {
            return $mappingUom->uom_code_s4;
        }
        return $uom;
    }

    protected function getPlan($header)
    {
        $service = new SapS4Service();
        $user = User::where("username", $header->Requester)->first();
        if (!$user) {
            $user = User::where("username", $header->CreatedBy)->first();
            $company = $user->company;
        } else {
            $company = $user->company;
        }
        $sloc = $service->getSloc(1, 1000, null, $company, $user, $header);
        $plant = '';
        $whsCodeHeader = $this->mappingWarehouse($header->WhsCode);
        if (array_key_exists("DATA", $sloc)) {
            foreach ($sloc['DATA'] as $item) {
                if ($whsCodeHeader == $item['LGORT']) {
                    $plant = $item['WERKS'];
                }
            }
        }

        return $plant;
    }

    /**
     * @param mixed $header
     * @param mixed $details
     */
    protected function paramsDetailsSap($header, $details, $viewEmployee)
    {
        $items = [];
        $user = User::where("username", $header->Requester)->first();
        if (!$user) {
            $user = User::where("username", $header->CreatedBy)->first();
        }
        $plant = $this->getPlan($header);

        // info('plan', [
        //     'plan' => $plant,
        //     'whsCodeHeader' => $whsCodeHeader,
        //     // 'sloc' => $dataSloc,
        // ]);

        foreach ($details as $detail) {
            $whsCodeDetail = $this->mappingWarehouse($detail->WhsCode);
            $items[] = [
                "ORDERID" => $detail->OrderId,
                "ACCTASSCAT" => $this->getAccType($header, $detail),
                "SPB_TYPE" => $header->RequestType,
                // "RS_REQTYPE" => ($detail->SPB == 'Y') ? 'SPB' : 'NPB',
                "RS_REQTYPE" => $detail->RequestType,
                // "PR_PUR_GROUP" => (Str::contains($header->WorkLocation, ['JAKARTA'])) ? "I02" : "I00",
                "PR_PUR_GROUP" => $this->getPrPurGroup($header, $user),
                "MATERIAL" => $detail->ItemCode,
                "PLANT" => $plant,
                "DELIV_DATE" => $this->convertToSapDate($header->RequiredDate),
                "STORE_LOC" => $whsCodeDetail,
                "PR_TRACKINGNO" => $header->DocNum,
                "QUANTITY" => $detail->ReqQty,
                // "QUANTITY" => $header->ReqQty,
                "PR_ZZDEPT" => $viewEmployee->Department,
                // "RS_ZZRESNO" => ($header->DocumentType == 'Item') ? $header->DocNum : "",
                "RS_ZZRESNO" => $header->DocNum,
                "ZZIRES" => $detail->LineNum,
                "ZZATTACHMENT" => $detail->U_ATTACH,
                "PR_ZZREQUESTOR" => $header->RequesterName,
                "ASSET_NO" => ($this->getAccType($header, $detail) == 'A') ? $detail->AssetCode : "",
                "ASSET_SUBNO" => "",
                "TEXT_LINE" => $detail->ReqNotes,
                "PR_DESC" => ($header->CategoryType == 'Triwulan') ? "" : (($header->RequestType == 'Restock' || $header->ItemType == 'Asset' || $header->ItemType == 'Service') ? $detail->ItemName : ""),
                "PR_UOM" => ($header->DocumentType == 'Service') ? $this->mappingUom($detail->UoMCode) : "",
                "PREQ_DATE" => $this->convertToSapDate($header->DocDate)
            ];
        }

        return $items;
    }

    protected function getPrPurGroup($header, $user)
    {
        if ($user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP'])) {
            return 'I02';
        } elseif (Str::contains($header->WorkLocation, ['IMIP JAKARTA', 'BDM JAKARTA', 'RAS JAKARTA'])) {
            return 'I02';
        } elseif ($user->hasAnyRole(['Admin E-RESERVATION JETTY BDM IMIP']) && $header->Company == 'IMIP_LIVE') {
            return 'I00';
        } elseif ($user->hasAnyRole(['Admin E-RESERVATION JETTY BDM IMIP']) && $header->Company == 'BDM_LIVE') {
            return 'B00';
        } elseif (Str::contains($header->WorkLocation, ['IMIP MOROWALI', 'BDT MOROWALI'])) {
            return 'I00';
        } elseif (Str::contains($header->WorkLocation, ['BDM MOROWALI', 'BDW MOROWALI'])) {
            return 'B00';
        }
    }
}
