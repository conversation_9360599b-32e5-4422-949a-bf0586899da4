<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateConfigsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('configs', function (Blueprint $table) {
            $table->id();
            $table->string('key', 200);
            $table->string('value', 200);
            $table->string('label', 200)->nullable();
            $table->string('type', 200)->nullable();
            $table->timestamps();
        });

        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('document_number', 150)->unique();
            $table->string('filename', 200);
            $table->string('filepath')->nullable();
            $table->string('extension', 200)->nullable();
            $table->string('type', 200)->nullable();
            $table->unsignedInteger('value')->default(10000);
            $table->date('document_date')->nullable();
            $table->text('meta')->nullable();
            $table->string('location')->nullable();
            $table->string('password')->nullable();
            $table->string('reason')->nullable();
            $table->string('specimen_path')->nullable();
            $table->string('profile_name')->nullable();
            $table->string('vis_llx')->nullable();
            $table->string('vis_lly')->nullable();
            $table->string('vis_urx')->nullable();
            $table->string('vis_ury')->nullable();
            $table->string('identity_type')->nullable();
            $table->string('identity_number')->nullable();
            $table->string('identity_name')->nullable();
            $table->smallInteger('signature_page')->nullable();
            $table->text('jwt_token')->nullable();
            $table->text('ref_token')->nullable();
            $table->unsignedBigInteger('temp_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('configs');
        Schema::dropIfExists('documents');
    }
}
