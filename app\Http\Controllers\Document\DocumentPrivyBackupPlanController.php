<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Models\Common\Attachment;
use App\Models\Document\Document;
use App\Services\DocumentService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocumentPrivyBackupPlanController extends Controller
{
    public function store(Request $request)
    {
        $errorDocument = Document::whereIn('privy_status', ['error', 'uploaded'])
            ->limit(1)
            ->get();

        $service = new DocumentService();

        foreach ($errorDocument as $key => $doc) {
            $attachment = Attachment::where('source_id', $doc->id)
                ->where('type', 'peruri')
                ->first();

            $file_name = $attachment->file_name;
            $str = explode(".", $file_name);
            $file_name = (Str::contains($file_name, '_token_'))
                ? substr($file_name, 0, -19) . '_token_' . Str::random(8) . '.pdf'
                : $str[0] . '_token_' . Str::random(8) . '.pdf';

            $document = Document::create([
                "document_number" => $service->generateDocNum(date('Y-m-d H:i:s'), $doc->type),
                "filename" => $doc->filename,
                "filepath" => $doc->filepath,
                "extension" => $doc->extension,
                "type" => $doc->type,
                "value" => $doc->value,
                "document_date" => $doc->document_date,
                "meta" => $doc->meta,
                "location" => $doc->location,
                "password" => $doc->password,
                "reason" => $doc->reason,
                "specimen_path" => $doc->specimen_path,
                "profile_name" => $doc->profile_name,
                "vis_llx" => $doc->vis_llx,
                "vis_lly" => $doc->vis_lly,
                "vis_urx" => $doc->vis_urx,
                "vis_ury" => $doc->vis_ury,
                "identity_type" => $doc->identity_type,
                "identity_number" => $doc->identity_number,
                "identity_name" => $doc->identity_name,
                "signature_page" => $doc->signature_page,
                "jwt_token" => $doc->jwt_token,
                "temp_id" => $doc->temp_id,
                "created_at" => Carbon::now(),
                "type_no" => $doc->type_no,
                "status" => 'approved - failed',
                "vis_digisign_llx" => $doc->vis_digisign_llx,
                "vis_digisign_lly" => $doc->vis_digisign_lly,
                "vis_digisign_urx" => $doc->vis_digisign_urx,
                "vis_digisign_ury" => $doc->vis_digisign_ury,
                "materai_page" => $doc->materai_page,
                "sign_page" => $doc->sign_page,
                "created_by" => $doc->created_by,
                "updated_by" => $doc->updated_by,
                "company" => $doc->company,
                "customer_id" => $doc->customer_id,
                "document_sub_type_id" => $doc->document_sub_type_id,
                "remark" => $doc->remark,
                "customer_name" => $doc->customer_name,
                "external_document_number" => $doc->external_document_number . 'rev',
                "digisign_coordinate" => $doc->digisign_coordinate,
                "digisign_top" => $doc->digisign_top,
                "digisign_left" => $doc->digisign_left,
                "digisign_width" => $doc->digisign_width,
                "digisign_height" => $doc->digisign_height,
                "meterai_coordinate" => $doc->meterai_coordinate,
                "meterai_top" => $doc->meterai_top,
                "meterai_left" => $doc->meterai_left,
                "meterai_width" => $doc->meterai_width,
                "meterai_height" => $doc->meterai_height,
                "coordinate_document_path" => $doc->coordinate_document_path,
                "digisign_id" => $doc->digisign_id,
                "digisign_color" => $doc->digisign_color,
                "digisign_stamp_type" => $doc->digisign_stamp_type,
                "digisign_page_index" => $doc->digisign_page_index,
                "meterai_id" => $doc->meterai_id,
                "meterai_color" => $doc->meterai_color,
                "meterai_stamp_type" => $doc->meterai_stamp_type,
                "meterai_page_index" => $doc->meterai_page_index,
                "total" => $doc->total,
                "document_type" => $doc->document_type,
                "approver" => $doc->approver,
                "sign_payment" => $doc->sign_payment,
                "approval_id" => $doc->approval_id,
                "internal_document" => $doc->internal_document,
                "str_url" => Str::random(120),
            ]);


            Storage::disk('app_documents')->copy('Attachment/docs/' . $attachment->file_name, 'Attachment/docs/' . $file_name);

            // data attachment
            $data = [
                'file_name' => $file_name,
                'file_path' => config('app.url') . '/Attachment/docs/' . $file_name,
                'source_id' => (int) $document->id,
                'str_url' => $document->id,
                'created_by' => $doc->created_by,
                'type' => 'peruri'
            ];

            // save attachment
            $attach = Attachment::create($data);
        }
    }
}
