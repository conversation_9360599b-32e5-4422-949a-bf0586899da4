<?php

namespace App\Services;

use App\Models\Settings\RegisterPrivy;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class RegisterPrivyService
{
    use ApiResponse;
    use AppConfig;

    /**
     * Returns an array of form data for a document based on the provided request and type.
     *
     * @param mixed $request The request data to be used for the form data.
     * @param string $type The type of form data to be returned, either 'store' or 'update'.
     * @param int|null $id The ID of the document being updated, if applicable.
     * @return array The array of form data to be used for the document.
     */
    public function formData($request, $type, $id = null): array
    {
        $data = $request->all();
        Arr::forget($data, 'coordinate');
        Arr::forget($data, 'sign_payments');
        Arr::forget($data, 'id');


        if ($type == 'store') {
            $data['reference_number'] = $this->generateDocNum();
            $data['created_by'] = auth()->user()->id;
            $data['created_at'] = Carbon::now();
        } else {
            $data['updated_by'] = auth()->user()->id;
            $data['created_at'] = Carbon::parse($request->created_at);
            $data['updated_at'] = Carbon::now();
        }

        return $data;
    }

    /**
     * Generates a document number for the PHP function.
     *
     * @return string The generated document number.
     */
    protected function generateDocNum()
    {
        $doc_num = RegisterPrivy::selectRaw('reference_number as code')
            ->orderBy('reference_number', 'DESC')
            ->first();

        $number = empty($doc_num) ? '0000000000' : $doc_num->code;
        $clear_doc_num = (int)substr($number, 7, 12);
        $number = $clear_doc_num + 1;

        return 'IMIPPRV' . sprintf('%05s', $number);
    }

    /**
     * Get the form.
     *
     * @return array The form object.
     */
    public function getForm()
    {
        $form = $this->form('register_privies');
        $form['reference_number'] = $this->generateDocNum();
        $form['channel_id'] = $this->getConfigByName('PrivyChannelId', 'PRIVY');

        return $form;
    }

    public function register($row)
    {
        $service = new ApprovalPrivyService();
        $service->login();

        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('PrivyRegisterUrl', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');
        $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $this->signature($row, $timestamp, 'POST'),
                'Request-ID' => strtotime($timestamp),
            ])
            ->post($url, $this->registerParams($row))
            ->throw(function ($response, $e) {
                throw new \Exception('E-DIGITAL SIGN: ' . json_decode($response->collect()), 1);
            });

        if ($response->collect()['data']) {
            $data = RegisterPrivy::find($row->id);
            $data->register_token = $response->collect()['data']['register_token'];
            $data->status = $response->collect()['data']['status'];
            $data->save();
        }

        return $response->collect();
    }

    public function statusParams($row)
    {
        return [
            "reference_number" => $row->reference_number,
            "channel_id" => $row->channel_id,
            "register_token" => $row->register_token,
            "info" => $row->info
        ];
    }

    protected function registerParams($row)
    {
        return [
            "reference_number" => $row->reference_number,
            "channel_id" => $row->channel_id,
            "info" => $row->indo,
            "email" => $row->email,
            "phone" => $row->phone,
            "nik" => $row->nik,
            "name" => $row->name,
            "dob" => $row->dob,
            "selfie" => $row->selfie,
            "identity" => $row->identity
        ];
    }

    public function signature($row, $timestamp, $httpVerb): string
    {
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $body = $this->registerParams($row);

        Arr::forget($body, 'identity');
        Arr::forget($body, 'selfie');

        $strBody = json_encode($body);
        $strBody = str_replace(" ", "", $strBody);
        $method = $httpVerb;
        $body_md5 = base64_encode(md5($strBody, true));
        $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        $byte_key = utf8_encode($api_secret);
        $new_hmac = utf8_encode($hmac_signature);
        $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        $base64_message = base64_encode($hmac);
        $auth_string = $api_key . ":" . $base64_message;
        $signature = base64_encode($auth_string);
        return $signature;
    }
}
