<?php

namespace App\Models\Task;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Task\TaskActivity
 *
 * @property int $id
 * @property int $task_id
 * @property int $user_id
 * @property string $activity
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity whereActivity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity whereTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskActivity whereUserId($value)
 * @mixin \Eloquent
 */
class TaskActivity extends Model
{
    use HasFactory;
}
