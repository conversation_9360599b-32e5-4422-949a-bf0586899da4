<?php

namespace App\Traits;

use PDO;

trait MasterSap
{
    use ConnectHana;

    public function customer()
    {
        $db_name = $this->sapDb();

        $driver = 'HDBODBC';
        $servername2 = '***************:30015';
        $username2 = 'IMIP_ERESV';
        $password2 = 'Ereserve#1234';
        $connection2 = new PDO(
            "odbc:Driver=$driver;ServerNode=$servername2;Uid=$username2;Pwd=$password2;CHAR_AS_UTF8=true;"
        );

        $sql = '
            SELECT T0."CardName",
                T0."CardCode"
            FROM ' . $db_name . '."OCRD" AS T0
        ';


        $customer = [];
        $arr_count = [];
        $statement = $connection2->query($sql);
        while ($row_datas = $statement->fetch(PDO::FETCH_ASSOC)) {
            $customer[] = [
                "CardName" => $row_datas["CardName"],
                "CardCode" => $row_datas["CardCode"],
                // "ItemName" => utf8_decode(odbc_result($rs, "ItemName")),
            ];
        }

        // $rs = odbc_exec($this->connectHana(), $sql);

        // // return odbc_fetch_array( $rs );

        // if (!$rs) {
        //     exit("Error in SQL");
        // }

        // while (odbc_fetch_row($rs)) {
        //     $customer[] = [
        //         "CardName" => odbc_result($rs, "CardName"),
        //         "CardCode" => odbc_result($rs, "CardCode"),
        //         // "ItemName" => utf8_decode(odbc_result($rs, "ItemName")),
        //     ];
        // }

        return $customer;
    }
}
