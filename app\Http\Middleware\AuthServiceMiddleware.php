<?php

namespace App\Http\Middleware;

use App\Models\User;
use App\Models\View\ViewEmployee;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use Laravel\Sanctum\Guard;
use Laravel\Sanctum\PersonalAccessToken;

class AuthServiceMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log or print for debugging
        $authorization = $request->header('Authorization');
        $approvalToken = $request->header('X-Approvaltoken');
        $username = $request->header('X-Username');
        $accessTokenClient = $this->bearerToken($request->header('X-Access-Token'));
        $token = $accessTokenClient;

        // Log::debug('params token2: ' . $accessTokenClient);
        if (!$token || $token == 'undefined') {
            if (!$authorization) {
                return response()->json(['message' => 'Unauthorized!!'], 401);
            } else {
                $token = $this->bearerToken($authorization);
            }
        }

        // Log::debug('params check token', [
        //     'token' => $token,
        //     'authorization' => $authorization,
        // ]);

        // Extract the token ID and token value
        $tokenId = explode('|', $token, 2)[0] ?? null;

        // Find the access token using the token ID
        $accessToken = PersonalAccessToken::findToken($token);

        if (!$accessToken || !$tokenId || !$this->isValidToken($accessToken, $tokenId)) {
            return response()->json([
                'message' => 'Unauthorized!!',
                'accessToken' => $accessToken,
                'token' => $token,
                'tokenId' => $tokenId
                // 'authorization' => $authorization,
                // 'request' => $request->all()
            ], 401);
        }

        if ($accessTokenClient) {
            $user = User::where("username", $username)->first();
            if (!$user) {
                $this->insertUser($username, $approvalToken);
            } else {
                $user->cherry_token = $approvalToken;
                $user->save();
            }
        }

        // Authenticate the user using the token's associated user
        $request->setUserResolver(function () use ($accessToken) {
            return $accessToken->tokenable;
        });

        // Set the authenticated user in the request and Auth facade
        $user = $accessToken->tokenable;
        $request->setUserResolver(fn() => $user);
        Auth::setUser($user);

        return $next($request);
    }

    protected function isValidToken(PersonalAccessToken $accessToken, string $tokenId): bool
    {
        // Additional token validation logic if needed
        // For example, check if the token has a specific scope, etc.
        return true;
    }

    protected function bearerToken($header)
    {
        $position = strrpos($header, 'Bearer ');

        if ($position !== false) {
            $header = substr($header, $position + 7);

            return str_contains($header, ',') ? strstr($header, ',', true) : $header;
        }
    }

    protected function insertUser($username, $token)
    {
        $employee = ViewEmployee::where("Nik", $username)->first();
        $data = [
            'name' => $employee->Name,
            'cherry_token' => $token,
            'username' => $employee->Nik,
            'password' => bcrypt('123'),
            'email' => !empty($employee->OfficeEmailAddress) ? $employee->OfficeEmailAddress : ((!empty($employee->PrivateEmailAddress)) ? $employee->PrivateEmailAddress : strtotime(date('Y-m-d H:i:s')) . '@imip.co.id'),
            'department' => $employee->Organization,
            'company' => $employee->Company,
            'position' => $employee->Position,
            'location' => $employee->Location,
            'company_code' => $employee->CompanyCode,
            'employee_code' => $employee->EmployeeCode,
        ];

        if (User::where('username', '=', $username)->first()) {
            $user = User::where('username', '=', $username)
                ->update($data);
        } else {
            $user = User::create($data);
        }
        return $user;
    }
}
