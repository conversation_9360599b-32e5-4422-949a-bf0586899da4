<?php

namespace App\Services;

use App\Jobs\RemoveAttachment;
use App\Models\Common\Attachment;
use App\Models\Inventory\Inventory;
use App\Models\Inventory\InventoryDetail;
use App\Models\Resv\PrintNpb;
use App\Models\Resv\ResvDetail;
use App\Models\Resv\ResvHeader;
use App\Models\User;
use App\Models\View\ViewApprovalStage;
use App\Services\ConvertDocxToPdfService;
use App\Services\ProcessPostSapS4Service;
use App\Services\QrCodeService;
use App\Services\SapS4Service;
use App\Traits\ApiResponse;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use PhpOffice\PhpWord\Element\Table;
use PhpOffice\PhpWord\TemplateProcessor;
use Vin<PERSON>\Hashids\Facades\Hashids;
use App\Traits\AppConfig;
use Imagick;
use Exception;

class ReservationService
{
    use ApiResponse;
    use AppConfig;

    /**
     * @param $name
     * @return string
     */
    protected function signatureName($name): string
    {
        $without_space = str_replace(' ', '', $name);
        $limit = substr($without_space, 0, 10);
        return ucwords(strtolower($limit));
    }

    public function printDocument($form, $type, $username, $source = 'apps')
    {
        $oldLimit = ini_get('memory_limit');
        ini_set('memory_limit', '2048M');
        $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');
        // try {
        $data_header = ResvHeader::select("*")
            ->where("resv_headers.U_DocEntry", "=", $form->U_DocEntry)
            ->first();

        $db_name = $data_header->Company;

        $header = ResvHeader::select(
            "resv_headers.*",
            "resv_headers.SAP_GIRNo AS SAP_GIRNos"
        )
            ->where("resv_headers.U_DocEntry", "=", $form->U_DocEntry)
            ->first();
        // throw new \Exception(json_encode($header), 1);

        $data_letter = [];

        $requester = User::where('username', $header->CreatedBy)->first();

        $details = ResvDetail::where("U_DocEntry", "=", $header->U_DocEntry)
            // ->where("RequestType", "=", "NPB")
            ->get();

        $item_groups_safety = [];
        $count_item_safety = 0;
        $checkOrder = 0;
        foreach ($details as $index => $items) {
            // validate item safety
            $item_groups_safety[] = $items['U_AppResBy'];
            if ($items['U_AppResBy'] == 'HSE' || $header->WhsCode == 'IG04') {
                $count_item_safety++;
            }

            if ($items['OrderId']) {
                $checkOrder++;
            }
        }

        $is_request_safety = false;
        if ($count_item_safety > 0) {
            if ($count_item_safety == count($item_groups_safety) || $header->WhsCode == 'IG04') {
                $is_request_safety = true;
            }
        }

        $user = User::where("username", $header->Requester)->first();
        if (!$user) {
            $user = User::where("username", $header->CreatedBy)->first();
            $company = $user->company;
        } else {
            $company = $user->company;
        }
        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();
        // initialize post service
        $postService = new ProcessPostSapS4Service();

        $sloc = $service->getSloc(1, 1000, null, $company, $user, $header);
        $whsCodeHeader = $postService->mappingWarehouse($header->WhsCode);
        $plant = '';
        if (array_key_exists("DATA", $sloc)) {
            foreach ($sloc['DATA'] as $item) {
                if ($whsCodeHeader == $item['LGORT']) {
                    $plant = $item['WERKS'];
                }
            }
        }

        // throw new \Exception($header->CategoryType, 1);
        info("check templae", [
            "request type" => $header->RequestType,
            "contain plain" => str($plant)->contains(['IM01', 'IM02', 'IM03']),

        ]);

        if ($header->RequestType == 'Restock' && str($plant)->contains(['IM01', 'IM02', 'IM03']) && !$user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP'])) {
            $letter_template = new TemplateProcessor(
                public_path(
                    'template/RESTOCKAPPROVE.docx'
                )
            );
        } elseif ($header->RequestType == 'Restock' && str($plant)->contains(['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03']) && !$user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP'])) {
            if ($header->CategoryType == 'Triwulan') {
                $letter_template = new TemplateProcessor(
                    public_path(
                        'template/NPB-BDM-PART.docx'
                    )
                );
            } else {
                $letter_template = new TemplateProcessor(
                    public_path(
                        'template/RESTOCKAPPROVE-BDM.docx'
                    )
                );
            }
        } elseif ($header->RequestType == 'Restock' && str($plant)->contains(['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03']) && $user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP'])) {
            $letter_template = new TemplateProcessor(
                public_path(
                    'template/SPB-BDM.docx'
                )
            );
        } elseif ($header->RequestType == 'Restock' && str($plant)->contains(['IM01', 'IM02', 'IM03']) && $user->hasAnyRole(['Admin E-RESERVATION BDM to IMIP'])) {
            $letter_template = new TemplateProcessor(
                public_path(
                    'template/SPB.docx'
                )
            );
        } else if ($header->DocumentType == 'Service' && str($plant)->contains(['IM01', 'IM02', 'IM03'])) {
            if ($checkOrder > 0) {
                $letter_template = new TemplateProcessor(
                    public_path(
                        'template/SPB-ORDER.docx'
                    )
                );
            } else {
                $letter_template = new TemplateProcessor(
                    public_path(
                        'template/SPB.docx'
                    )
                );
            }
        } elseif ($header->DocumentType == 'Service' && str($plant)->contains(['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03'])) {
            $letter_template = new TemplateProcessor(
                public_path(
                    'template/SPB-BDM.docx'
                )
            );
        } elseif ($is_request_safety && str($plant)->contains(['IM01', 'IM02', 'IM03'])) {
            $letter_template = new TemplateProcessor(
                public_path(
                    'template/APD.docx'
                )
            );
        } elseif ($is_request_safety && str($plant)->contains(['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03'])) {
            $letter_template = new TemplateProcessor(
                public_path(
                    'template/NPB-BDM.docx'
                )
            );
        }
        // elseif (auth()->user()->hasAnyRole(['Superuser'])) {
        //      $letter_template = new TemplateProcessor(
        //         public_path(
        //             'template/NPB-BDM-PART.docx'
        //         )
        //     );
        // }
        elseif (str($plant)->contains(['IM01', 'IM02', 'IM03'])) {
            $letter_template = new TemplateProcessor(
                public_path(
                    'template/NPB.docx'
                )
            );
        } elseif (str($plant)->contains(['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03'])) {
            $letter_template = new TemplateProcessor(
                public_path(
                    'template/NPB-BDM-PART.docx'
                )
            );
        }

        // $details = (object)$arr;

        // throw new \Exception(json_encode($header). json_encode($details), 1);
        $textDisetujui = "";
        $textSetuju = "";
        $drf = "";
        if ($header->RequestType == 'Restock' && $header->CategoryType != 'Triwulan' && str($header->WorkLocation)->contains(['IMIP MOROWALI', 'BDM MOROWALI'])) {
            if ($checkUseNewApproval == '1') {
                $approvalEngineService = new ApprovalEngineService();
                $approvalStages = $approvalEngineService->approvalList($header, "Id", "ASC");
            } else {
                $approvalStages = ViewApprovalStage::where('DocumentReferenceID', $header->DocNum)
                    ->orderBy('Sequence', 'ASC')
                    ->get();
            }
            // $approvalStages = $this->approvalStages($request, $header);
            if (count($approvalStages) > 0) {
                $dataStages = [];
                if ($checkUseNewApproval == "1") {
                    foreach ($approvalStages as $value) {
                        $dataStages[] = [
                            'APPROVALSTAGESNAME' => $value["ResponseByName"] ??  $value['ApproverName'],
                            'APPROVALSTAGESDATE' => $value["ResponseAt"],
                        ];
                    }
                } else {
                    foreach ($approvalStages as $value) {
                        // $date = date('Y-m-d H:i:s', substr($value['ResponseDate'], 6, 10));
                        // $date = Carbon::parse($date);
                        $dataStages[] = [
                            'APPROVALSTAGESNAME' => $value->Name,
                            'APPROVALSTAGESDATE' => $value->ResponseDate,
                            // 'APPROVALSTAGESNAME' => (!empty($value['ApproverEmployeeName'])) ? $value['ApproverEmployeeName'] : '',
                            // 'APPROVALSTAGESDATE' => (!empty($value['ResponseDate'])) ? $date : '',
                        ];
                    }
                }
                if (count($dataStages) < 1) {
                    $dataStages = '';
                } else {
                    $letter_template->cloneRowAndSetValues('APPROVALSTAGESNAME', $dataStages);
                }
            } else {
                $letter_template->setValue('APPROVALSTAGESNAME', "");
                $letter_template->setValue('APPROVALSTAGESDATE', "");
            }
            if ($header->ApprovalStatus != 'Y') {
                $textDisetujui = "Disetujui Oleh : 批准者";
                $textSetuju = "( IRSAN WIDJAJA )";
                $letter_template->setValue('DRF', "DRAFT");
            } else {
                $letter_template->setValue('DRF', "");
            }
        }

        info('item type ' . $header->ItemType);
        if (str($header->ItemType)->contains(['Ready Stock', 'Asset', 'Service'])) {
            foreach ($details as $index => $item) {
                $attachment = Attachment::where('source_id', '=', $item->LineEntry)
                    ->where('type', '=', 'reservation')
                    ->first();

                $regex = "/^(.*)(\.webp|\.jfif)$/"; // Updated regex to include .jfif
                if ($attachment) {
                    $attachmentFile = '/Attachment/docs/' . $attachment->file_name;
                    create_file_delete_job($attachmentFile);
                    $attachFile = public_path($attachmentFile);
                    if (preg_match($regex, $attachFile)) {

                        $im = new Imagick($attachFile);
                        if (str($attachFile)->contains(['.webp'])) {
                            // Handle WebP files
                            $newName = str_replace('.webp', '.jpg', $attachFile);
                            // $im = imagecreatefromwebp($attachFile);
                        } elseif (str($attachFile)->contains(['.jfif'])) {
                            $newName = str_replace('.jfif', '.jpg', $attachFile);
                            // Handle JFIF files
                            // $im = imagecreatefromjpeg($attachFile);
                        }

                        if (!class_exists('Imagick')) {
                            throw new Exception('Imagick is not installed.');
                        }

                        $im->setImageFormat('jpeg');
                        $im->writeImage($newName);
                        $im->clear();
                        $im->destroy();


                        // $im = imagecreatefromwebp($attachFile);

                        // $newName = str_replace('.webp', '.jpg', $attachFile);

                        // Convert it to a jpeg file with 100% quality
                        // imagejpeg($im, $newName, 100);
                        // imagedestroy($im);
                        $image = $newName;
                        // $out=preg_replace($regex, "${1}.png", $value['IMAGE']);
                        // exec("dwebp $file -o $out");
                    } else {
                        $image = $attachFile;
                    }
                }

                $clean_item_name = preg_replace('/[^\x20-\x7E]/', '', $item->ItemName); // Remove non-UTF-8 and non-printable characters
                $clean_item_name = htmlspecialchars($clean_item_name, ENT_QUOTES | ENT_XML1, 'UTF-8'); // Escape HTML special characters

                $defaultItem = [
                    "NO" => ($index + 1),
                    "ITEMCODE" => $item->ItemCode,
                    "ORDER" => $item->OrderId,
                    "ITEMNAME" => $clean_item_name,
                    // "ITEMNAME" => str_replace('&', '&amp;', preg_replace('/[^\x00-\x7F]+/', '', $item->ItemName)),
                    // "ITEMNAME" => htmlentities($item->ItemName, ENT_QUOTES, 'UTF-8'),
                    "UOM" => $item->UoMCode,
                    "IMAGE" => ($attachment) ? $image : null,
                    "QTY" => $this->formatNumber($item->ReqQty),
                    "DATE" => $item->ReqDate,
                    "NOTES" => str_replace('&', '&amp;', $item->ReqNotes),
                    "EMPLOYEE" => (!empty($item->EmployeeName)) ? str_replace('&', '&amp;', $item->EmployeeName) : str_replace('&', '&amp;', $header->RequesterName),
                    "DEPARTMENT" => (!empty($item->U_Department)) ? str_replace('&', '&amp;', $item->U_Department) : str_replace('&', '&amp;', $header->Department),
                    "EMPLOYEEID" => (!empty($item->EmployeeId)) ? str_replace('&', '&amp;', $item->EmployeeId) : str_replace('&', '&amp;', $header->Requester),
                    "PARTNO" => null
                ];

                if (str($plant)->contains(['BD01', 'BD02', 'BD03', 'BW01', 'BW02', 'BW03'])) {
                    $itemMaterial = $service->getMaterial(1, 1000, $item->ItemCode, null, null, null, $data_header->WhsCode);
                    $partNumber = '';
                    if (array_key_exists("DATA", $itemMaterial)) {
                        foreach ($itemMaterial['DATA'] as $itemMat) {
                            if ($item->ItemCode == $itemMat['MATNR']) {
                                // info("item code print part number " .  $item->ItemCode, [
                                //     "MFRPN" => $itemMat['MFRPN'],
                                //     "MATNR" => $itemMat['MATNR'],
                                //     "itemcode" => $item->ItemCode
                                // ]);
                                // $defaultItem  = array_merge($defaultItem,  ["PARTNO" => $partNumber]);
                                $partNumber = $itemMat['MFRPN'];
                                // info("part number " . $partNumber);
                                $defaultItem["PARTNO"] = $partNumber;
                                break;
                                // $itemPart = ["PARTNO" => $partNumber];
                            }
                        }
                    }

                    // $defaultItem  = array_merge($defaultItem,  ["PARTNO" => $partNumber]);
                }

                $data_letter[] = $defaultItem;
                // info("plan " . $plant);
                // info("data_letter " . ($index + 1), $data_letter);
            }
            if ($header->RequestType == 'Restock') {
                $letter_template->cloneRow('NO', count($data_letter));
                foreach ($data_letter as $key => $value) {
                    ++$key;
                    $letter_template->setValue('NO#' . $key, $value['NO']);
                    $letter_template->setValue('ITEMCODE#' . $key, $value['ITEMCODE']);
                    $letter_template->setValue('ITEMNAME#' . $key, $value['ITEMNAME']);
                    $letter_template->setValue('UOM#' . $key, $value['UOM']);


                    // Get file extension
                    $ext = strtolower(pathinfo($value['IMAGE'], PATHINFO_EXTENSION));
                    // List of image file extensions
                    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

                    if (in_array($ext, $imageExtensions)) {
                        $letter_template->setImageValue('IMAGE#' . $key, $value['IMAGE']);
                    } else {
                        $letter_template->setValue('IMAGE#' . $key, "");
                    }
                    // $letter_template->setValue('IMAGE#' . $key, "");
                    $letter_template->setValue('QTY#' . $key, $value['QTY']);
                    $letter_template->setValue('DATE#' . $key, $value['DATE']);
                    $letter_template->setValue('NOTES#' . $key, $value['NOTES']);
                    $letter_template->setValue('PARTNO#' . $key, $value['PARTNO']);
                }
            } else {
                $letter_template->cloneRowAndSetValues('NO', $data_letter);
            }
        } else {
            if ($type == 'all') {
                $clean_item_name = preg_replace('/[^\x20-\x7E]/', '', $item->ItemName); // Remove non-UTF-8 and non-printable characters
                $clean_item_name = htmlspecialchars($clean_item_name, ENT_QUOTES | ENT_XML1, 'UTF-8'); // Escape HTML special characters
                foreach ($details as $index => $item) {
                    $data_letter[] = [
                        "NO" => ($index + 1),
                        "ITEMCODE" => $item->ItemCode,
                        "ORDER" => $item->OrderId,
                        // "ITEMNAME" => str_replace('&', '&amp;', $item->ItemName),
                        "ITEMNAME" => $clean_item_name,
                        "UOM" => $item->UoMCode,
                        "QTY" => number_format($item->ReqQty, 0),
                        "DATE" => $item->ReqDate,
                        "NOTES" => str_replace('&', '&amp;', $item->ReqNotes),
                    ];
                }
            } else {
                foreach ($details as $index => $item) {
                    $data_letter[] = [
                        "NO" => ($index + 1),
                        "ITEMCODE" => $item->ItemCode,
                        "ORDER" => $item->OrderId,
                        "ITEMNAME" => str_replace('&', '&amp;', $item->ItemName),
                        "UOM" => $item->UoMCode,
                        "QTY" => number_format($item->ReqQty, 0),
                        "DATE" => $item->ReqDate,
                        "NOTES" => str_replace('&', '&amp;', $item->ReqNotes),
                    ];
                }
                // $docs = PrintNpb::where("DocEntry", "=", sprintf("%010s", $header->SAP_GIRNos))
                //     ->whereNotNull("ItemCode")
                //     ->distinct()
                //     ->get();

                // foreach ($docs as $index => $item) {
                //     $data_letter[] = [
                //         "NO" => ($index + 1),
                //         "ITEMCODE" => $item->U_ItemCode,
                //         "ITEMNAME" => str_replace('&', '&amp;', $item->U_ItemDesc),
                //         "UOM" => $item->unitMsr,
                //         "QTY" => number_format($item->ReceiveQty, 2),
                //         "DATE" => date('Y-m-d', strtotime(substr($item->U_DocDate, 0, 10))),
                //         "NOTES" => str_replace('&', '&amp;', $item->Remark),
                //     ];
                // }
            }

            $letter_template->cloneRowAndSetValues('NO', $data_letter);
        }

        //return response()->json($data_letter);

        if ($checkUseNewApproval == '1') {
            $approvalEngineService = new ApprovalEngineService();
            $approvalStages = $approvalEngineService->approvalList($header, "Id", "ASC");
            $approvalStage = [];
            foreach ($approvalStages as $key => $value) {
                $approvalStage[] = (object) [
                    "DocumentReferenceID" => $value["ReferenceId"],
                    "Name" => $value["ResponseByName"] ??  $value['ApproverName'],
                    "ResponseDate" => $value["ResponseAt"],
                    "Sequence" => $value["Sequence"],
                    "JobPosition" => $value["ApproverJobPosition"],
                ];
            }
            $approvalStage = collect($approvalStage)->all();
        } else {
            $approvalStage = ViewApprovalStage::where('DocumentReferenceID', $header->DocNum)
                ->leftJoin('vw_employee_masterdata', 'vw_employee_masterdata.EmployeeCode', 'vw_approver_gadocuments.ApproverCode')
                ->whereNotNull('ResponseDate')
                ->select('DocumentReferenceID', 'vw_approver_gadocuments.Name', 'ResponseDate', 'Sequence', 'vw_employee_masterdata.JobPosition')
                // ->with(['employee'])
                ->distinct()
                ->orderBy('Sequence', 'asc')
                ->take(1)
                ->get();
        }


        if (($header->DocumentType == 'Service' || $header->DocumentType == 'Item') && str($header->WorkLocation)->contains(['JAKARTA'])) {

            $table = new Table(
                array(
                    'borderSize' => 0,
                    // 'borderColor' => '#000000',
                    'borderColor' => '#ffffff',
                    "layout" => "fixed",
                    "width" => 100 * 50,
                    //word in 1/50th. 1% = 50 units
                    "unit" => "pct",
                    'align' => 'center',
                    'alignment' => 'center',
                )
            );
            $table->addRow();

            $rowStyle = ["unit" => "pct", 'alignment' => 'start', 'align' => 'start', 'size' => 10];
            $signStyle = ["unit" => "pct", 'size' => 10];
            $signStyle2 = ["unit" => "pct", 'size' => 10, 'bold' => true];
            $signStyleCell = [
                "unit" => "pct",
                // 'borderBottomColor' => '000000',
                // 'borderBottomSize' => 6,
                'spacing' => 100,
                'cellMargin' => 100,
                'size' => 10,
                'bold' => true
            ];
            $signatureStyle = array('name' => 'Creattion Demo', 'size' => 22, 'bold' => true, 'valign' => 'center');

            // $table->addCell(190, ['gridSpan' => 2])->addText('Prepare by (Disiapkan oleh)', $rowStyle);
            $table->addCell(190, ['gridSpan' => (count($approvalStage) * 2), 'alignment' => 'start', 'align' => 'start'])->addText('Approved By', $rowStyle, $rowStyle);

            $table->addRow(800, array("exactHeight" => true));
            // $table->addCell(50, $rowStyle)->addText('', $rowStyle);
            // $table->addCell(190, array('valign' => 'center'))->addText($this->signatureName($paper->user_name), $signatureStyle);
            for ($i = 0; $i < count($approvalStage); $i++) {
                $table->addCell(30, $rowStyle)->addText('', $rowStyle);
                $table->addCell(190, array('valign' => 'center'))->addText((($approvalStage[$i]->ResponseDate) ? $this->signatureName($approvalStage[$i]->Name) : null), $signatureStyle);
            }

            $table->addRow();

            // $table->addCell(30, $rowStyle)->addText('Name', $rowStyle);
            // $table->addCell(190, $signStyleCell)->addText($paper->created_name, $signStyle);

            for ($i = 0; $i < count($approvalStage); $i++) {
                $table->addCell(30, $rowStyle)->addText('Name', $rowStyle);
                $table->addCell(190, $signStyleCell)->addText($approvalStage[$i]->Name, $signStyle2);
            }

            $table->addRow();

            // $table->addCell(30, $rowStyle)->addText('Title', $rowStyle);
            // $table->addCell(190)->addText(str_replace('&', '&amp;', $occupation_created), $signStyle);
            for ($i = 0; $i < count($approvalStage); $i++) {
                $table->addCell(30, $rowStyle)->addText('Position', $rowStyle);
                $table->addCell(190, $rowStyle)->addText(str_replace('&', '&amp;', $approvalStage[$i]->JobPosition), $signStyle);
            }

            $table->addRow();

            // $table->addCell(30, $rowStyle)->addText('Title', $rowStyle);
            // $table->addCell(190)->addText(str_replace('&', '&amp;', $occupation_created), $signStyle);
            for ($i = 0; $i < count($approvalStage); $i++) {
                $table->addCell(30, $rowStyle)->addText('Approved At', $rowStyle);
                $table->addCell(190, $rowStyle)->addText($approvalStage[$i]->ResponseDate, $signStyle);
            }


            $letter_template->setComplexBlock('TABLE', $table);
        }

        // get document flow
        $flow = $service->getFlow($header->DocNum);

        $girNo = null;
        $prNo = null;
        $poNo = null;
        $grNo = null;
        $giNo = null;


        if ($flow) {
            // throw new \Exception(json_encode($flow));
            if (array_key_exists('DATA', $flow)) {
                if ($flow['DATA'] != 'NULL') {
                    $girNo = (array_key_exists('RESERVASI', $flow['DATA'][0])) ? $flow['DATA'][0]['RESERVASI'] : null;

                    // $girNo =  null;
                    $prNo = (array_key_exists("PR", $flow['DATA'][0])) ? $flow['DATA'][0]['PR'] : null;
                    // $girNo = ($girNo != "") ? $girNo : $header->SAP_GIRNo;
                    // $prNo = ($prNo != "") ? $prNo : $header->SAP_PRNo;
                } else {
                    // $girNo = $header->SAP_GIRNo;
                    // $prNo = $header->SAP_PRNo;

                    $girNo = null;
                    $prNo = null;
                }
                // $poNo = $flow['DATA'][0]['PO'];
                // $grNo = $flow['DATA'][0]['GR'];
                // $giNo = $flow['DATA'][0]['GI'];
            }
        }

        $goodIssueCount = InventoryDetail::where('resv_number', $header->DocNum)->count();
        $goodIssue = Inventory::whereHas('lineItems', function ($query) use ($header) {
            $query->where('resv_number', $header->DocNum);
        })
            ->with('lineItems')
            ->orderBy("created_at", 'desc')
            ->first();

        // if ($header->DocumentType == 'Item' && !str($header->ItemType)->contains(['Service'])  && $header->RequestType != 'Restock' && !$girNo) {
        //     throw new \Exception("Cannot print document because GIR No is empty!");
        // }

        if ($header->DocumentType == 'Service' && !$prNo) {
            throw new \Exception("Cannot print document because PR No is empty!");
        }

        // $letter_template->setValue('OCCUPATION', $requester->position);
        $letter_template->setValue('TEXTDISETUJUI', $textDisetujui);
        $letter_template->setValue('TEXTSETUJU', $textSetuju);
        $letter_template->setValue('REQUESTOR', $header->RequesterName);
        $letter_template->setValue('NOERESVE', $header->DocNum);
        $letter_template->setValue('NOGIR', ($girNo) ? sprintf("%010s", $girNo) : null);
        $letter_template->setValue('NOPR', $prNo);
        $letter_template->setValue('REQUESTDATE', $header->DocDate);
        $letter_template->setValue('DEPARTMENT', str_replace('&', '&amp;', $header->Department));
        $letter_template->setValue('REQUEST_TYPE', $header->RequestType);
        $letter_template->setValue('REQUIRED_DATE', $header->RequiredDate);
        $letter_template->setValue('WHSCODE', $header->WhsCode);
        $letter_template->setValue('CREATEDBY', $header->CreatedName);
        $letter_template->setValue('REMARKS', str_replace('&', '&amp;', $header->Memo));
        $letter_template->setValue('REMAKGI', (($goodIssueCount > 0) ? 'Pengambilan ke : '
            . ($goodIssueCount + 1) . ' Pengambilan terakhir tanggal ' . $goodIssue->updated_at : ''));
        $letter_template->setValue('DATETIME', 'Print Date: ' . date('Y-m-d H:i:s'));

        if ($header->CategoryType == 'Fuel') {
            $letter_template->setValue('TEMP1', 'Vehicle No');
            $letter_template->setValue('TEMP2', 'Mileage');
            $letter_template->setValue('SEP1', ':');
            $letter_template->setValue('SEP2', ':');
            $letter_template->setValue('VEHICLENO', $header->VehicleNo);
            $letter_template->setValue('MILEAGE', $header->Mileage);
        } else {
            $letter_template->setValue('TEMP1', null);
            $letter_template->setValue('TEMP2', null);
            $letter_template->setValue('SEP1', null);
            $letter_template->setValue('SEP2', null);
            $letter_template->setValue('VEHICLENO', null);
            $letter_template->setValue('MILEAGE', null);
        }

        // $letter_template->cloneRowAndSetValues('NO', $data_letter);

        $file_path_name = public_path() . '/Attachment/NPB/' . $username . '/';

        $qrCodeService = new QrCodeService();
        $file_export_name = Hashids::encode($header->U_DocEntry);

        $qr_file = "https://eportal.imip.co.id/eresv" . '/verification?str='
            . $file_export_name . '&type=reservation';

        $qrCodeService->generatePath($file_path_name);
        // generate QR Code
        $qrPath = $file_path_name . $header->doc_num . '.png';
        $qrCodeService->generateQrCodeDoc($qr_file, $qrPath, 300);
        $letter_template->setImageValue(
            'QRCODE',
            $qrPath
        );

        $file_str = Str::upper(Str::slug($header->RequesterName . ' ' . $header->RequestType . ' ' . $header->ItemType . ' ' . $header->DocNum));
        $file_name = $file_path_name . $file_str . '.docx';

        //return response()->json($file_name);
        $letter_template->saveAs($file_name);

        $serviceConvert = new ConvertDocxToPdfService();
        $pathToSavingDirectory = $file_path_name;
        $pdfFileName = $file_str . ".pdf";
        $serviceConvert->convert($file_name, $pathToSavingDirectory, $pdfFileName);
        $pdf_file = $file_path_name . $file_str . ".pdf";

        // if ($header->RequestType != 'Restock') {
        //     $serviceConvert = new ConvertDocxToPdfService();
        //     $pathToSavingDirectory = $file_path_name;
        //     $pdfFileName = $file_str . ".pdf";
        //     $serviceConvert->convert($file_name, $pathToSavingDirectory, $pdfFileName);
        //     $pdf_file = $file_path_name . $file_str . ".pdf";
        // } else {
        //     $pdf_file = $file_path_name . $file_str . ".pdf";

        //     // Check if the Word document exists
        //     if (!file_exists($file_name)) {
        //         throw new \Exception('File does not exist: ' . $file_name);
        //     }

        //     try {
        //         $word_file = new \COM('Word.Application');
        //         $word_file->Visible = 0;
        //         $word_file->DisplayAlerts = 0;

        //         // Try to open the Word document
        //         $doc = $word_file->Documents->Open($file_name);

        //         // Check if the document opened successfully
        //         if ($doc === null) {
        //             throw new \Exception('Failed to open the document: ' . $file_name);
        //         }

        //         // Export to PDF
        //         $doc->ExportAsFixedFormat(
        //             $pdf_file,
        //             17,   // PDF format
        //             false,
        //             0,
        //             0,
        //             0,
        //             0,
        //             7,
        //             true,
        //             true,
        //             2,
        //             true,
        //             true,
        //             false
        //         );

        //         // Close the document
        //         $doc->Close(false);
        //         // Quit the Word process
        //         $word_file->Quit(false);
        //     } catch (\Exception $e) {
        //         // Handle exceptions
        //         throw new \Exception('Error: ' . $e->getMessage(), 1);
        //     } finally {
        //         // Ensure proper cleanup
        //         if (isset($word_file)) {
        //             $word_file = null;
        //         }
        //         // Optional: Force garbage collection
        //         gc_collect_cycles();
        //         unset($word_file);
        //     }
        // }

        info('file size check docnum ' . $header->DocNum, [
            'size' => filesize($pdf_file) / 1024,
            'pathToSavingDirectory' => $pathToSavingDirectory,
            'pdfFileName' => $pdfFileName,
            'filePath' => $pathToSavingDirectory . $pdfFileName
        ]);

        if ((filesize($pdf_file) / 1024) < 2) {
            throw new \Exception('Failed generate pdf file, please check pdf engine', 1);
        }
        $all_files = [
            $file_name,
            $pdf_file,
            $qrPath
        ];

        // Remove Attachment
        ini_set('memory_limit', $oldLimit);
        RemoveAttachment::dispatch($all_files)->delay(now()->addMinutes(15));

        return '/Attachment/NPB/' . $username . '/' . $file_str . ".pdf";
        // } catch (\Exception $exception) {
        //     throw new \Exception($exception->getMessage(), 1);
        // }
    }

    private function formatNumber($num)
    {
        // Check if the number has a decimal point
        if (strpos((string) $num, '.') !== false) {
            // Split the number into integer and decimal parts
            list($integer, $decimal) = explode('.', (string) $num);

            // Check if the decimal part is all zeros
            if ((int) $decimal === 0) {
                // Format with no decimal places
                return number_format($num, 0);
            } else {
                // Format with 3 decimal places
                return number_format($num, 2);
            }
        } else {
            // Format with no decimal places
            return number_format($num, 0);
        }
    }
}

