<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\User\UserDocType
 *
 * @property int $id
 * @property int $user_id
 * @property string $document_type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserDocType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserDocType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserDocType query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserDocType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserDocType whereDocumentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserDocType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserDocType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserDocType whereUserId($value)
 * @mixin \Eloquent
 */
class UserDocType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $connection = 'sqlsrv';
}
