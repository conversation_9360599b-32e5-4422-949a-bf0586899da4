<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\User\UserApp
 *
 * @property int $id
 * @property int $app_id
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserApp newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserApp newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserApp query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserApp whereAppId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserApp whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserApp whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserApp whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserApp whereUserId($value)
 * @mixin \Eloquent
 */
class UserApp extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $connection = 'sqlsrv';
}
