<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\PrintNpb
 *
 * @method static \Illuminate\Database\Eloquent\Builder|PrintNpb newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PrintNpb newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PrintNpb query()
 * @mixin \Eloquent
 */
class PrintNpb extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $connection = 'sqlsrv2';
    //    public $incrementing = false;
    protected $table = 'VIEW_PRINT_NPB';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
