<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePostingPeriodsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('posting_periods', function (Blueprint $table) {
            $table->id();
            $table->string('period_name');
            $table->date('start_period');
            $table->date('end_period');
            $table->string('is_locked', 2)->default('N');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by');
            $table->timestamps();

            $table->index(['created_by', 'updated_by']);
            $table->index(['start_period', 'end_period']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('posting_periods');
    }
}
