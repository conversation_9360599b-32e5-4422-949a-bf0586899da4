<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessSubmitApprovalDigisign;
use App\Models\Common\Attachment;
use App\Models\Document\Document;
use App\Models\View\ViewEmployee;
use App\Services\DocumentApprovalService;
use App\Traits\CherryApproval;
use App\Traits\DocumentApproval;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DocumentApprovalController extends Controller
{
    use DocumentApproval;
    use CherryApproval;
    /**
     * Processes approval request and returns appropriate response.
     *
     * @param Request $request The HTTP request object
     * @throws \Exception if an error occurs
     * @return mixed Returns either an error or success message.
     */
    public function store(Request $request)
    {
        try {
            // $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');
            $checkUseNewApproval = '0';
            if ($checkUseNewApproval == '1') {
                $id = $request->id;
                $document = Document::select(
                    'documents.*',
                    'document_number as paper_no',
                    'document_sub_types.name as document_sub_type_name'
                )
                    ->leftJoin('document_sub_types', 'document_sub_types.id', 'documents.document_sub_type_id')
                    ->with(['attachment', 'userCreate'])
                    ->where('documents.id', '=', $id)
                    ->first();

                $this->submitApproval($document, [], $request, 'e-meterai');

                return $this->success('Data Submitted');
            } else {
                $service = new DocumentApprovalService();
                $response = $service->processSubmitApproval($request, $request, 'single');


                if ($response['error']) {
                    return $this->error($response['message']);
                } else {
                    $id = $request->id;
                    $document = Document::find($id);

                    if ($document->digisign_coordinate == 'Y') {
                        $fileName = Attachment::where('source_id', $document->id)
                            ->where('type', 'peruri')
                            ->first();

                        ProcessSubmitApprovalDigisign::dispatch($document, $fileName, $request->user()->id);
                    }

                    return $this->success('Data Submitted');
                }
            }
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [
                $exception->getTraceAsString()
            ]);
        }
    }

    /**
     * Submit multiple document approvals in a batch.
     *
     * @param Request $request The HTTP request object containing the document data to submit.
     * @throws \Exception If an error occurs during processing.
     * @return JsonResponse The HTTP response object containing a success or error message.
     */
    public function batchSubmitApproval(Request $request): JsonResponse
    {
        try {
            $count_document = 0;
            $list_doc = [];
            $cc_email = [];
            foreach ($request->all() as $key => $item) {
                $count_document++;
            }

            $service = new DocumentApprovalService();

            foreach ($request->all() as $key => $item) {
                $document = $service->processSubmitApproval((object) $item, $request, 'batch');

                if ($document['error']) {
                    return $this->error($document['message']);
                }

                $itms = (object) $item;
                $id = $itms->id;
                $doc = Document::find($id);
                $list_doc[] = $doc->external_document_number;
                // $list_doc .= $doc->external_document_number .', ';
                $requester = ViewEmployee::where('Nik', $doc->userCreate->username)->first();
                $cc_email[] = $requester->OfficeEmailAddress;
            }

            $service->sendNotificationToApprover($count_document, $list_doc, $cc_email);


            return $this->success('Data Submitted');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [$exception->getTraceAsString()]);
        }
    }

    /**
     * Cancels the approval of a document based on the provided Request object.
     *
     * @param Request $request The Request object containing the document to be cancelled.
     * @throws \Exception If an error occurs during the cancellation process.
     * @return JsonResponse The JSON response containing the success or error message.
     */
    public function cancelApproval(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $service = new DocumentApprovalService();

            $service->processCancel($request, $request);
            DB::commit();

            return $this->success([], 'Document Canceled!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace(),
            ]);
        }
    }

    /**
     * Executes batch cancel for a set of documents received in the request.
     *
     * @param Request $request the HTTP request object containing the set of
     * documents to be cancelled
     * @throws \Exception if an error occurs during the execution
     * @return JsonResponse returns a JSON response containing the result of
     * the operation
     */
    public function batchCancelDocument(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $service = new DocumentApprovalService();

            foreach ($request->all() as $key => $item) {
                $service->processCancel((object) $item, $request);
            }
            DB::commit();

            return $this->success([], 'Document Canceled!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace(),
            ]);
        }
    }

    public function batchDeleteDocument(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $service = new DocumentApprovalService();

            foreach ($request->all() as $key => $item) {
                $service->processDelete((object) $item, $request);
            }
            DB::commit();

            return $this->success([], 'Document Canceled!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace(),
            ]);
        }
    }

    /**
     * Retrieves the document approval information based on the criteria provided in the request object.
     *
     * @param Request $request The HTTP request object containing the search criteria
     * @return JsonResponse Returns a JSON response containing the document approval information
     */
    public function documentApproval(Request $request): JsonResponse
    {
        $status = isset($request->searchStatus) ? (string) $request->searchStatus : 'pending';
        if ($status == 'all') {
            $status = '';
        }

        $user = $request->user()->name;
        $rows = [];


        return $this->success([
            'rows' => $this->stages($status, $user)['data'],
            'total' => $this->stages($status, $user)['total'],
            'document_status' => ['pending', 'approved', 'rejected'],
            'search_item' => ['Document Number', 'Document Type'],
        ]);
    }
}
