<table style="border-collapse: collapse;width: 100%;">
    <tr>
        <td style="border: none !important;;text-align: left;padding: 4px;">Tipe Surat</td>
        <td style="border: none !important;;text-align: left;padding: 4px;">: {{ $form->paper_type }}</td>
    </tr>
    <tr>
        <td style="border: none !important;;text-align: left;padding: 4px;">Nomor Surat</td>
        <td style="border: none !important;;text-align: left;padding: 4px;">: {{ $form->paper_no }}</td>
    </tr>
    @if(!\Illuminate\Support\Str::contains($form->paper_alias, ['abr']))
    <tr>
        <td style="border: none !important;;text-align: left;padding: 4px;">Tanggal Surat</td>
        <td style="border: none !important;;text-align: left;padding: 4px;">: {{ $form->paper_date }}</td>
    </tr>
    @endif


    @if(\Illuminate\Support\Str::contains($form->paper_alias, ['fsr']))
        <tr style="">
            <td style="border: none !important;text-align: left;padding: 4px;">Reservation For</td>
            <td style="border: none !important;text-align: left;padding: 4px;">: {{ $form->resv_for }}</td>
        </tr>
        <tr style="">
            <td style="border: none !important;text-align: left;padding: 4px;">Travel Purpose</td>
            <td style="border: none !important;text-align: left;padding: 4px;">: {{ $form->travel_purpose }}</td>
        </tr>
        <tr style="">
            <td style="border: none !important;text-align: left;padding: 4px;">Flight Origin</td>
            <td style="border: none !important;text-align: left;padding: 4px;">: {{ $form->flightOrigin->name }}</td>
        </tr>
        <tr style="">
            <td style="border: none !important;text-align: left;padding: 4px;">Flight Destination</td>
            <td style="border: none !important;text-align: left;padding: 4px;">: {{ $form->flightDestination->name }}</td>
        </tr>
        <tr style="">
            <td style="border: none !important;text-align: left;padding: 4px;">Total Seat</td>
            <td style="border: none !important;text-align: left;padding: 4px;">: {{ $form->total_seat }}</td>
        </tr>
        <tr>
            <td style="border: none !important;text-align: left;padding: 4px;">Flight Date</td>
            <td style="border: none !important;text-align: left;padding: 4px;">: {{ $form->request_date }}</td>
        </tr>
        <tr>
            <td style="border: none !important;text-align: left;padding: 4px;">Flight Cost Cover</td>
            <td style="border: none !important;text-align: left;padding: 4px;">: {{ $form->cost_cover }}</td>
        </tr>
        <tr style="padding-bottom: 20px; border: none !important;">
            <td style="border: none !important;padding: 10px;"></td>
            <td style="border: none !important;padding: 10px;"></td>
        </tr>
        <tr style="padding-top: 10px;">
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Title</th>
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Propose Passenger Name</th>
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Nationality</th>
            {{-- <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">ID No.</th> --}}
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Employee/Guest</th>
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Company Name</th>
        </tr>
        @foreach ($form->lineItems as $key => $value)
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->name_title }} </td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->name }} </td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->nationality }} </td>
                {{-- <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->id_card }} </td> --}}
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->employee_type }} </td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->company }} </td>
            </tr>
        @endforeach
    @endif

    @if(\Illuminate\Support\Str::contains($form->paper_alias, ['abr']))
        <tr style="">
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Company</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $form->host_company }}</td>
        </tr>
        <tr style="">
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">From Date</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $form->date_in }}</td>
        </tr>
        <tr style="">
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">To Date</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $form->date_out }}</td>
        </tr>
        <tr style="">
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Plan Visit Area</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $form->plan_visit_area }}</td>
        </tr>
        <tr style="">
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Total Guest</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $form->total_guest }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Reservation Date</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $form->paper_date }}</td>
        </tr>
        <tr style="padding-bottom: 20px; border: none !important;">
            <td style="border: none !important;padding: 10px;"></td>
            <td style="border: none !important;padding: 10px;"></td>
        </tr>
        <tr style="padding-top: 10px;">
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Title</th>
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Name</th>
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Company</th>
            <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Position</th>
        </tr>
        @foreach ($form->lineItems as $key => $value)
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->name_title }} </td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->name }} </td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->company }} </td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;width:50px;white-space: nowrap;">{{ $value->position }} </td>
            </tr>
        @endforeach
    @endif

    @if(\Illuminate\Support\Str::contains($form->paper_alias, ['smt', 'rtm', 'rtk', 'skt']))
        @if (count($form->lineItems) == 1)
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Perusahaan</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $paper->lineItems->first()->company }}</td>
            </tr>
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Nama</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $paper->lineItems->first()->name }}</td>
            </tr>
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">NIK</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $paper->lineItems->first()->id_card }}</td>
            </tr>
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Jabatan</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">: {{ $paper->lineItems->first()->position }}</td>
            </tr>
        @else
            <table style="border-collapse: collapse;width: 100%;">
                <tbody>
                    <tr>
                        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Nama</th>
                        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">NIK KTP</th>
                        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Departemen</th>
                        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Perusahaan</th>
                        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Jabatan</th>
                        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Nomor Telpon</th>
                        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Alamat</th>
                    </tr>
                    @foreach ($form->lineItems as $key => $value)
                        <tr>
                            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;white-space: nowrap;">{{ $value->name }} </td>
                            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;white-space: nowrap;">{{ $value->national_id }} </td>
                            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;white-space: nowrap;">{{ $value->department }} </td>
                            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;white-space: nowrap;">{{ $value->company }} </td>
                            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;white-space: nowrap;">{{ $value->position }} </td>
                            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;white-space: nowrap;">{{ $value->no_hp }} </td>
                            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;white-space: nowrap;">{{ $value->address }} </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endif
    @endif

     @if(\Illuminate\Support\Str::contains($form->paper_alias, ['sik', 'srm', 'srk', 'sim']))
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Perusahaan</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->company }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Nama</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->user_name }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">NIK</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->id_card }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Jabatan</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->occupation }}</td>
        </tr>
    @endif

    @if ($form->paper_alias == 'sik')
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Tanggal Keluar Kawasan</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->date_out }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Tujuan</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->destination }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Keterangan</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->reason }}</td>
        </tr>
    @endif

    @if ($form->paper_alias == 'sim')
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Tanggal Masuk Kawasan</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->date_in }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Keterangan</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->reason }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Vaksinasi Ke </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->swab_type }}</td>
        </tr>
        @if ($clinic_response)
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Keterangan Dari Klinik</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $clinic_response->clinic_response }}</td>
            </tr>
        @endif
    @endif
    @if ($attachment)
        @foreach ($attachment as $index => $element)
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">File {{ ($index + 1) }}</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">
                    <a href="{{ $element->file_path }}" target="_blank">{{ $element->file_name }}</a>
                </td>
            </tr>
        @endforeach
    @endif

    @if ($form->paper_alias == 'srk' || $form->paper_alias == 'srm')
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Departemen</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->department }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Pembayaran</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->payment }}</td>
        </tr>
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Tanggal Swab</td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->swab_date }}</td>
        </tr>
        @if(isset($form->reason_swab))
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Keterangan</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->reason_swab }}</td>
            </tr>
        @endif
        {{-- @if ($form->paper_alias == 'srm')
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Tanggal Masuk Kawasan</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->date_in }}</td>
            </tr>
            <tr>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Keterangan</td>
                <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $form->reason }}</td>
            </tr>
        @endif --}}
    @endif
</table>
