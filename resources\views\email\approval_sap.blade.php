<table style="border-collapse: collapse;width: 100%;">
    <tr>
        <td>DocNum</td>
        <td>: {{ $DocNum }}</td>
    </tr>
    <tr>
        <td>Doc Date</td>
        <td>: {{ $DocDate }}</td>
    </tr>
    {{-- <tr>
        <td>Cost Center</td>
        <td>: {{ $header['costCenter'] }}</td>
    </tr> --}}
    {{-- <tr>
        <td>Type</td>
        <td>: {{ $header['type'] }}</td>
    </tr> --}}
    {{-- <tr>
        <td>User</td>
        <td>: {{ $header['user'] }}</td>
    </tr> --}}
    <tr>
        <td>Purchaser</td>
        <td>: {{ $PurchaserCode }} - {{ $PurchaserName }}</td>
    </tr>
    {{-- <tr style="margin-bottom: 30px;">
        <td>Requester</td>
        <td>: {{ $header['requester'] }}</td>
    </tr> --}}
    <tr>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">PO No</th>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">PO Date</th>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Card Code</th>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Card Name</th>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Item Code</th>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Item Name</th>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">UoM</th>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Qty</th>
        <th style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">Remark</th>
    </tr>
    @foreach ($items as $key => $value)
        <tr>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $value["PONum"] }} </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $value["PODate"] }} </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $value["CardCode"] }} </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $value["CardName"] }} </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $value["ItemCode"] }} </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $value["ItemName"] }} </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $value["Uom"] }} </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ floatval($value["Qty"]) }} </td>
            <td style="border: 1px solid #5e5b5b;text-align: left;padding: 4px;">{{ $value["Remarks"] }} </td>
        </tr>
    @endforeach

    {{-- <tr style="margin-top: 30px;">
        <td>Remark</td>
        <td>: {{ $header['remark'] }}</td>
    </tr> --}}
    <tr>
        <td>Reason</td>
        <td>: {{ $Reason }}</td>
    </tr>
</table>
