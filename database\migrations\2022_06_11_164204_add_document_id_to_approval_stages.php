<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDocumentIdToApprovalStages extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('approval_stages', function (Blueprint $table) {
            $table->unsignedBigInteger('document_id')->nullable();
        });

        Schema::table('documents', function (Blueprint $table) {
            $table->string('company', 100)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('approval_stages', function (Blueprint $table) {
            //
        });
    }
}
