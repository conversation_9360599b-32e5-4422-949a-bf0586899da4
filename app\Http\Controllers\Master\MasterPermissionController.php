<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\View\ListPermission;
use App\Traits\RolePermission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;

class MasterPermissionController extends Controller
{
    use RolePermission;

    /**
     * MasterUserController constructor.
     */
    public function __construct()
    {
        $this->middleware(['direct_permission:Permission-index'])->only(['index', 'show']);
        $this->middleware(['direct_permission:Permission-store'])->only('store');
        $this->middleware(['direct_permission:Permission-edits'])->only('update');
        $this->middleware(['direct_permission:Permission-erase'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $search = (isset($request->search)) ? $request->search : null;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "order_line";
        $order = (!empty($options->sortDesc)) ? (($options->sortDesc[0]) ? "desc" : "asc") : 'asc';
        $offset = $pages;

        $result = array();
        $query = ListPermission::select('*')
            ->when($search, function ($query) use ($search) {
                $query->where('menu_name', 'like', '%' . $search . '%');
            });

        $result["total"] = $query->count();

        $parents = Permission::where('has_child', 'Y')
            //->whereIsNull('route_name')
            ->select('id', 'menu_name')
            ->get();

        $data_parent = [];
        foreach ($parents as $parent) {
            $data_parent[] = $parent->menu_name;
        }

        $all_data = $query
            ->orderBy($sorts, $order)
            ->paginate($row_data)
            ->items();

        $all_rows = Permission::groupBy(['menu_name'])->select('menu_name')->get();
        $arr_rows = [];
        foreach ($all_rows as $item) {
            $arr_rows[] = $item->menu_name;
        }

        $result = array_merge($result, [
            'rows' => $all_data,
            'simple' => $arr_rows,
            'parent' => $data_parent
        ]);
        return $this->success($result);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        $form = $request->form;
        DB::beginTransaction();
        try {
            $data = $this->data($form);

            if ($form['is_crud'] == 'Y') {
                $this->generatePermission((object) $data, '-index', 'Y');
            } else {
                if (isset($form['index'])) {
                    $this->generatePermission((object) $data, '-index', 'Y');
                }

                if (isset($form['store'])) {
                    $this->generatePermission((object) $data, '-store', 'Y');
                }

                if (isset($form['edits'])) {
                    $this->generatePermission((object) $data, '-edits', 'Y');
                }

                if (isset($form['edits'])) {
                    $this->generatePermission((object) $data, '-erase', 'Y');
                }
            }

            DB::commit();

            return $this->success([
                "errors" => false
            ], 'Data inserted!');
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $form
     *
     * @return array
     */
    protected function data($form)
    {
        $parent = Permission::where('menu_name', $form['parent_name'])->first();
        return [
            'name' => $form['menu_name'],
            'app_name' => $form['app_name'],
            'menu_name' => $form['menu_name'],
            'parent_id' => ($parent) ? $parent->id : 0,
            'icon' => $form['icon'],
            'route_name' => $form['route_name'],
            'has_child' => $form['has_child'],
            'has_route' => $form['has_route'],
            'order_line' => $form['order_line'],
            'is_crud' => $form['is_crud'],
            'role' => $form['role'],
            'guard_name' => 'web',
        ];
    }

    /**
     * @param $request
     *
     * @return false|string
     */
    protected function validation($request)
    {
        $messages = [
            'form.app_name' => 'Application Name is required!',
            'form.menu_name' => 'Menu Name is required!',
            'form.order_line' => 'Order line field is required!',
            'form.role' => 'Role field is required!',
        ];

        $validator = Validator::make($request->all(), [
            'form.app_name' => 'required',
            'form.menu_name' => 'required',
            'form.order_line' => 'required',
            'form.role' => 'required',
        ], $messages);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param int $id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, int $id): \Illuminate\Http\JsonResponse
    {
        $menu_name = $request->menu_name;
        $data = DB::select("EXEC sp_single_permission '$menu_name' ");

        return $this->success([
            'rows' => $data[0]
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        $form = $request->form;
        DB::beginTransaction();
        try {
            $data = $this->data($form);

            if ($form['is_crud'] == 'Y') {
                $this->generatePermission((object) $data, '-index', 'Y');
            } else {
                if (isset($form['index'])) {
                    $this->generatePermission((object) $data, '-index', 'Y');
                }

                if (isset($form['store'])) {
                    $this->generatePermission((object) $data, '-store', 'Y');
                }

                if (isset($form['edits'])) {
                    $this->generatePermission((object) $data, '-edits', 'Y');
                }

                if (isset($form['edits'])) {
                    $this->generatePermission((object) $data, '-erase', 'Y');
                }
            }

            DB::commit();

            return $this->success([
                "errors" => false
            ], 'Data inserted!');
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): \Illuminate\Http\JsonResponse
    {
        Permission::where('menu_name', '=', $id)->delete();

        return $this->success([
            'errors' => false,
        ], 'Row deleted!');
    }
}
