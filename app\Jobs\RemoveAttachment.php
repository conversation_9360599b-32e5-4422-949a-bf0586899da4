<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemoveAttachment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $files;
    public $docType;
    public $diskName;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($files, $docType = 'public', $diskName = 'sftp')
    {
        $this->files = $files;
        $this->docType = $docType;
        $this->diskName = $diskName;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->files) {
            if ($this->docType == 'storage') {
                custom_disk_delete($this->files, $this->diskName);
            }

            if (is_array($this->files) == 1) {
                foreach ($this->files as $file) {
                    // iterate files
                    if (is_file($file)) {
                        unlink($file);
                    } //end if
                }
            } else {
                if (is_file($this->files)) {
                    unlink($this->files);
                } //end if
            }
        }
    }
}
