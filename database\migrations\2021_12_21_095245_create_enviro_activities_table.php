<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEnviroActivitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('enviro_roles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
        });

        Schema::create('enviro_sub_roles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->unsignedBigInteger('role_id');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
        });

        Schema::create('enviro_user_roles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('enviro_role_id');
            $table->timestamps();
        });

        Schema::create('enviro_user_sub_roles', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('enviro_sub_role_id');
            $table->timestamps();
        });

        Schema::create('enviro_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sub_role_id');
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->dateTime('act_date')->nullable();
            $table->dateTime('problem_date')->nullable();
            $table->dateTime('solution_date')->nullable();
            $table->string('problem_desc')->nullable();
            $table->string('solution_desc')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('enviro_activities');
        Schema::dropIfExists('enviro_role');
        Schema::dropIfExists('enviro_sub_role');
        Schema::dropIfExists('enviro_user_sub_role');
    }
}
