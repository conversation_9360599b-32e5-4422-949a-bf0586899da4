<?php

namespace App\Http\Controllers\Callback;

use App\Http\Controllers\Controller;
use App\Models\Document\Document;
use App\Models\User;
use App\Notifications\CommonNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class CallbackPrivyController extends Controller
{
    public function store(Request $request)
    {
        // $reference_number = $request->reference_number;
        // $status = $request->status;
        $reference_number = $request['reference_number'];
        $status = $request['status'];
        // Log::info('Privy Callback', [
        //     'reference_number' => $reference_number,
        //     'status' => $status,
        //     // 'request' => $request->all(),
        //     // 'request' => Arr::forget($request->all(), 'unsigned_document'),
        //     'message' => $request['message']
        // ]);

        $file_name = null;

        info("callback privy reference_number " . $reference_number
            // " status => " . $status,
            // " file length => " . ($request->signed_document) ? $this->getBase64OriginalLength($request->signed_document) : 0
        );

        if (Str::contains($reference_number, ['EKB'])) {
            $batch = DB::connection('sqlsrv4')
                ->table('batch_approvals')
                ->where('reference_number', $reference_number)
                ->first();
            if ($batch) {
                if (Str::contains($status, ['completed'])) {
                    $updateStatus = 'approved';
                } else {
                    $updateStatus = $status;
                }

                DB::connection('sqlsrv4')
                    ->table('batch_approvals')
                    ->where('reference_number', $reference_number)
                    ->update([
                        'status' => $updateStatus,
                        'callback_message' => 'Success reference number: ' . $reference_number
                    ]);

                $form_draft = json_decode(json_decode($batch->value));
                if (!str($batch->document_type)->contains(["DynamicSign", "TemporaryExport"])) {
                    $file_name = $form_draft->file_names;
                    info("store file reference_number: " . $reference_number . " with status " . $status);
                    $this->storeFile($status, $file_name, $request);
                } else {
                    $file_name = $reference_number . '.pdf';
                    info("store file reference_number: " . $reference_number . " with status " . $status);
                    $this->storeFile($status, $file_name, $request);
                }
                // Log::info('pricy Callback', [
                //     'collect' => collect($form_draft),
                //     // 'file_name' => $form_draft['file_names'],
                //     // 'file_name' => $form_draft->file_names,
                // ]);
                // Log::info('pricy Callback', [
                //     // 'collect' => collect($form_draft),
                //     'file_name' => json_decode(json_decode($batch->value)),
                //     // 'file_name' => $form_draft->file_names,
                // ]);
                // Log::info('pricy Callback', [
                //     // 'collect' => collect($form_draft),
                //     // 'file_name' => $form_draft['file_names'],
                //     'file_name' => $form_draft->file_names,
                // ]);
                // Log::info('success retrive callback EKB');
            } else {
                $file_name = $reference_number . '.pdf';
                info("store file reference_number: " . $reference_number . " with status " . $status);
                $this->storeFile($status, $file_name, $request);
            }
        } else if((Str::contains($reference_number, ['ESIGN']))) {
            $checkUseDigisign = $this->getConfigByName('UseDigisign', 'GENERAL');
            $document = Document::where('reference_number', $reference_number)->first();
            if ($document) {
                $user = User::find($document->created_by);
                // comment after all company use privy
                if (Str::contains($status, ['completed'])) {
                    $document->status = 'approved - finish';
                    $user->notify(new CommonNotification(
                        [
                            "title" => "E-Sign Invoice " . $document->external_document_number . " has been signed",
                            "document" => $document
                        ],
                        $document->id,
                        "E-Sign Invoice",
                        "E-Sign Invoice"
                    ));
                } else {
                    $document->status = 'approved - ' . $status;
                    $user->notify(new CommonNotification(
                        [
                            "title" => "E-Sign Invoice " . $document->external_document_number . " failed to sign! with error $status",
                            "document" => $document
                        ],
                        $document->id,
                        "E-Sign Invoice",
                        "E-Sign Invoice"
                    ));
                }
                $document->privy_status = $status;
                $document->save();

                $file_name = $document->attachment->file_name;
                $file_name = (Str::contains($file_name, '_token_')) ? substr($file_name, 0, -19) . '.pdf' : $file_name;

                $includeTokenInFileName = $this->getConfigByName('IncludeTokenInFileName', 'GENERAL');

                if ($includeTokenInFileName == 'Y' && Str::contains($document->external_document_number, ['IMIP/DN'])) {
                    $file_name = strtoupper(Str::slug(str_replace('/', '-', $document->external_document_number))) . '.pdf';
                }

                info("store file reference_number: " . $reference_number . " with status " . $status);

                $this->storeFile($status, $file_name, $request);

                // Log::info('success retrive callback ESIGN');
            }
        } else if((Str::contains($reference_number, ['ELO']))) {
            if (Str::contains($status, ['completed'])) {
                if (preg_match('/ELO(\d+)CTR(\d+)/', $reference_number, $matches)) {
                    $id_doc = $matches[1];
                    $doc_num = "CTR".$matches[2];
                }
                $basicAuth = base64_encode(config('app.elo_username').":".config('app.elo_password'));

                $reqbody = [ "KT_DOC_NUMBER" => $doc_num ];
                $search_keyword_doc_num = Http::withHeaders([
                    'Authorization' => 'Basic ' . $basicAuth,
                ])->post(config('app.elo_api_url')."/search/keywording", $reqbody);

                if($search_keyword_doc_num->successful() && !empty($search_keyword_doc_num->json())) {
                    $id_parent_doc = $search_keyword_doc_num->json()[0]['id'];
                    $mask_id = "Basic entry";
                    // add file
                    $name = "SIGNED DOCUMENT $doc_num";
                    $version_label = date('YmdHis');
                    $version_comment = "Signed Document From Privy";
                    $base64Data = $request->signed_document;
                    $fileName = 'signed_elo_document_' . time() . '.pdf';

                    $base64String = str_replace('data:application/pdf;base64,', '', $base64Data);
                    $base64String = str_replace(' ', '+', $base64String);
                    $fileContent = base64_decode($base64String);                

                    $response = Http::withHeaders([
                            'Authorization' => 'Basic ' . $basicAuth,
                            'Accept' => '*/*',
                        ])->attach(
                            'file',           
                            $fileContent,     
                            $fileName        
                        )->post(config('app.elo_api_url')."/files/$id_parent_doc?name=$name&maskId=$mask_id&versionLabel=$version_label&versionComment=$version_comment");
                    
                    if($response->successful()) {
                        // update status
                        $updateStatus = 'Signed Document Privy';
                        $updateStatusSite = 'Signed';
                        $reqbodyupdate = [
                            "fields" => [
                                "KT_CONTRACT_STATUS_IMIP" => $updateStatus,
                                "KT_CONTRACT_STATUS_SITE" => $updateStatusSite
                            ]
                        ];
                        $response_update_status = Http::withHeaders([
                            'Authorization' => 'Basic ' . $basicAuth,
                            'Accept' => '*/*',
                        ])->put(config('app.elo_api_url')."/files/$id_parent_doc/keywording", $reqbodyupdate);

                        $response_update_color = Http::withHeaders([
                            'Authorization' => 'Basic ' . $basicAuth,
                            'Accept' => '*/*',
                        ])->put(config('app.elo_api_url')."/files/$id_parent_doc/info", [
                            "colorId" => 2
                        ]);

                        if($response_update_status->successful() && $response_update_color->successful()) {
                            // check double doc
                            $search_children_file = Http::withHeaders([
                                'Authorization' => 'Basic ' . $basicAuth,
                            ])->get(config('app.elo_api_url')."/files/$id_parent_doc/children");
                            
                            if($search_children_file->successful() && !empty($search_children_file->json())) {
                                $children = $search_children_file->json();
                                $oldestId = "";
                                $countData = 0;
                                foreach($children as $child) {
                                    if($child['name'] == $name) {
                                        $countData++;
                                        if ($oldestId === "" || $child['id'] < $oldestId) {
                                            $oldestId = $child['id'];
                                        }
                                    }
                                }

                                if($countData > 1){
                                    // delete duplicate file has oldest id
                                    $responseDeleteFile = Http::withHeaders([
                                        'Authorization' => 'Basic ' . $basicAuth,
                                        'Accept' => '*/*',
                                    ])->delete(config('app.elo_api_url')."/files/$oldestId");
                                }
                            }
                            info("callback privy to ELO reference_number: " . $id_doc . " " . $doc_num . " with status " . $status);   
                        }else{
                            info("callback privy to ELO reference_number: " . $id_doc . " " . $doc_num . " update status error " . $response_update_status);   
                        }
                    }else{
                        info("callback privy to ELO reference_number: " . $id_doc . " " . $doc_num . " upload error " . $response);
                    }
                }else{
                    info("callback privy to ELO reference_number: " . $id_doc . " " . $doc_num . " error " . $search_keyword_doc_num);
                }
            }
        } else {
            info("reference_number not found");
        }
    }

    protected function storeFile($status, $file_name, $request)
    {
        if (Str::contains($status, ['completed'])) {
            if ($file_name) {
                $contents = base64_decode(str_replace('data:application/pdf;base64,', '', $request->signed_document));
                custom_disk_put('documents/' . $file_name, $contents);

                // $path_download = public_path('documents/' . $file_name);
                // file_put_contents($path_download, $contents);
            }
        }
    }
}
