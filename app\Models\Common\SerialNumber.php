<?php

namespace App\Models\Common;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Common\SerialNumber
 *
 * @property int $id
 * @property string|null $serial_number
 * @property string|null $status
 * @property string|null $desc
 * @property string|null $file
 * @property string|null $tgl
 * @property string|null $tgl_update
 * @property string|null $no_doc
 * @property string|null $tgl_doc
 * @property string|null $jenis_doc
 * @property string|null $status_sn
 * @property string|null $file_id
 * @property string|null $tgl_new
 * @property string|null $is_used
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber query()
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereDesc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereFile($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereIsUsed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereJenisDoc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereNoDoc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereSerialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereStatusSn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereTgl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereTglDoc($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereTglNew($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereTglUpdate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SerialNumber whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SerialNumber extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv4';
    protected $table = 'serial_numbers';
}
