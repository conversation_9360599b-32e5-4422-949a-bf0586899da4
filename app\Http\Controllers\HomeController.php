<?php

namespace App\Http\Controllers;

use App\Models\Document\Document;
use App\Models\Resv\ResvHeader;
use App\Models\User;
use App\Traits\DocumentApproval;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class HomeController extends Controller
{
    use DocumentApproval;
    /**
     * @return string
     */
    public function downloadManual()
    {
        return response()->json([
            'url' => url('/Attachment/E-RESV-MANUAL.zip')
        ]);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function homeData(Request $request): \Illuminate\Http\JsonResponse
    {
        $count_draft = $this->copyQuery($request)->where("ApprovalStatus", "=", "-")->count();
        $count_pending = $this->copyQuery($request)->where("ApprovalStatus", "=", "W")->count();
        $count_reject = $this->copyQuery($request)->where("ApprovalStatus", "=", "N")->count();
        $count_approve = $this->copyQuery($request)->where("ApprovalStatus", "=", "Y")->count();


        return response()->json([
            "rows" => [
                [
                    "text" => "Draft",
                    "icon" => "mdi-text-box-plus-outline",
                    "color" => "secondary",
                    "description" => "Document that not yet submitted for approval",
                    'value' => $count_draft,
                ],
                [
                    "text" => "Waiting",
                    "icon" => "mdi-clock-outline",
                    "description" => "Document that waiting for approvals",
                    "color" => "blue",
                    'value' => $count_pending,
                ],
                [
                    "text" => "Rejected",
                    "icon" => "mdi-close-box-outline",
                    "color" => "error",
                    "description" => "Rejected documents",
                    'value' => $count_reject,
                ],
                [
                    "text" => "Approved",
                    "icon" => "mdi-checkbox-marked-outline",
                    "color" => "primary",
                    "description" => "Approved documents",
                    'value' => $count_approve,
                ]
            ],
        ]);
    }

    /**
     * Retrieves statistics data for the given document type and created by.
     *
     * @param Request $request The HTTP request containing the document and document type.
     * @throws \Exception If there was an error retrieving statistics data.
     * @return \Illuminate\Http\JsonResponse The statistics data in JSON format.
     */
    public function statistics(Request $request)
    {
        $document = $request->document;
        $document_type = $request->document_type;

        $data = [];
        if ($document == 'meterai') {
            $created_by = $this->getCreatedDocument($request);
            $data = DB::select("EXEC sp_stats_document '$document_type', '$created_by' ");
        }

        if ($document == 'approval') {
            $data = [
                [
                    'title' => 'Pending Approval Cherry',
                    'stats' => $this->countPendingCherryApproval($request),
                    'color' => 'warning',
                    'icon' => 'mdi-pause-circle',
                    'link' => '/approval/cherry'
                ],
                [
                    'title' => 'Pending Approval Internal',
                    'stats' => $this->countPendingInternal($request),
                    'color' => 'warning',
                    'icon' => 'mdi-pause-circle',
                    'link' => '/document/approval'
                ]
            ];
        }

        return response()->json([
            'data' => $data
        ]);
    }

    public function countPendingInternal($request)
    {
        $status = 'pending';

        $user = $request->user()->username;
        return $this->stages($status, $user)['total'];
    }

    public function countPendingCherryApproval($request)
    {
        $cherry_token = $request->user()->cherry_token;
        $employee_code = $request->user()->employee_code;
        $documents = Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'GetList',
            'ModelCode' => 'ApprovalRequests',
            'UserName' => $request->user()->username,
            'Token' => $cherry_token,
            'OrderBy' => 'InsertStamp',
            'OrderDirection ' => 'desc',
            'ParameterData' => [
                [
                    'ParamKey' => 'ApproverCode',
                    'ParamValue' => $employee_code,
                    'Operator' => 'eq'
                ],
                [
                    'ParamKey' => 'StatusId',
                    'ParamValue' => 'Pending',
                    'Operator' => 'eq'
                ]
            ]
        ]);

        $collect = $documents->collect();
        return ($collect['Data']) ? count($collect['Data']) : 0;
    }

    /**
     * Retrieves the user ID of the creator of a document.
     *
     * @param mixed $request The HTTP request object.
     * @throws \Exception If the user does not have permission to access the creator ID.
     * @return mixed The ID of the creator of the document.
     */
    protected function getCreatedDocument($request)
    {
        // $contains = [850, 699, 4321];
        $contains = ['6071', '850', '699', '4321', '15385', '15679', '16862'];
        $user = User::find($request->user()->id);
        if ($user->hasRole('Superuser')) {
            $user = Document::select('created_by')
                ->distinct()
                ->whereNotNull('created_by')
                ->pluck('created_by');

            $created_by = collect($user)->implode(", ");
        } else if (Str::contains($request->user()->id, $contains)) {
            $created_by = implode(", ", $contains);
        } else {
            $created_by = $request->user()->id;
        }
        return $created_by;
    }


    /**
     * @param $request
     *
     * @return mixed
     */
    protected function copyQuery($request)
    {
        return ResvHeader::where("CreatedBy", "=", $request->user()->username);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    protected function menus(Request $request)
    {
        $user = User::find($request->user()->id);
        $permissions = $user
            ->getAllPermissions()
            ->where('parent_id', '=', '0')
            ->whereIn('app_name', [$request->appName, 'All']);

        $array = [];
        foreach ($permissions as $permission) {
            $children = $user
                ->getAllPermissions()
                ->where('parent_id', '=', $permission->id)
                ->whereIn('app_name', [$request->appName, 'All']);

            $array_child = [];
            $prev_name = '';
            foreach ($children as $child) {
                if ($prev_name != $child->menu_name) {
                    if (Str::contains($child->name, 'index')) {
                        $array_child[] = [
                            'menu' => $child->menu_name,
                            'title' => $child->menu_name,
                            'icon' => $child->icon,
                            'route_name' => $child->route_name,
                            'to' => $child->route_name,
                        ];

                        $prev_name = $child->menu_name;
                    }
                }
            }

            $array[] = [
                'menu' => $permission->menu_name,
                'title' => $permission->menu_name,
                'icon' => $permission->icon,
                'route_name' => $permission->route_name,
                'to' => $permission->route_name,
                'children' => $array_child
            ];
        }
        return $this->success([
            'menus' => $array,
            'app_version' => app()->version()
        ]);
    }
}
