<?php

namespace App\Http\Controllers\SapS4;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\CherryApprovalService;
use App\Services\ProcessPostSapS4Service;
use App\Services\SapS4Service;
use App\Models\Resv\ReservationDetails;
use App\Models\Resv\ReservationHeader;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ReprocessDocumentToS4Controller extends Controller
{
    public function pushDocumentTransType3()
    {
        $datas = DB::connection('sqlsrv')
            ->table('api_responses')
            ->select('reservation_number')
            ->distinct()
            ->where('message', 'Success')
            // ->whereRaw("CONVERT(char(10), created_at ,126)  >= '2024-01-09' ")
            ->get()
            ->pluck('reservation_number')
            ->toArray();

        $itemCode = DB::connection('sqlsrv')
            ->table('resv_headers')
            // ->table('resv_details')
            // ->leftJoin("RESV_H", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            // ->select("resv_details.ItemCode", "resv_details.WhsCode", "resv_headers.WorkLocation", "resv_headers.Division")
            ->select("resv_headers.DocNum")
            ->whereBetween("resv_headers.DocDate", ['2023-12-01', '2024-01-31'])
            // ->where('resv_headers.RequestType', 'Restock')
            ->whereIn('resv_headers.ItemType', ['Asset', 'Service'])
            ->where('WorkLocation', 'IMIP MOROWALI')
            ->where('resv_headers.ApprovalStatus', 'Y')
            // ->whereNotIn("resv_headers.DocNum", $datas)
            ->distinct()
            ->get()
            ->pluck('DocNum')
            ->toArray();

        $itemCode2 = DB::connection('sqlsrv')
            ->table('resv_headers')
            // ->table('resv_details')
            // ->leftJoin("RESV_H", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            // ->select("resv_details.ItemCode", "resv_details.WhsCode", "resv_headers.WorkLocation", "resv_headers.Division")
            ->select("resv_headers.DocNum")
            ->whereBetween("resv_headers.DocDate", ['2023-12-01', '2024-01-31'])
            ->where('resv_headers.RequestType', 'Restock')
            // ->whereIn('resv_headers.ItemType', ['Asset', 'Service'])
            ->where('WorkLocation', 'IMIP MOROWALI')
            ->where('resv_headers.ApprovalStatus', 'Y')
            // ->whereNotIn("resv_headers.DocNum", $datas)
            ->distinct()
            ->get()
            ->pluck('DocNum')
            ->toArray();

        $allData = array_merge($itemCode, $itemCode2);

        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();

        $res = [];
        $postService = new ProcessPostSapS4Service();
        foreach ($allData as $key => $value) {
            // Log::info('Re Push Doc before if ' . $value);
            // if (!Str::contains($value, $datas)) {
            // Log::info('Re Push Doc after IF ' . $value);
            // }
            $dataHeader = ReservationHeader::select(
                "resv_headers.*",
                "resv_headers.Company as CompanyName"
            )
                ->where("resv_headers.DocNum", "=", $value)
                ->first();
            $dataDetails = ReservationDetails::where("U_DocEntry", "=", $dataHeader->U_DocEntry)
                ->get();
            $response = $postService->store($dataHeader, $dataDetails);

            $res[] = $response;
        }

        return response()->json([
            'allData' => $allData,
            'res' => $res,
        ]);

    }

    public function pushDocument(Request $request)
    {
        try {
            // Initialize service sap s4
            $listDoc = $request->docNum;
            $service = new SapS4Service();
            $service->login();

            $datas = DB::connection('sqlsrv')
                ->table('api_responses')
                ->select('reservation_number')
                ->distinct()
                ->where('message', 'Success')
                ->whereRaw("CONVERT(char(10), created_at ,126)  >= '2024-01-09' ")
                ->get()
                ->pluck('reservation_number')
                ->toArray();
            // initialize post service
            $postService = new ProcessPostSapS4Service();
            $res = [];
            foreach ($listDoc as $key => $value) {
                // Log::info('Re Push Doc before if ' . $value);
                // if (!Str::contains($value, $datas)) {
                //     Log::info('Re Push Doc after IF ' . $value);
                // }
                $dataHeader = ReservationHeader::select(
                    "resv_headers.*",
                    "resv_headers.Company as CompanyName"
                )
                    ->where("resv_headers.DocNum", "=", $value)
                    ->first();
                $dataDetails = ReservationDetails::where("U_DocEntry", "=", $dataHeader->U_DocEntry)
                    ->get();
                $response = $postService->store($dataHeader, $dataDetails);

                $res[] = $response;
            }

            return response()->json($res);

        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 422);
        }
    }
}
