<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\UrgentRequest
 *
 * @method static \Illuminate\Database\Eloquent\Builder|UrgentRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UrgentRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UrgentRequest query()
 * @mixin \Eloquent
 */
class UrgentRequest extends Model
{
    use HasFactory;
}
