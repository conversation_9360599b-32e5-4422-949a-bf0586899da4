<?php

namespace App\Http\Controllers\Approval;

use App\Http\Controllers\Controller;
use App\Models\Document\Document;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class CherryApprovalStagesController extends Controller
{
    public function __construct()
    {
        $this->middleware(['direct_permission:Approval Cherry-index'])->only('index');
        $this->middleware(['direct_permission:Approval Cherry-store'])->only('store');
    }
    public function index(Request $request): JsonResponse
    {
        $cherry_token = $request->user()->cherry_token;
        $employee_code = $request->user()->employee_code;
        $status_approval = $request->status_approval;

        $documents = Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'GetList',
            'ModelCode' => 'ApprovalRequests',
            'UserName' => $request->user()->username,
            'Token' => $cherry_token,
            'OrderBy' => 'InsertStamp',
            'OrderDirection ' => 'desc',
            'ParameterData' => [
                [
                    'ParamKey' => 'ApproverCode',
                    'ParamValue' => $employee_code,
                    'Operator' => 'eq'
                ],
                [
                    'ParamKey' => 'StatusId',
                    'ParamValue' => $status_approval,
                    'Operator' => 'eq'
                ]
            ]
        ]);

        $collect = $documents->collect();
        $array_result = [];

        //return response()->json($collect);

        if (!isset($collect['Data'])) {
            return $this->success([]);
        }

        foreach ($collect['Data'] as $datum) {
            $documents = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'GADocuments',
                'UserName' => $request->user()->username,
                'Token' => $cherry_token,
                'ParameterData' => [
                    [
                        'ParamKey' => 'Code',
                        'ParamValue' => $datum['ModelEntityCode'],
                        'Operator' => 'eq'
                    ],
                ]
            ]);

            //return response()->json($documents->collect()['Data']);
            if ($documents->collect()['Data']) {
                $document = Document::where('document_number', $documents->collect()['Data'][0]['DocumentReferenceID'])
                    ->with(['attachment', 'coordinate'])
                    ->first();

                $array_result[] = [
                    'Keys' => Str::random(20),
                    'TypeName' => $datum['TypeName'],
                    'ApproveUrl' => $datum['ApproveUrl'],
                    'ApproveToken' => $datum['ApproveToken'],
                    'RejectUrl' => $datum['RejectUrl'],
                    'RejectToken' => $datum['RejectToken'],
                    'Code' => $datum['Code'],
                    'DocDate' => $datum['InsertStamp'],
                    'Details' => $documents->collect()['Data'][0]['DocumentContent'],
                    'DocumentReferenceID' => $documents->collect()['Data'][0]['DocumentReferenceID'],
                    'DocNum' => $documents->collect()['Data'][0]['DocumentReferenceID'],
                    'RequesterName' => $datum['RequesterName'],
                    'StatusId' => $datum['StatusId'],
                    'U_DocEntry' => $document->id,
                    'Document' => $document,
                    'Date' => ($datum['Date']) ?
                        date('Y-m-d H:i:s', (int)substr($datum['Date'], 6, 10)) : '',
                ];
            }
        }

        return $this->success([
            'rows' => $array_result,
            'total' => count($collect['Data']),
            'ApprovalStatus' => ['Pending', 'Approved', 'Rejected'],
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $code = [];
            $selected = collect($request->selected);
            foreach ($selected as $item) {
                $code[] = $item['Code'];
            }

            $action = $request->action;
            $documents = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'ApprovalRequests',
                'UserName' => $request->user()->username,
                'Token' => $request->user()->cherry_token,
                'ParameterData' => [
                    [
                        'ParamKey' => 'Code',
                        'ParamValue' => implode(',', $code),
                        'Operator' => 'in'
                    ]
                ]
            ]);

            $concat_array = [];
            foreach ($documents->collect()['Data'] as $index => $item) {
                $item = (object)$item;
                $item->StatusId = $action;
                $concat_array[] = $item;
            }

            //return response()->json($concat_array);

            $approval = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'SubmitList',
                'ModelCode' => 'ApprovalRequests',
                'UserName' => $request->user()->username,
                'Token' => $request->user()->cherry_token,
                'ModelData' => json_encode($concat_array),
                'ParameterData' => []
            ]);
            return $this->success($approval->collect(), $approval->collect()['Message']);
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }
}
