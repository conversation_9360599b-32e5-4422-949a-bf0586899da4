<?php

namespace App\Models;

use App\Models\Task\TaskTagPeople;
use App\Models\User\UserCompany;
use App\Models\User\UserDivision;
use App\Models\User\UserDocType;
use App\Models\User\UserItmGrp;
use App\Models\User\UserWhs;
use App\Models\User\UserWorkLocation;
use App\Models\View\ViewEmployee;
use App\Traits\GenerateInitial;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Broadcasting\PrivateChannel;

/**
 * App\Models\User
 *
 * @property int $id
 * @property string|null $name
 * @property string $username
 * @property string|null $email
 * @property string|null $cherry_token
 * @property string|null $cherry_expired_token
 * @property string|null $department
 * @property string|null $company
 * @property string|null $employee_code
 * @property string|null $company_code
 * @property string|null $position
 * @property string|null $location
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string|null $password
 * @property string $active
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $is_admin_subwh
 * @property string $is_superuser
 * @property string|null $is_sap_user
 * @property string $is_sales_user
 * @property string|null $specimen_sign
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserDocType> $documentType
 * @property-read int|null $document_type_count
 * @property-read mixed $initial
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserItmGrp> $itemGroup
 * @property-read int|null $item_group_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Master\Permission> $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Paper\Role> $roles
 * @property-read int|null $roles_count
 * @property-read TaskTagPeople|null $tag
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserCompany> $userCcompany
 * @property-read int|null $user_ccompany_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserDivision> $userDivision
 * @property-read int|null $user_division_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserWhs> $userWhs
 * @property-read int|null $user_whs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserWorkLocation> $userWorLocation
 * @property-read int|null $user_wor_location_count
 * @property-read ViewEmployee|null $viewEmployee
 * @method static \Database\Factories\UserFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User permission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User role($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCherryExpiredToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCherryToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCompanyCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmployeeCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIsAdminSubwh($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIsSalesUser($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIsSapUser($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereIsSuperuser($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereSpecimenSign($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUsername($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User withoutPermission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|User withoutRole($roles, $guard = null)
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles, HasApiTokens;
    use GenerateInitial;

    protected $connection = 'sqlsrv';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'username',
        'cherry_token',
        'department',
        'company',
        'position',
        'location',
        'company_code',
        'employee_code',
        'is_admin_subwh',
        'is_superuser',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    protected $appends = [
        'initial'
    ];

    public function getInitialAttribute()
    {
        return ($this->name) ? $this->generate($this->name) : '';
    }

    /**
     * @return HasMany
     */
    public function userCcompany(): HasMany
    {
        return $this->hasMany(UserCompany::class, 'U_UserID', 'U_UserID');
    }

    public function tag()
    {
        return $this->hasOne(TaskTagPeople::class, 'user_id', 'id');
    }

    public function documentType()
    {
        return $this->hasMany(UserDocType::class);
    }

    public function userDivision()
    {
        return $this->hasMany(UserDivision::class);
    }

    public function itemGroup()
    {
        return $this->hasMany(UserItmGrp::class);
    }

    public function userWhs()
    {
        return $this->hasMany(UserWhs::class);
    }

    public function userWorLocation()
    {
        return $this->hasMany(UserWorkLocation::class);
    }

    public function viewEmployee()
    {
        return $this->belongsTo(ViewEmployee::class, 'username', 'Nik');
    }

    /**
     * The channels the user receives notification broadcasts on.
     */
    public function receivesBroadcastNotificationsOn(): string
    {
        return 'users.' . $this->username;
    }
}
