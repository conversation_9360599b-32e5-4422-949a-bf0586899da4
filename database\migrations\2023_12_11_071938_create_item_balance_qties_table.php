<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateItemBalanceQtiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('item_balance_qty', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('master_item_id');
            $table->date('balance_date');
            $table->double('balance_qty', 12, 2);
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->index(['master_item_id', 'created_by']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('item_balance_qty');
    }
}
