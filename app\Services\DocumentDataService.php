<?php

namespace App\Services;

use App\Models\Document\Document;
use App\Traits\ConnectHana;
use Carbon\Carbon;
use PDO;

class DocumentDataService
{
    use ConnectHana;

    /**
     * Retrieves an array of data filters for use in a UI.
     *
     * @throws \Exception description of exception
     * @return array An array of data filters for use in a UI.
     */
    public function dataFilter()
    {
        $now = Carbon::now();
        $weekStartDate = $now->startOfWeek()->format('Y-m-d');
        $weekEndDate = $now->endOfWeek()->format('Y-m-d');

        $startOfMonth = $now->startOfMonth()->format('Y-m-d');
        $endOfMonth = $now->endOfMonth()->format('Y-m-d');


        $lastWeek = Carbon::now()->addDays(-7);
        $startOfLastWeek = $lastWeek->startOfWeek()->format('Y-m-d');
        $endOfLastWeek = $lastWeek->endOfWeek()->format('Y-m-d');

        return [
            [
                'text' => 'Custom',
                'date_from' => null,
                'date_to' => null
            ],
            [
                'text' => 'Since Yesterday',
                'date_from' => Carbon::now()->addDays(-1)->format('Y-m-d'),
                'date_to' => date('Y-m-d')
            ],
            [
                'text' => 'Since 2 Days Ago',
                'date_from' => Carbon::now()->addDays(-2)->format('Y-m-d'),
                'date_to' => date('Y-m-d')
            ],
            [
                'text' => 'This Week',
                'date_from' => $weekStartDate,
                'date_to' => $weekEndDate
            ],
            [
                'text' => 'Last Week',
                'date_from' => $startOfLastWeek,
                'date_to' => $endOfLastWeek
            ],
            // [
            //     'text' => 'This Week-to-date',
            //     'date_from' => $weekStartDate,
            //     'date_to' => date('Y-m-d')
            // ],
            [
                'text' => 'This Month',
                'date_from' => $startOfMonth,
                'date_to' => $endOfMonth
            ],
            // [
            //     'text' => 'This Month-to-date',
            //     'date_from' => $startOfMonth,
            //     'date_to' => date('Y-m-d')
            // ],
            // [
            //     'text' => 'This Year',
            //     'date_from' => $startOfYear,
            //     'date_to' => $endOfYear
            // ],
            // [
            //     'text' => 'This Year-to-date',
            //     'date_from' => $startOfYear,
            //     'date_to' => date('Y-m-d')
            // ],
            // [
            //     'text' => 'Since 30 Days Ago',
            //     'date_from' => Carbon::now()->addDays(-30)->format('Y-m-d'),
            //     'date_to' => date('Y-m-d')
            // ],
            // [
            //     'text' => 'Since 60 Days Ago',
            //     'date_from' => Carbon::now()->addDays(-60)->format('Y-m-d'),
            //     'date_to' => date('Y-m-d')
            // ],
            // [
            //     'text' => 'Since 90 Days Ago',
            //     'date_from' => Carbon::now()->addDays(-90)->format('Y-m-d'),
            //     'date_to' => date('Y-m-d')
            // ],
            // [
            //     'text' => 'Since 365 Days Ago',
            //     'date_from' => Carbon::now()->addDays(-365)->format('Y-m-d'),
            //     'date_to' => date('Y-m-d')
            // ],
        ];
    }

    /**
     * This function returns an array of document statuses based on the given input.
     *
     * @param string $status The status of the document or special value 'All'
     *                       to retrieve all distinct statuses.
     * @return array The array of document statuses.
     */
    public function documentStatus($status)
    {
        if ($status == 'All') {
            $status = Document::select('status')->distinct()->pluck('status');
        } elseif ($status == 'Canceled / rejected') {
            $status = ['canceled', 'rejected'];
        } elseif ($status == 'Approved') {
            $status = ['approved', 'approved - finish', 'approved - failed', 'failed - approve'];
        } else {
            $status = [$status];
        }

        return $status;
    }

    /**
     * Retrieves customer data from a SAP database using a PDO ODBC connection.
     *
     * @return array An array containing the customer data in the format:
     *               [
     *                   "CardName" => string,
     *                   "CardCode" => string,
     *               ]
     * @throws \Exception Error connecting to the database or executing the query.
     */
    public function customer()
    {
        $service = new SapS4Service();
        $service->login();

        $customer = [];
        $customers = $service->getCustomer();
        if ($customers) {
            if (array_key_exists('DATA', $customers)) {
                foreach ($customers['DATA'] as $key => $value) {
                    $customer[] = [
                        "CardName" => $value['NAME1'],
                        "CardCode" => $value['KUNNR'],
                        "Address" => $value['KUNNR'],
                    ];
                }
            }
        }

        return $customer;
    }

    /**
     * Retrieve the payment options available for signing.
     *
     * @return array Returns an array of payment options.
     */
    public function signPayment()
    {
        return [
            [
                'text' => 'Pay Per Document',
                'value' => '3'
            ],
            [
                'text' => 'Pay Per Sign',
                'value' => '2'
            ]
        ];
    }
}
