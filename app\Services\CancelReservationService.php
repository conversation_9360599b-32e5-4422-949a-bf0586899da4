<?php

namespace App\Services;

use App\Models\Common\SafetyData;
use App\Models\Master\ApiResponse;
use App\Models\Resv\ResvHeader;
use App\Traits\AppConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class CancelReservationService
{
    use AppConfig;

    public function update(ResvHeader $header, Request $request)
    {
        $cancelReservation = $this->cancelReservation($header);
        if ($cancelReservation['error']) {
            throw new \Exception($cancelReservation['message'], 1);
        }

        if ($header->CategoryType == 'APD') {
            DB::connection('sqlsrv')
                ->table('resv_headers')
                ->where('U_DocEntry', '=', $request->form['U_DocEntry'])
                ->update([
                    'DocStatus' => 'C'
                ]);
            SafetyData::where("header_id", $header->U_DocEntry)->delete();
        } else {
            DB::connection('sqlsrv')
                ->table('resv_headers')
                ->where('U_DocEntry', '=', $request->form['U_DocEntry'])
                ->update([
                    'DocStatus' => 'C'
                ]);
        }
    }

    private function cancelReservation(ResvHeader $header)
    {
        // Initialize service sap s4
        $service = new SapS4Service();
        $service->login();

        $postService = new ProcessPostSapS4Service();
        $transType = $postService->getTransType($header);

        $baseUrl = $this->getConfigByName('UrlSAPBaseUrl', 'SAPS4');
        $postAction = $this->getConfigByName('SapCancelDocument', 'SAPS4');
        $clientId = $this->getConfigByName('SapClientId', 'SAPS4');

        $url = $baseUrl . $postAction . '?sap-client=' . $clientId;

        $params = [
            'eresv' => $header->DocNum,
            'transtype' => $transType,
        ];

        $response = Http::withHeaders([
            'x-csrf-token' => $this->getConfigByName('SapS4Token', 'SAPS4'),
            'Content-Type' => 'application/json',
            'Authorization' => 'Basic ' . $this->getConfigByName('SapS4Token', 'SAPS4'),
            'Cookie' => $this->getConfigByName('SapS4Cookie', 'SAPS4') . '; sap-usercontext=sap-client=' . $clientId
        ])
            ->withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->post($url, $params);

        $responseData = $response->json();

        ApiResponse::create([
            'message' => $responseData[0]['SUCCESS'] === 'Success' ? 'Success' : 'Error',
            'url' => $url,
            'params' => json_encode($params),
            'response' => json_encode($responseData),
            'reservation_number' => $header->DocNum
        ]);

        if ($responseData[0]['SUCCESS'] !== 'Success') {
            return [
                'error' => true,
                'message' => $responseData[0]['MESSAGE']
            ];
        }

        return [
            'error' => false
        ];
    }
}
