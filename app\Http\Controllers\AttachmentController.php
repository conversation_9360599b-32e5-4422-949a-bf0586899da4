<?php

namespace App\Http\Controllers;

use App\Models\Common\Attachment;
use App\Models\Document\Document;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AttachmentController extends Controller
{
    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $type = $request->type;
        if ($type == 'eform') {
            $attachment = Attachment::where('str_url', '=', $request->source_id);
        } else {
            $attachment = Attachment::where('source_id', '=', (int) $request->source_id);
        }
        return $this->success([
            'rows' => $attachment->where('type', $type)->get(),
            'total' => $attachment->where('type', $type)->count()
        ]);
    }

    public function attachment(Request $request, $path)
    {
        // return response()->json([
        //     "status" => custom_disk_check('Attachment/' . $path, 'sftp')
        // ]);

        if (custom_disk_check('Attachment/' . $path, 'sftp')) {
            return Storage::disk('sftp')->response(config('filesystems.disks.sftp.root') . 'Attachment/' . $path);
        }

        abort(404);
    }

    public function documents(Request $request, $path)
    {
        if (custom_disk_check('documents/' . $path, 'sftp')) {
            return Storage::disk('sftp')->response('documents/' . $path);
        }

        abort(404);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        if ($request->type == 'reservation') {
            $validRule = 'required|mimes:pdf,docx,docx,png,jpg,jpeg|max:5048';
        } else {
            $validRule = 'required|mimes:pdf,docx,docx,png,jpg,jpeg|max:8048';
        }
        $validator = Validator::make($request->all(), [
            'file' => $validRule,
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors(), '422');
        }
        try {
            if ($request->type == 'reservation' || $request->type == 'reservation_header') {
                $attachment = Attachment::where('type', '=', $request->type)
                    ->where('source_id', '=', $request->source_id)
                    ->first();
                if ($attachment) {
                    return $this->error('Row must have 1 attachment!');
                }
            }
            $data_file = $request->file('file');

            $extension = $data_file->getClientOriginalExtension();

            if(empty($extension)) {
                return $this->error("Silahkan periksa file kembali, karena file yang diupload tidak ada extension!", '422');
            }

            // $destination_path = public_path('/Attachment/docs');

            // if (!file_exists($destination_path)) {
            //     if (!mkdir($destination_path, 0777, true) && !is_dir($destination_path)) {
            //         throw new \RuntimeException(
            //             sprintf(
            //                 'Directory "%s" was not created',
            //                 $destination_path
            //             )
            //         );
            //     }
            // }

            $origin_name = $data_file->getClientOriginalName();
            $name_no_ext = strtoupper(Str::slug(pathinfo($origin_name, PATHINFO_FILENAME))) . time();
            $file_name = $name_no_ext . '.' . $extension;
            // $data_file->move($destination_path, $file_name);

            $destination_path = custom_disk_path("Attachment/docs", "sftp");
            $data_file->storeAs($destination_path, $file_name, 'sftp');


            $url = config('app.url');

            $data = [
                'file_name' => $file_name,
                'file_path' =>  $url . '/Attachment/docs/' . $file_name,
                'source_id' => (int) $request->source_id,
                'str_url' => $request->source_id,
                'created_by' => $request->user()->id,
                'type' => $request->type
            ];

            $attach = Attachment::create($data);

            if ($request->type == 'peruri') {
                $document = Document::find($request->source_id);
                $document->external_document_number = pathinfo($origin_name, PATHINFO_FILENAME);
                $document->save();
            } else if ($request->type == 'reservation_header') {
                DB::connection('sqlsrv')
                    ->table('resv_headers')
                    ->where('U_DocEntry', '=', $request->source_id)
                    ->update([
                        'U_ATTACH' => $attach->file_path
                    ]);
            }

            $count_attachment = Attachment::where('type', '=', $request->type)
                ->where('source_id', '=', (int) $request->source_id)
                ->count();

            return $this->success([
                'count' => $count_attachment
            ], 'Document Uploaded!');
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), '422');
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request)
    {
        try {
            $attachment = Attachment::where('id', '=', $request->id)
                ->first();

            if ($attachment) {
                if ($attachment->created_by != $request->user()->id) {
                    return $this->error('Not authorized to delete this file!');
                }

                $file = '/Attachment/docs/' . $attachment->file_name;
                // throw new \Exception(custom_disk_delete($file, 'sftp'));
                custom_disk_delete($file, 'sftp');
                // unlink(public_path() . $file);
                Attachment::where('id', '=', $attachment->id)
                    ->delete();

                if ($attachment->type == 'peruri') {
                    $document = Document::find($attachment->source_id);
                    $document->external_document_number = null;
                    $document->digisign_coordinate = null;
                    $document->digisign_top = null;
                    $document->digisign_left = null;
                    $document->digisign_width = null;
                    $document->digisign_height = null;
                    $document->meterai_coordinate = null;
                    $document->meterai_top = null;
                    $document->meterai_left = null;
                    $document->meterai_width = null;
                    $document->meterai_height = null;
                    $document->coordinate_document_path = null;
                    $document->vis_llx = null;
                    $document->vis_lly = null;
                    $document->vis_urx = null;
                    $document->vis_ury = null;
                    $document->materai_page = null;
                    $document->vis_digisign_llx = null;
                    $document->vis_digisign_lly = null;
                    $document->vis_digisign_urx = null;
                    $document->vis_digisign_ury = null;
                    $document->sign_page = null;
                    $document->save();
                }


                return $this->success('', 'File deleted!');
            } else {
                return $this->error('File not found', 422);
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 422);
        }
    }
}
