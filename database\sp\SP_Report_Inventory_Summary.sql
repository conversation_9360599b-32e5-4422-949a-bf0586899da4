SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO -- =============================================
    -- Author:		<Author,,Name>
    -- Create date: <Create Date,,>
    -- Description:	<Description,,>
    -- =============================================
    CREATE PROCEDURE SP_Report_Inventory_Summary (
        @FromDate DATE,
        @EndDate DATE,
        @Whs NVARCHAR(200),
        @DocType NVARCHAR(200)
    ) AS -- BEGIN
    BEGIN -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
SET NOCOUNT ON;
select COUNT(B.qty) as qty,
    D.Department,
    D.WhsCode,
    A.doc_type
from inventories as A
    left join inventory_details As B on A.id = B.header_id
    left join resv_details as C on B.resv_detail_id = C.LineEntry
    left join resv_headers as D on C.U_DocEntry = D.U_DocEntry
WHERE CONVERT(varchar, A.post_date, 23) BETWEEN @FromDate AND @EndDate
    AND A.doc_type = @DocType
    AND B.whs_code in (
        SELECT Split.a.value('.', 'NVARCHAR(MAX)') DATA
        FROM (
                SELECT CAST(
                        '<X>' + REPLACE(@Whs, ',', '</X><X>') + '</X>' AS XML
                    ) AS String
            ) AS A
            CROSS APPLY String.nodes('/X') AS Split(a)
    )
GROUP by D.Department,
    D.WhsCode,
    A.doc_type
END
GO