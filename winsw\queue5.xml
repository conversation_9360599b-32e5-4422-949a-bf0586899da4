<?xml version="1.0" encoding="utf-8" ?>
<service>
    <id>laravel-backendcore-queue-doc-purhase</id>
    <name>Laravel Backendcore Queue Document Purchase</name>
    <description>This service runs Laravel Queue document purchase.</description>
    <executable>D:\laragon-new\bin\php\php-8.3.4\php.exe</executable>
    <arguments>D:\laragon-new\www\backendcore\artisan queue:work --tries=2 --memory=512 --timeout=7000  --daemon --queue=documentPurchase</arguments>
    <priority>RealTime</priority>
    <log mode="roll"></log>
    <startmode>Automatic</startmode>
    <onfailure action="restart" delay="10 sec"/>
    <onfailure action="restart" delay="20 sec"/>
    <onfailure action="restart" delay="20 sec"/>
</service>
