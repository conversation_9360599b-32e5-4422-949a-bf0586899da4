<?php

namespace App\Jobs;

use App\Models\Approval\ApprovalStage;
use App\Models\Document\Document;
use App\Models\User;
use App\Models\View\ViewApprovalStage;
use App\Models\View\ViewEmployee;
use App\Services\QrCodeService;
use App\Traits\ApiResponse;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image;
use setasign\Fpdi\Fpdi;

class ProcessInternalSignDocument implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use ApiResponse;

    protected $document;
    protected $fileName;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($document, $fileName)
    {
        $this->document = $document;
        $this->fileName = $fileName;
        $this->onQueue('internalSign');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $documents = $this->document;
        $document = Document::where('id', $documents->id)
            ->first();

        $approval = ApprovalStage::where('document_id', $document->id)->first();

        $fileName = $this->fileName;
        create_file_delete_job('/Attachment/docs/' . $fileName->file_name);
        $pdf_file = public_path('/Attachment/docs/' . $fileName->file_name);
        $fpdi = new Fpdi('P', 'pt');
        $qrCodeService = new QrCodeService();

        $dataCheck = [
            'document_id' => $document->id,
            'name' => 'Submit Digital Sign',
        ];

        try {
            $qrCodeService->generatePath(public_path() . '/images/qrcode/');
            $count = $fpdi->setSourceFile($pdf_file);
            for ($i = 1; $i <= $count; $i++) {
                $template = $fpdi->importPage($i);
                $size = $fpdi->getTemplateSize($template);
                $fpdi->AddPage($size['orientation'], array($size['width'], $size['height']));
                $fpdi->useTemplate($template);

                foreach ($document->coordinate as $key => $value) {
                    if ($value->sign_page == $i) {
                        $file_export_name = $document->str_url;
                        $qr_file = config('app.front_url') . '/verification?str=' . $file_export_name;

                        $qrCodeService->generateQrCode($qr_file, $file_export_name, 300, false);


                        $x = $value->digisign_left;
                        $y = $value->digisign_top;
                        $width = $value->vis_digisign_urx - $value->vis_digisign_llx;
                        $height = $value->vis_digisign_ury - $value->vis_digisign_lly;

                        Log::info('sign manual', [
                            'document id' => $document->id,
                            'approval' => $approval
                        ]);

                        $qrImage = public_path('images/qrcode/' . $file_export_name . '.png');

                        if ($approval) {
                            if ($approval->approval->show_speciment == 'Y') {
                                $specimen = public_path('images/approval/' . $approval->user->id . '.png');
                                $newname = public_path('images/speciment/' . $file_export_name . '.png');

                                $img_canvas = Image::canvas(500, 300);
                                $img_canvas->insert(Image::make($qrImage)->resize(300, 300));
                                $img_canvas->insert(Image::make($specimen)->resize(200, null, function ($constraint) {
                                    $constraint->aspectRatio();
                                }), 'top-right'); // add offset
                                $img_canvas->save($newname, 100);

                                $fpdi->Image($newname, $x, $y, 100, 60);
                                RemoveAttachment::dispatch([
                                    $specimen,
                                    $newname
                                ])->delay(now()->addMinutes(15));
                            } else {
                                $fpdi->Image($qrImage, $x, $y, 65, 65);
                            }
                        } else {
                            $approvalStage = ViewApprovalStage::where('DocumentReferenceID', $document->document_number)
                                ->orderBy('Sequence', 'desc')
                                ->first();
                            $approver = ViewEmployee::where('EmployeeCode', $approvalStage->ApproverCode)->first();
                            $approvalUser = User::where('username', $approver->Nik)->first();
                            $specimenFile = $this->base64ToFile($approvalUser->specimen_sign, public_path('images/approval/'), $approvalUser->username);
                            $specimen = public_path('images/approval/' . $specimenFile);
                            $newname = public_path('images/speciment/' . $file_export_name . '.png');

                            $img_canvas = Image::canvas(500, 300);
                            $img_canvas->insert(Image::make($qrImage)->resize(300, 300));
                            $img_canvas->insert(Image::make($specimen)->resize(200, null, function ($constraint) {
                                $constraint->aspectRatio();
                            }), 'top-right'); // add offset
                            $img_canvas->save($newname, 100);

                            $fpdi->Image($newname, $x, $y, 100, 60);

                            RemoveAttachment::dispatch([
                                $specimen,
                                $newname
                            ])->delay(now()->addMinutes(15));
                        }
                    }
                }
            }
            $qrCodeService->generatePath(public_path() . '/documents/');
            $outputFilePath = public_path('/documents/' . $fileName->file_name);
            $fpdi->Output($outputFilePath, 'F');

            $all_files = [
                $qrImage,
                $outputFilePath
            ];

            RemoveAttachment::dispatch($all_files)->delay(now()->addMinutes(15));

            $dataUpdate = [
                'batch_id' => 0,
                'status' => 'Processing ' . 100 . '%',
                'callback_message' => 'Success',
                'callback_trace' => 'Success Submit Manual Sign'
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
        } catch (\Exception $exception) {
            $dataUpdate = [
                'batch_id' => 0,
                'status' => 'Processing ' . 100 . '%',
                'callback_message' => 'Failed manual sign: ' . $exception->getMessage(),
                'callback_trace' => ''
            ];
            $this->createApprovalBatchLog($dataCheck, $dataUpdate);
            $this->fail($exception);
            throw new \Exception('MANUAL SIGN: ' . $exception->getMessage() . ' trace:' .  $exception->getTraceAsString(), 1);
        }
    }

    /**
     * base64ToFile
     * @param $base64
     * @param $path
     * @param int $width
     * @param int $height
     * @return string
     * @info usage 'Image' => Intervention\Image\Facades\Image::class
     */
    public static function base64ToFile($base64, $path, $fileName, $width = 400, $height = 400)
    {
        $image = str_replace('data:image/png;base64,', '', $base64);
        $image = str_replace(' ', '+', $image);
        $imageName = $fileName . '.png';
        $path = $path . $imageName;
        $input = File::put($path, base64_decode($image));
        $image = Image::make($path)->resize($width, $height);
        $result = $image->save($path);

        return $imageName;
    }
}
