<?php

namespace App\Models\Approval;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Approval\ApprovalRule
 *
 * @property int $id
 * @property int $approval_id
 * @property string $name
 * @property string $operator
 * @property array $value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule whereApprovalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule whereOperator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalRule whereValue($value)
 * @mixin \Eloquent
 */
class ApprovalRule extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $casts = [
        'value' => 'array',
        'approval_id' => 'integer',
    ];
}
