<?php

namespace App\Models\Common;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Common\ApdNote
 *
 * @property int $id
 * @property string $notes
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ApdNote newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApdNote newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApdNote query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApdNote whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApdNote whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApdNote whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApdNote whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApdNote whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ApdNote extends Model
{
    use HasFactory;

    protected $guarded = [];
}
