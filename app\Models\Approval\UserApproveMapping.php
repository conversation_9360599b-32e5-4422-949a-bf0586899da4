<?php

namespace App\Models\Approval;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Approval\UserApproveMapping
 *
 * @property int $id
 * @property int $approval_id
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Approval\Approval|null $approval
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserApproveMapping newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserApproveMapping newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserApproveMapping query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserApproveMapping whereApprovalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserApproveMapping whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserApproveMapping whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserApproveMapping whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserApproveMapping whereUserId($value)
 * @mixin \Eloquent
 */
class UserApproveMapping extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function approval()
    {
        return $this->belongsTo(Approval::class, 'approval_id', 'id');
    }
}
