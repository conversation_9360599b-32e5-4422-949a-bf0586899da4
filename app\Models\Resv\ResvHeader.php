<?php

namespace App\Models\Resv;

use App\Models\Inventory\Inventory;
use App\Models\Inventory\InventoryDetail;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * App\Models\Resv\ResvHeader
 *
 * @property int $U_DocEntry
 * @property int $DocNum
 * @property string|null $DocDate
 * @property string|null $RequiredDate
 * @property int|null $Requester
 * @property string|null $Division
 * @property string|null $Department
 * @property string|null $Company
 * @property string|null $Memo
 * @property string|null $Canceled
 * @property string|null $DocStatus
 * @property string|null $ApprovalStatus
 * @property int|null $ApprovalKey
 * @property string|null $isConfirmed
 * @property string|null $ConfirmDate
 * @property int|null $ConfirmBy
 * @property int|null $SAP_GIRNo
 * @property int|null $SAP_TrfNo
 * @property int|null $SAP_PRNo
 * @property string|null $CreateDate
 * @property string|null $CreateTime
 * @property int|null $CreatedBy
 * @property string|null $UpdateDate
 * @property string|null $UpdateTime
 * @property int|null $UpdatedBy
 * @property string|null $RequestType
 * @property string|null $U_NIK
 * @property string|null $WhsCode
 * @property string|null $WhTo
 * @property string|null $Token
 * @property string|null $CreatedName
 * @property string|null $RequesterName
 * @property string|null $UrgentReason
 * @property string|null $ItemType
 * @property string|null $Is_Urgent
 * @property string|null $U_ATTACH
 * @property string|null $CategoryType
 * @property string|null $UsageFor
 * @property string|null $VehicleNo
 * @property string|null $Mileage
 * @property string|null $Customer
 * @property int|null $SAP_SONo
 * @property string|null $Replacement
 * @property string|null $EmployeeType
 * @property string|null $WorkLocation
 * @property string|null $DocumentType
 * @property string|null $CostType
 * @property string|null $Usage
 * @property int|null $SAP_GIRNoOld
 * @property int|null $SAP_PRNoOld
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Resv\ResvDetail> $details
 * @property-read int|null $details_count
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader query()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereApprovalKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereApprovalStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCanceled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCategoryType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereConfirmBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereConfirmDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCostType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCreateDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCreateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCreatedName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereCustomer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereDivision($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereDocDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereDocNum($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereDocStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereDocumentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereEmployeeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereIsConfirmed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereIsUrgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereItemType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereMemo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereMileage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereReplacement($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereRequestType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereRequester($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereRequesterName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereRequiredDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereSAPGIRNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereSAPGIRNoOld($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereSAPPRNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereSAPPRNoOld($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereSAPSONo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereSAPTrfNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUATTACH($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUDocEntry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUNIK($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUpdateDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUpdateTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUrgentReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUsage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereUsageFor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereVehicleNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereWhTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereWhsCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereWorkLocation($value)
 * @property string|null $ExpiredDate
 * @property-read User|null $creator
 * @property-read InventoryDetail|null $issueReceipt
 * @method static \Illuminate\Database\Eloquent\Builder|ResvHeader whereExpiredDate($value)
 * @mixin \Eloquent
 */
class ResvHeader extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable;

    use HasFactory;

    protected $primaryKey = 'U_DocEntry';
    protected $guarded = [];
    protected $connection = 'sqlsrv';

    public function details()
    {
        return $this->hasMany(ResvDetail::class, 'U_DocEntry', 'U_DocEntry');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'U_NIK', 'username');
    }

    public function issueReceipt()
    {
        return $this->belongsTo(InventoryDetail::class, 'DocNum', 'resv_number');
    }
}
