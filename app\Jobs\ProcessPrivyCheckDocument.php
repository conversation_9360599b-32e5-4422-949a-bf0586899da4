<?php

namespace App\Jobs;

use App\Models\Document\Document;
use App\Services\ApprovalPrivyService;
use App\Traits\AppConfig;
use Carbon\Carbon;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class ProcessPrivyCheckDocument implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use AppConfig;
    public $tries = 1;
    public $timeout = 0;
    protected $document;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($document)
    {
        $this->document = $document;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        sleep(40);

        $response = $this->process()->collect();
        if ($response['data']['status'] != 'completed') {
            sleep(5);
            $this->process();
        }
    }

    protected function process()
    {
        $row = Document::find($this->document->id);
        $service = new ApprovalPrivyService();
        $service->login();

        $url = $this->getConfigByName('PrivyBaseUrl', 'PRIVY') . $this->getConfigByName('PrivyCheckDocumentRegister', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');
        $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $this->signature($row, $timestamp, 'POST'),
                'Request-ID' => strtotime($timestamp),
            ])
            ->post($url, $this->params($row))
            ->throw(function ($response, $e) {
                throw new \Exception('E-DIGITAL SIGN: ' . json_decode($response->collect()), 1);
            });

        $row->privy_status = $response->collect()['data']['status'];
        $row->save();

        if ($response->collect()['data']['status'] == 'completed') {
            $contents = base64_decode(str_replace('data:application/pdf;base64,', '', $response['data']['signed_document']));
            $file_name = $row->attachment->file_name;
            $file_name = (Str::contains($file_name, '_token_')) ? substr($file_name, 0, -19) . '.pdf' : $file_name;
            $path_download = public_path('documents/' . $file_name);
            custom_disk_put('documents/' . $file_name, $contents);
            // file_put_contents($path_download, $contents);
        }
        return $response;
    }

    public function params($document)
    {
        $channelId = $this->getConfigByName('PrivyChannelId', 'PRIVY', $document->company);
        return [
            "reference_number" => $document->reference_number,
            "channel_id" => $channelId,
            "document_token" => $document->document_token,
            "info" => ""
        ];
    }

    protected function signature($row, $timestamp, $httpVerb)
    {
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $body = $this->params($row);

        $strBody = json_encode($body);
        $strBody = str_replace(" ", "", $strBody);
        $method = $httpVerb;
        $body_md5 = base64_encode(md5($strBody, true));
        $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        $byte_key = utf8_encode($api_secret);
        $new_hmac = utf8_encode($hmac_signature);
        $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        $base64_message = base64_encode($hmac);
        $auth_string = $api_key . ":" . $base64_message;
        $signature = base64_encode($auth_string);
        return $signature;
    }
}
