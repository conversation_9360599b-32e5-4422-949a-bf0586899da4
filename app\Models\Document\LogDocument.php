<?php

namespace App\Models\Document;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Document\LogDocument
 *
 * @property int $id
 * @property string $log_type
 * @property int $log_id
 * @property string $event_name
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument query()
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument whereEventName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument whereLogId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument whereLogType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LogDocument whereUserId($value)
 * @mixin \Eloquent
 */
class LogDocument extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
}
