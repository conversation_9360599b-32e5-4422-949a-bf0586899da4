<?php

use App\Http\Controllers\Reservatin\PrintReservationController;
use App\Http\Controllers\Reservation\SapDocumentController;
use App\Http\Controllers\Reservation\TransactionApprovalController;
use App\Http\Controllers\Reservation\TransactionReservationController;
use Illuminate\Support\Facades\Route;

Route::get('print', [PrintReservationController::class, 'index']);
Route::get('max-doc-resv', [TransactionReservationController::class, 'maxDocResv']);
Route::get('fetch-docnum', [TransactionReservationController::class, 'fetchDocNum']);
Route::get('approval-list', [TransactionApprovalController::class, 'index']);
Route::get('approval-stages', [TransactionApprovalController::class, 'approvalStages']);
Route::get('document/{document}', [TransactionReservationController::class, 'getDocument']);
Route::get('sap-document', [SapDocumentController::class, 'index']);

Route::delete('/{id}', [TransactionReservationController::class, 'destroy']);
Route::delete('delete-all/{id}', [TransactionReservationController::class, 'deleteAll']);

Route::post('submit-approval-resv', [TransactionReservationController::class, 'submitApprovalReservation']);
Route::post('submit-approval', [TransactionReservationController::class, 'submitApproval']);
Route::post('action', [TransactionApprovalController::class, 'action']);
Route::put('cancel-document', [TransactionApprovalController::class, 'cancelDocument']);

Route::apiResource('master', TransactionReservationController::class);
Route::apiResource("req-item", \App\Http\Controllers\Reservation\ReqItemController::class);
