<?php

namespace App\Services;

use App\Models\Master\ApiResponse;
use App\Models\Resv\ResvHeader;
use App\Traits\AppConfig;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;

class ApprovalEngineService
{
    use AppConfig;

    public function getDocumentType()
    {
        $initializeData = $this->initializeData();

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withHeaders($initializeData['header'])
            ->get($initializeData['documentType'], [
                "c" => 9999999
            ]);

        if ($response->failed()) {
            throw new \Exception($response->body(), 1);
        }

        return $response->collect();
    }

    public function approvalList(ResvHeader $header, string $orderName = "CreatedAt", string $orderOperator = "DESC")
    {
        $initializeData = $this->initializeData();

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withHeaders($initializeData['header'])
            ->post($initializeData['approvalList'], [
                "Sorts" => [
                    $orderName => $orderOperator
                ],
                "Filters" => [
                    [
                        "ReferenceId",
                        "=",
                        'ERESV' . $header->DocNum,
                    ]
                ]
            ]);

        if ($response->failed()) {
            throw new \Exception($response->body(), 1);
        }

        return $response->collect()["Data"]["Items"];
    }

    public function approvalView(ResvHeader $header)
    {
        $initializeData = $this->initializeData();
        $approvalList = $this->approvalList($header);

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withHeaders($initializeData['header'])
            ->get($initializeData['approvalView'], [
                "keys" => $approvalList[0]['Keys'],
                "signature" => $approvalList[0]['Signature'],
            ]);

        if ($response->failed()) {
            throw new \Exception($response->body(), 1);
        }

        return $response->body();
    }

    public function approvalDetail(ResvHeader $header)
    {
        $initializeData = $this->initializeData();
        $approvalList = $this->approvalList($header);

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withHeaders($initializeData['header'])
            ->get($initializeData['approvalDetail'], [
                "Id" => $approvalList[0]['DocumentId'],
            ]);

        if ($response->failed()) {
            throw new \Exception($response->body(), 1);
        }

        return $response->collect();
    }

    public function cancelApproval(ResvHeader $header)
    {
        $initializeData = $this->initializeData();

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withHeaders($initializeData['header'])
            ->post($initializeData['approvalCancel'] . '?referenceId=' . 'ERESV' .$header->DocNum);

        if ($response->failed()) {
            throw new \Exception($response->body(), 1);
        }

        return $response->collect();
    }

    private function paramsEreservation($header, $reservationCode, $documentContent)
    {
        $docNum = $header->DocNum;
        return [
            'ReferenceId' => 'ERESV' . $docNum,
            'TypeCode' => $reservationCode,
            'NIK' => $header->Requester,
            'Date' => Carbon::now()->format('Y-m-d\TH:i:s.v\Z'),
            'Description' => "E-RESERVATION $docNum",
            'Parameters' => [
                'DocumentContent' => $documentContent,
            ],
            "AttachmentPaths" => [

            ],
            "AdditionalCriteria" => [],
        ];
    }

    private function paramsEMeterai($header, $reservationCode, $documentContent)
    {
        return [
            'ReferenceId' => $header->document_number,
            'TypeCode' => $reservationCode,
            'NIK' => $header->userCreate->username,
            'Date' => Carbon::now()->format('Y-m-d\TH:i:s.v\Z'),
            'Description' => $header->remark,
            'Parameters' => [
                'DocumentContent' => $documentContent,
            ],
            "AttachmentPaths" => [
                '/eresv/eresv/Attachment/docs/' . $header->attachment->file_name
            ],
            "AdditionalCriteria" => [],
        ];
    }

    public function submitApproval($header, $reservationCode, $documentContent, $approvalType = 'e-reservation')
    {
        $initializeData = $this->initializeData();

        if ($approvalType == 'e-meterai') {
            $params = $this->paramsEMeterai($header, $reservationCode, $documentContent);
        } else {
            $params = $this->paramsEreservation($header, $reservationCode, $documentContent);
        }

        $request = [
            "header" => $initializeData['header'],
            "params" => $params
        ];

        try {
            $response = Http::withoutVerifying()
                ->timeout(90)
                ->retry(3, 100)
                ->withOptions([
                    "verify" => false,
                ])
                ->withHeaders($initializeData['header'])
                ->post($initializeData['createDocument'], $params);

            if ($response->failed()) {
                $errorBody = $response->json();
                $errorMessage = isset($errorBody['Message'])
                    ? sprintf(
                        "HTTP request returned status code %d: %s",
                        $response->status(),
                        substr($errorBody['Message'], 0, 10000) // Increase substring length
                    )
                    : $response->body();

                $this->createApiResponse(
                    'Error',
                    $initializeData['createDocument'],
                    $request,
                    $errorBody,
                    $header
                );

                throw new \Exception($errorMessage, $response->status());
            }

            $responseData = $response->json();

            $this->createApiResponse(
                'Success',
                $initializeData['createDocument'],
                $request,
                $responseData,
                $header
            );

            return $responseData;

        } catch (\Exception $e) {
            $this->createApiResponse(
                'Error',
                $initializeData['createDocument'],
                $request,
                $e->getMessage(),
                $header
            );
            throw new \Exception($e->getMessage(), 1);
        }
    }

    private function createApiResponse($message, $url, $params, $responseData, $header)
    {
        ApiResponse::create([
            'message' => $message,
            'url' => $url,
            'params' => json_encode($params),
            'response' => json_encode($responseData),
            'reservation_number' => $header->DocNum,
            'type' => 'ApprovalEngine'
        ]);
    }


    private function initializeData(): array
    {
        $clientId = $this->getConfigByName('ApprovalClientId', 'ApprovalService');
        $clientSecret = $this->getConfigByName('ApprovalClientSecret', 'ApprovalService');
        $baseUrl = $this->getConfigByName('ApprovalBaseUrl', 'ApprovalService');
        $documentType = $this->getConfigByName('ApprovalDocumentType', 'ApprovalService');
        $createDocument = $this->getConfigByName('ApprovalCreate', 'ApprovalService');
        $approvalList = $this->getConfigByName('ApprovalList', 'ApprovalService');
        $approvalView = $this->getConfigByName('ApprovalView', 'ApprovalService');
        $approvalCancel = $this->getConfigByName('ApprovalCancel', 'ApprovalService');
        $approvalDetail = $this->getConfigByName('ApprovalDetail', 'ApprovalService');
        $appId = $this->getConfigByName('ApprovalAppId', 'ApprovalService');
        $token = base64_encode($clientId . ':' . $clientSecret);

        return [
            "token" => $token,
            "clientId" => $clientId,
            "clientSecret" => $clientSecret,
            "baseUrl" => $baseUrl,
            "documentType" => $baseUrl . $documentType,
            "createDocument" => $baseUrl . $createDocument,
            "approvalList" => $baseUrl . $approvalList,
            "approvalView" => $baseUrl . $approvalView,
            "approvalDetail" => $baseUrl . $approvalDetail,
            "approvalCancel" => $baseUrl . $approvalCancel,
            "header" => [
                'Authorization' => 'Basic ' . $token,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'AppId' => $appId,
            ]
        ];
    }
}

