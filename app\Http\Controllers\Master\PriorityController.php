<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Master\MasterPriority;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PriorityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $category = $request->formData;
        $priority = MasterPriority::select('id', 'title')
            ->get();

        $simple_data = MasterPriority::select('id', 'title')
            ->pluck('title');

        if (count($priority) < 1) {
            $priority = [
                [
                    'id' => null,
                    'title' => null,
                ]
            ];
        }

        if (count($simple_data) < 1) {
            $simple_data = [''];
        }

        return $this->success([
            'rows' => $priority,
            'header' => ['Id', 'Name'],
            'simple' => $simple_data
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $details = collect($request->details);
        DB::beginTransaction();
        try {
            foreach ($details as $detail) {
                if (empty($detail['title'])) {
                    return $this->error('Title cannot empty', '422');
                }
                $brand = MasterPriority::where('id', '=', $detail['id'])->first();
                if ($brand) {
                    $brand->title = Str::ucfirst($detail['title']);
                    $brand->updated_at = Carbon::now();
                } else {
                    $brand = new MasterPriority();
                    $brand->title = Str::ucfirst($detail['title']);
                    $brand->created_at = Carbon::now();
                }
                $brand->save();
            }

            DB::commit();
            return $this->success([
                'emitName' => 'sub_category'
            ], 'Rows updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $category = $request->category;
        $brand = MasterPriority::where('category', '=', $category)
            ->pluck('title');
        return $this->success($brand);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Master\MasterPriority $productBrand
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, MasterPriority $productBrand)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            MasterPriority::whereIn('id', $id)->delete();
            DB::commit();
            return $this->success([], 'Data updated!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422');
        }
    }
}
