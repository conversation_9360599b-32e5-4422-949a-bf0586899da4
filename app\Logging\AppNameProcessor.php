<?php

namespace App\Logging;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;

class AppNameProcessor implements ProcessorInterface
{
    protected $appName;

    public function __construct($appName)
    {
        $this->appName = $appName;
    }

    public function __invoke(LogRecord $record)
    {
        $record['extra']['app_name'] = $this->appName;
        return $record;
    }
}
