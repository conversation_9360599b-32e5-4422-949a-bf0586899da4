<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\ResvReqItem
 *
 * @property int $id
 * @property string $specification
 * @property string $item_group
 * @property string $uom
 * @property string $supporting_data
 * @property string $description
 * @property int $created_id
 * @property int $whs_admin
 * @property string $doc_status
 * @property string $insert_to_sap
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereCreatedId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereDocStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereInsertToSap($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereItemGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereSpecification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereSupportingData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResvReqItem whereWhsAdmin($value)
 * @property int $U_DocEntry
 * @property string|null $date_out
 * @property string|null $item_code
 * @property string|null $item_name
 * @property string|null $id_card
 * @property string|null $employee_name
 * @property string|null $company
 * @property string|null $department
 * @property int|null $created_by
 * @property string|null $notes
 * @property float|null $qty
 * @property int|null $header_id
 * @property int|null $detail_id
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereDateOut($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereDetailId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereEmployeeName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereHeaderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereIdCard($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereItemCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereItemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SafetyData whereUDocEntry($value)
 * @mixin \Eloquent
 */
class SafetyData extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $connection = 'sqlsrv';
    protected $primaryKey = 'U_DocEntry';
    protected $table = 'safety_data';
}
