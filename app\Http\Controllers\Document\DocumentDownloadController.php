<?php

namespace App\Http\Controllers\Document;

use App\Http\Controllers\Controller;
use App\Jobs\RemoveAttachment;
use App\Models\Common\Attachment;
use App\Models\Document\Document;
use App\Models\View\ViewApprovalStage;
// use App\Override\SignaturePdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
// use LSNepomuceno\LaravelA1PdfSign\{ManageCert, SignaturePdf};
use LSNepomuceno\LaravelA1PdfSign\Sign\ManageCert;
use LSNepomuceno\LaravelA1PdfSign\Sign\SignaturePdf;
use <PERSON>nest\Madzipper\Madzipper as Zipper;
use RuntimeException;
use Illuminate\Support\Facades\Artisan;

class DocumentDownloadController extends Controller
{
    /**
     * Downloads a document given a request containing the document ID and type.
     *
     * @param Request $request The HTTP request containing the ID and type of the document to download.
     * @throws \Exception If the document fails to download.
     */
    public function downloadDocument(Request $request)
    {
        try {
            $doc_id = (array) $request->id;
            $type = $request->type;
            foreach ($doc_id as $key => $id) {
                $document = Document::find($id);
                // return $this->error($document->ref_token);
                // $document->ref_token = $response['result']['sn'];
                // $document->save();
                $file_name = $document->attachment->file_name;
                $file_name = (Str::contains($file_name, '_token_')) ? substr($file_name, 0, -19) . '.pdf' : $file_name;
                $file_names = (Str::contains($file_name, '_token_')) ? substr($file_name, 0, -19) . '.pdf' : $file_name;

                $includeTokenInFileName = $this->getConfigByName('IncludeTokenInFileName', 'GENERAL');

                if ($includeTokenInFileName == 'Y' && Str::contains($document->external_document_number, ['IMIP/DN'])) {
                    $file_name = strtoupper(Str::slug(str_replace('/', '-', $document->external_document_number))) . '.pdf';
                }

                if ($document->internal_document != 'Y') {
                    $checkUseDigisign = $this->getConfigByName('UseDigisign', 'GENERAL');
                    // if ($checkUseDigisign == '1' && !Str::of($document->company)->contains(['PT BDM', 'PT MMM'])) {
                    // if (!$document->privy_status) {
                    if ($checkUseDigisign == '1') {
                        if ($document->digisign_coordinate == 'Y') {
                            // $this->bulkSignDocument($document);

                            $this->downloadDigisign($document, $file_name, $request);
                        }
                    }
                }


                if ($type == 'batch') {
                    if ($document->internal_document == 'Y') {
                        create_file_delete_job('documents/' . $file_name);
                        $all_pdf[] = public_path('documents/' . $file_name);
                    } else {
                        if ($document->digisign_coordinate == 'Y') {
                            create_file_delete_job('documents/' . $file_name);
                            $all_pdf[] = public_path('documents/' . $file_name);
                        } else {
                            $fileNames = Attachment::where('source_id', $document->id)
                                ->where('type', 'peruri')
                                ->first();
                            create_file_delete_job('documents/' . $fileNames->file_name);
                            $all_pdf[] = public_path('report/documents/' . $fileNames->file_name);
                        }
                    }
                } else {
                    $headers = [
                        'Content-Type' => 'application/pdf',
                        'Content-Disposition' => 'attachment; filename="' . $file_names . '"',
                    ];

                    if ($document->internal_document == 'Y') {
                        create_file_delete_job('documents/' . $file_name);
                        $file = public_path('documents/' . $file_name);
                        return $this->signWithInternalCert($file, $document);
                        // return Response::make(Storage::disk('app_documents')
                        //     ->get('/documents/' . $file_name), 200, $headers);
                    } else {
                        // Artisan::call('cache:clear');
                        // Artisan::call('config:cache');

                        if ($document->digisign_coordinate == 'Y') {
                            create_file_delete_job('documents/' . $file_name);
                            return response()->download(public_path('/documents/' . $file_name));
                            // return Response::make(Storage::disk('app_documents')
                            //     ->get('/documents/' . $file_name), 200, $headers);
                        } else {
                            $fileNames = Attachment::where('source_id', $document->id)
                                ->where('type', 'peruri')
                                ->first();
                            // $file_name = public_path('documents/' . $file_name);
                            create_file_delete_job('documents/' . $file_name);
                            return response()->download(public_path('/documents/' . $fileNames->file_name));
                            // return Response::make(Storage::disk('app_public')
                            //     ->get('/documents/' . $fileNames->file_name), 200, $headers);
                        }
                    }
                }
            }

            $zipper = new Zipper();
            $zip_path = public_path() . '/documents/';
            if (!file_exists($zip_path)) {
                if (!mkdir($zip_path, 0777, true) && !is_dir($zip_path)) {
                    throw new RuntimeException(sprintf(
                        'Directory "%s" was not created',
                        $zip_path
                    ));
                }
            }
            $file_name = 'E-MATERAI-BATCH' . date('YmdHis') . '.zip';
            $data_file = public_path('documents/' . $file_name);
            if (file_exists($data_file)) {
                unlink($data_file);
            }
            $zipper->make($data_file)->add($all_pdf);
            $zipper->close();
            $headers = [
                'Content-Type: application/zip',
                'Content-Disposition' => 'attachment; filename="' . $file_name . '"'
            ];

            RemoveAttachment::dispatch([$data_file])->delay(now()->addMinutes(30));
            return response()->download($data_file);
            // return Response::make(Storage::disk('app_documents')
            //     ->get('/documents/' . $file_name), 200, $headers);
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTraceAsString()
            ]);
        }
    }

    private function bulkSignDocument($document)
    {
        if (date('Y-m-d', strtotime($document->created_at)) < '2022-06-27') {
            $doc_id = Str::slug(strtoupper($document->document_number));
        } else {
            $doc_id = Str::slug(strtoupper($document->external_document_number));
        }

        $token = $this->getConfigByName('TokenEsign', 'ESIGN', $document->company);
        $url = $this->getConfigByName('BulkSign', 'ESIGN');
        $esignUserId = $this->getConfigByName('UserIdEsign', 'ESIGN', $document->company);

        $str = Str::random(20);

        $options = [
            'jsonfield' => json_encode(
                [
                    'JSONFile' => [
                        'userid' => $esignUserId,
                        'email_user' => $esignUserId,
                        // 'document_id' => [$doc_id, $doc_id],
                        'document_id' => ['0363bdminvv23', '0362bdminvv23'],
                        'must_read' => false
                    ],
                ]
            ),
        ];

        // $headers[] = "Authorization: Bearer $token; Content-Type: multipart/form-data; boundary=" . $str;
        $headers[] = "Authorization: Bearer $token";
        // $headers[] = "Authorization: Bearer $token;";
        // throw new \Exception($url, 1);
        // throw new \Exception(json_encode($options), 1);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl, CURLOPT_VERBOSE, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_TIMEOUT, 95);
        // timeout in seconds
        curl_setopt($curl, CURLOPT_POSTFIELDS, $options);

        $MAX_RETRIES = 5;
        for ($i = 0; $i < $MAX_RETRIES; $i++) {

            // Send the request and save response to $response
            $response = curl_exec($curl);

            // Stop if fails
            if (!$response) {
                // Log::info('result from approve document failed', [
                //     'result' => curl_error($curl) . '" - Code: ' . curl_errno($curl),
                //     'response' => $response
                // ]);
                die('Error: "' . curl_error($curl) . '" - Code: ' . curl_errno($curl));
            }

            $status_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            // If it's a successful request (200 or 404 status code):
            if (in_array($status_code, array(200, 404))) {
                // $response = curl_exec($curl);
                curl_close($curl);
                // dd($options);
                // dd(config('app.sign_url_download'));
                $response_text = json_decode($response);
                // return $this->error('', 422, $response);
                // dd($response);
                $collect_response = collect($response_text);

                throw new \Exception(json_encode($collect_response), 1);
                if ($collect_response['JSONFile']->result != '00') {
                    throw new \Exception(json_encode($collect_response), 1);
                }
                // $contents = base64_decode($response->collect()['JSONFile']['file']);
                // $contents = base64_decode($collect_response['JSONFile']->file);
                // $file_name = $doc_id . '_DOWNLOAD.pdf';
                // $path_download = public_path('documents/' . $file_name);
                // file_put_contents($path_download, $contents);

                // Remove Attachment
                // RemoveAttachment::dispatch([$path_download])->delay(now()->addMinutes(30));
                // echo 'Response Body: ' . $response . PHP_EOL;
                break;
            }
        }
    }

    /**
     * Downloads a digisign document.
     *
     * @param object $document The document object.
     * @param string $file_name The name of the file.
     * @param object $request The request object.
     * @throws \Exception An exception if the download fails.
     */
    protected function downloadDigisign($document, $file_name, $request)
    {
        if (date('Y-m-d', strtotime($document->created_at)) < '2022-06-27') {
            $doc_id = Str::slug(strtoupper($document->document_number));
        } else {
            $doc_id = Str::slug(strtoupper($document->external_document_number));
        }

        // there are no digisign error message, next step process the download file
        $esignUserId = $this->getConfigByName('UserIdEsign', 'ESIGN', $document->company);
        $token = $this->getConfigByName('TokenEsign', 'ESIGN', $document->company);

        $url = $this->getConfigByName('DownloadBase64', 'ESIGN');
        $options = [
            'jsonfield' => json_encode(
                [
                    'JSONFile' => [
                        // "userid" => $user_id,
                        'userid' => $esignUserId,
                        'document_id' => $doc_id,
                        // "document_id" => "SNI7",
                    ],
                ]
            ),
        ];

        // Log::info('params digisign', [$options]);

        $headers[] = "Authorization: Bearer $token";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        // curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($curl, CURLOPT_VERBOSE, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_TIMEOUT, 95);
        // timeout in seconds
        curl_setopt($curl, CURLOPT_POSTFIELDS, $options);

        // Set max retries:
        $MAX_RETRIES = 5;
        for ($i = 0; $i < $MAX_RETRIES; $i++) {

            // Send the request and save response to $response
            $response = curl_exec($curl);

            // Stop if fails
            if (!$response) {
                // Log::info('result from download document failed', [
                //     'result' => curl_error($curl) . '" - Code: ' . curl_errno($curl)
                // ]);
                die('Error: "' . curl_error($curl) . '" - Code: ' . curl_errno($curl));
            }

            $status_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            // If it's a successful request (200 or 404 status code):
            if (in_array($status_code, array(200, 404))) {
                // $response = curl_exec($curl);
                curl_close($curl);
                // dd($options);
                // dd(config('app.sign_url_download'));
                $response_text = json_decode($response);
                // return $this->error('', 422, $response);
                // dd($response);
                $collect_response = collect($response_text);

                // throw new \Exception(json_encode($collect_response), 1);

                // $contents = base64_decode($response->collect()['JSONFile']['file']);
                $contents = base64_decode($collect_response['JSONFile']->file);
                // Log::info('base64_decode', [$collect_response['JSONFile']->file]);
                // $file_name = $doc_id . '_DOWNLOAD.pdf';

                // Log::info('file_name ' . $file_name);
                $path_download = public_path('documents/' . $file_name);
                // $path_download = public_path('docs/' . $file_name);
                // file_put_contents($path_download, $contents);
                custom_disk_put('documents/' . $file_name, $contents);

                // Remove Attachment
                // RemoveAttachment::dispatch([$path_download])->delay(now()->addMinutes(30));
                // echo 'Response Body: ' . $response . PHP_EOL;
                break;
            }
        }


        $this->createLog(
            $document->id,
            'document',
            'Digisign - Download document ID ' . $doc_id . ', userid : ' . $esignUserId,
            $request->user()->id
        );
    }

    /**
     * Sign a file using an internal certificate.
     *
     * @param string $file The file to sign.
     * @throws \Exception If an error occurs while signing the file.
     * @return string The signed resource string.
     */
    protected function signWithInternalCert($file, $document)
    {
        // FROM FILE
        try {
            Storage::disk('local')->copy('imip_co_id.pfx', 'public/imip_co_id.pfx');

            $cert = new ManageCert;
            $cert->fromPfx(storage_path('app/public/imip_co_id.pfx'), 'imip*8Ultra');
        } catch (\Throwable $th) {
            throw new \Exception($th->getMessage(), 1);
        }

        // Returning signed resource string
        try {
            $approver = ViewApprovalStage::where('DocumentReferenceID', $document->document_number)->first();
            $specimen = public_path('images/speciment/' . $document->str_url . '.png');

            $x = $document->digisign_left;
            $y = $document->digisign_top;
            $width = 100;
            $height = 0;

            $pdf = new SignaturePdf($file, $cert, SignaturePdf::MODE_RESOURCE); // Resource mode is default
            $resource = $pdf
                // ->setImage(
                //     $specimen,
                //     $x,
                //     $y,
                //     $width,
                //     $height,
                //     (int)$document->sign_page
                // )
                ->setInfo(
                    $document->identify_name,
                    $document->location,
                    $document->reason,
                    $approver->Name
                )
                ->setPdf('P', 'pt')
                ->signature();
            return $resource;
        } catch (\Throwable $th) {
            throw new \Exception($th->getMessage(), 1);
        }
    }
}
