<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        channels: __DIR__ . '/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            // \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'direct_permission' => \App\Http\Middleware\DirectPermission::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            // 'jwt.verify' => \App\Http\Middleware\JwtMiddleware::class,
            'superapps_auth' => \App\Http\Middleware\SuperAppsApiMiddleware::class,
            'superapps_api' => \App\Http\Middleware\SuperAppsMiddleware::class,
            'posting_period' => \App\Http\Middleware\PostingPeriodMiddleware::class,
            'auth_service' => \App\Http\Middleware\AuthServiceMiddleware::class,
        ]);
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->truncateRequestExceptionsAt(240);
    })->create();
