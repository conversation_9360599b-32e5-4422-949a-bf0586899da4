<?php

namespace App\Http\Controllers\Paper;

use App\Http\Controllers\Controller;
use App\Jobs\RemoveAttachment;
use App\Models\Common\Attachment;
use App\Models\Paper\Paper;
use App\Models\Paper\PaperDetails;
use App\Models\View\ViewEmployee;
use App\Services\PaperService;
use App\Traits\CherryApproval;
use App\Traits\RolePermission;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PaperController extends Controller
{
    use CherryApproval, RolePermission;

    public $service;

    public function __construct(PaperService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $options = json_decode($request->options);
            $item = json_decode($request->item);
            $pages = isset($request->page) ? (int) $request->page : 1;
            $clinic = isset($request->clinic) ? $request->clinic : 'N';
            $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
            $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : 'papers.paper_no';
            $order = (!empty($options->sortDesc)) ? (($options->sortDesc[0]) ? "desc" : "desc") : 'desc';

            $user_id = isset($request->user_id) ? (string) $request->user_id : '';
            $swabDate = isset($request->swabDate) ? (string) $request->swabDate : '';
            $search_item = isset($request->searchItem) ? (string) $request->searchItem : '';
            $search = isset($request->search) ? (string) $request->search : '';
            $type = isset($request->type) ? (string) $request->type : '';
            $offset = $pages;
            $status = isset($request->status) ? (string) $request->status : 'active';

            if ($status == 'All') {
                $status = '';
            }

            $default_form = Paper::where('id', '=', 1)->first()->toArray();
            $default_form['status'] = 'pending';
            // return response()->json($default_form);

            $result = [];
            $query = Paper::select(
                'papers.*',
                'B.name as paper_name',
                'B.alias',
            )
                ->leftJoin('master_papers as B', 'b.id', 'papers.master_paper_id')
                ->where('papers.deleted', '=', 'N')
                ->orderBY($sorts, $order);

            if ($clinic == 'Y') {
                $query = $query->whereIn('B.alias', ['srm', 'srk'])
                    ->where('papers.status', '=', 'active')
                    ->where('papers.swab_date', 'LIKE', '%' . $swabDate . '%');
            } else {
                $query = $query->where('B.alias', '=', $type)
                    ->where('papers.status', 'LIKE', '%' . $status . '%');
            }

            if ($type == 'stkpd') {
                if (!$request->user()->hasAnyRole(['Superuser', 'HRD Jakarta'])) {
                    $query = $query->where("papers.user_id", '=', $user_id);
                }
            } else {
                if (!$request->user()->hasAnyRole(['HRD Morowali', 'Superuser', 'Admin Klinik'])) {
                    if ($request->user()->hasAnyRole(['Admin E-FORM'])) {
                        $user_by_departemen = ViewEmployee::where("Department", "=", $request->user()->department)
                            ->pluck("Nik");
                        $query = $query->whereIn("papers.user_id", $user_by_departemen);
                    } else {
                        $query = $query->where("papers.user_id", '=', $user_id);
                    }
                }
            }

            if ($search_item == 'Employee') {
                $query = $query->where('papers.user_name', 'LIKE', '%' . $search . '%');
            } elseif ($search_item == 'Paper No') {
                $query = $query->where('papers.paper_no', 'LIKE', '%' . $search . '%');
            } elseif ($search_item == 'Created By') {
                $query = $query->where('papers.created_name', 'LIKE', '%' . $search . '%');
            }

            $result['total'] = $query->count();

            $all_data = $query->paginate($row_data)
                ->items();

            $document_status = Paper::select('status')->distinct()->get();
            $complete_status = Paper::select('is_complete')->distinct()->get();
            $filter_status = ['All'];
            $filter_complete = ['All'];

            foreach ($document_status as $value) {
                $filter_status[] = $value->status;
            }

            foreach ($complete_status as $value) {
                if ($value->is_complete == 'Y') {
                    $filter_complete[] = 'finish';
                }

                if ($value->is_complete == 'N') {
                    $filter_complete[] = 'pending';
                }
            }

            $company = ViewEmployee::distinct()
                ->pluck('Company');

            $resv_for = [
                'Own Purpose',
                'Subordinate',
                'Superior'
            ];

            $cost_cover = [
                'IMIP',
                'Guest Company',
                'Contractor'
            ];

            $travel_purpose = [
                'Duty Travel',
                'Family Visit/Yearly Leave',
                'Special Permit',
                'Others Purpose'
            ];

            $country = Storage::disk('local')->get('Data/country.json');
            $collection = collect(json_decode($country, true));
            $plucked = $collection->pluck('name');

            $master_paper = $this->service->getMasterPaper($request->type);
            $paper_no = (isset($request->type)) ? $this->service->generateDocNum(
                date('Y-m-d H:i:s'),
                $request->type,
                $master_paper->id
            ) : '';

            $result = array_merge($result, [
                'rows' => $all_data,
                'filter' => ['Paper No', 'Employee', 'Created By'],
                'document_status' => $filter_status,
                'filter_complete' => $filter_complete,
                'form' => $default_form,
                'str_url' => Str::random(40),
                'resv_for' => $resv_for,
                'travel_purpose' => $travel_purpose,
                'cost_cover' => $cost_cover,
                'country' => $plucked->all(),
                'detail' => [],
                'company' => $company,
                'paper_no' => $paper_no,
                'swabDate' => $swabDate,
            ]);

            return response()->json($result);
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * get reference no
     *
     * @param Request $request
     * @param $username
     *
     * @return JsonResponse
     */
    public function referenceNo(Request $request, $username)
    {
        $date_check = date('Y-m-d', strtotime('-40 days'));
        $reference_no = Paper::whereRaw("master_paper_id = (SELECT id from master_papers where alias='srm')")
            ->where('clinic_response', '=', 'Negatif')
            ->where('id_card', '=', $username)
            ->where('swab_date', '>=', $date_check)
            ->select('paper_no', 'user_name')
            ->get();

        return response()->json($reference_no);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            if ($request->alias == 'stkpd') {
                $check_paper = $this->checkPaperBaseIdCardAlias($request);
                if ($check_paper) {
                    return $this->error('Form Surat Ini Sudah Pernah Dibuatkan
                        Untuk Karyawan Yang Bersangkutan, Silahkan Cek Kembali!', 422);
                }
            }

            $alias = $request->alias;

            if (empty($request->form['paper_date'])) {
                return $this->error('Tanggal Surat Tidak Boleh Kosong!', 422);
            }

            if (!Str::contains($request->alias, ['rtm', 'smt'])) {
                if (empty($request->form['user_name'])) {
                    return $this->error('Karyawan Tidak Boleh Kosong!', 422);
                }
            }

            if ($alias == 'sim' || $alias == 'sik' || $alias == 'srm' || $alias == 'srk') {
                if (array_key_exists('work_location', $request->form)) {
                    if (str_contains($request->form['work_location'], 'MOROWALI')) {
                        if ($request->form['for_self'] == 'Karyawan') {
                            if ($alias == 'sim' && $request->form['swab_type'] != '3') {
                                if (empty($request->form['reference_no'])) {
                                    return $this->error('Nomor Rapid tidak boleh kosong!', 422);
                                }
                            }
                            if (empty($request->form['leave_from_to']) || empty($request->form['reference_number'])) {
                                return $this->error('Tanggal Cuti dan Nomor Cuti tidak boleh kosong!', 422);
                            }
                        }
                        $master_paper = $this->service->getMasterPaper($request->alias);
                        $check_paper = Paper::where('master_paper_id', '=', $master_paper->id)
                            ->where('reference_number', '=', $request->form['reference_number'])
                            ->where('user_id', '=', $request->username)
                            ->where('user_name', '=', $request->form['user_name'])
                            ->whereNotIn('status', ['canceled', 'rejected'])
                            ->count();
                        if ($check_paper > 0) {
                            return $this->error(
                                $master_paper->name . ' dengan Nomer cuti ' .
                                    $request->form['reference_number'] .
                                    ' Sudah ada, Silahkan pilih nomer yang lain!',
                                422
                            );
                        }
                    }
                }
            }

            if ($alias == 'sim') {
                if (
                    date('Y-m-d', strtotime($request->form['date_in']))
                    < date('Y-m-d', strtotime($request->form['paper_date']))
                ) {
                    return $this->error('Tanggal masuk kawasan harus melebihi tanggal surat!', 422);
                }
            }

            if ($alias == 'sik') {
                if (
                    date('Y-m-d', strtotime($request->form['date_out']))
                    < date('Y-m-d', strtotime($request->form['paper_date']))
                ) {
                    return $this->error('Tanggal keluar kawasan harus melebihi tanggal surat!', 422);
                }
            }

            // if ($alias == 'srm') {
            //     if (empty($request->form['date_in'])) {
            //         return $this->error('Tanggal masuk kawasan tidak boleh kosong!', 422);
            //     }

            //     if (empty($request->form['transportation'])) {
            //         return $this->error('Transportasi tidak boleh kosong!', 422);
            //     }

            //     if (empty($request->form['route'])) {
            //         return $this->error('Jalur tidak boleh kosong!', 422);
            //     }

            //     if (empty($request->form['reason'])) {
            //         return $this->error('Keperluan masuk kawasan tidak boleh kosong!', 422);
            //     }
            // }

            if ($alias == 'srm' || $alias == 'srk' || $alias == 'rtm' || $alias == 'rtk') {
                if (empty($request->form['swab_date'])) {
                    return $this->error('Tanggal Swab tidak boleh kosong!', 422);
                }
                // if (empty($request->form['name_boss'])) {
                //     return $this->error('Nama Atasan tidak boleh kosong!', 422);
                // }
                if (!empty($request->form['swab_date'])) {
                    // if (date('Y-m-d', strtotime($request->form['swab_date']))
                    //     < date('Y-m-d', strtotime(Carbon::now()))) {
                    //     return $this->error('Tanggal Swab tidak boleh kurang dari tannggal sekarang!');
                    // }
                    if (
                        date('Y-m-d', strtotime($request->form['swab_date']))
                        <= date('Y-m-d', strtotime(Carbon::now()))
                    ) {
                        if (empty($request->form['reason_swab'])) {
                            return $this->error('Alasan tanggal Swab tidak boleh kosong!');
                        }
                    }
                }
            }

            $paper = new Paper();
            $paper = $this->service->saveData($paper, $request, 'post');

            // if ($alias == 'srm') {
            //     $request->merge(['alias' => 'sim', 'form.status' => 'pending']);
            //     $data = $request->all();
            //     $data['form']['reference_no'] = $paper->paper_no;
            //     $data['form']['status'] = 'pending';
            //     // $request->form['status'] = 'pending';
            //     $paper_in = new Paper();
            //     $paper_in = $this->saveData($paper_in, (object)$data, 'post');
            // }

            $paper = Paper::leftJoin('master_papers', 'papers.master_paper_id', 'master_papers.id')
                ->select('papers.*', 'master_papers.name as paper_type', 'master_papers.alias as paper_alias')
                ->where('papers.id', '=', $paper->id)
                ->first();
            /**
             * Check if paper has attachment
             */
            $this->checkAttachment($paper);

            // if ($alias == 'srm') {
            //     $attachment = Attachment::where('source_id', '=', $paper->id)->count();
            //     if ($attachment == 0) {
            //         return $this->error("Attachment tidak boleh kosong!");
            //     }
            // }

            // if ($alias == 'srm' || $alias == 'sim') {
            //     $attachment = Attachment::where('source_id', '=', $paper->id)->count();
            //     if ($attachment == 0) {
            //         return $this->error("Attachment tidak boleh kosong!");
            //     }
            // }

            if ($alias == 'sim' && $request->form['swab_type'] != '3') {
                $attachment = Attachment::where('source_id', '=', $paper->id)->count();
                if ($attachment == 0) {
                    return $this->error("Attachment tidak boleh kosong!");
                }
            }

            if (!Str::contains($paper->paper_alias, ['stkpd'])) {
                if ($request->details) {
                    foreach ($request->details as $detail) {
                        $dataDetail = new PaperDetails();
                        $this->service->saveDetails($detail, $dataDetail, 'create', $paper->id);
                    }
                }

                if (!Str::contains($paper->paper_alias, ['ibk'])) {
                    $response_approval = $this->submitPaperApproval($paper, $request);

                    DB::commit();
                    if ($response_approval['error']) {
                        return $this->error($response_approval['message']);
                    } else {
                        return $this->success([], $response_approval['message']);
                    }
                } else {
                    DB::commit();
                    return $this->success([], 'Data saved!');
                }


                // return $this->success([], 'Data saved!');
            } else {
                return $this->success([], 'Data saved!');
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace(),
            ]);
        }
    }

    /**
     * @param $request
     *
     * @return mixed
     */
    protected function checkPaperBaseIdCardAlias($request)
    {
        $master_paper = $this->service->getMasterPaper($request->alias);
        return Paper::where('id_card', '=', $request->form['id_card'])
            ->where('master_paper_id', '=', $master_paper->id)
            ->where('status', '<>', 'canceled')
            ->first();
    }


    /**
     * @param $paper
     */
    protected function checkAttachment($paper)
    {
        $attachment = Attachment::where('str_url', '=', $paper->str_url);
        $count = $attachment->count();
        if ($count > 0) {
            $attachment->update([
                'source_id' => $paper->id
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $paper = Paper::where('papers.id', '=', $id)
            ->select(
                'papers.*',
                'B.name as flight_origin',
                'C.name as flight_destination',
                'D.name as flight_origin_approve',
                'E.name as flight_destination_approve'
            )
            ->leftJoin('airports as B', 'papers.flight_origin', 'B.id')
            ->leftJoin('airports as C', 'papers.flight_destination', 'C.id')
            ->leftJoin('airports as D', 'papers.flight_origin_approve', 'D.id')
            ->leftJoin('airports as E', 'papers.flight_destination_approve', 'E.id')
            ->first();
        return response()->json([
            'form' => $paper,
            // 'detail' => PaperDetails::where('paper_id', $id)->get()
            'detail' => $paper->lineItems
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     *
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $paper = Paper::where('id', '=', $id)->first();
            $this->service->saveData($paper, $request, 'update');

            if ($request->details) {
                foreach ($request->details as $detail) {
                    $dataDetail = PaperDetails::where('id', '=', $detail['id'])->first();
                    $this->service->saveDetails($detail, $dataDetail, 'update', $paper->id);
                }
            }
            DB::commit();

            /**
             * check response dari klinik apakah karyawan FIT atau UNFIT
             * lalu kirim notifikasi email ke created surat
             */
            $this->service->sendEmail($request, $paper);

            /**
             * Cancel Dukumen
             */
            $this->service->cancelDocument($request);


            return $this->success('Data Saved');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace(),
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function destroy($id)
    {
        try {
            Paper::where('id', $id)->delete();

            return $this->success('Form deleted');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     *
     * @return string
     *
     * @throws \PhpOffice\PhpWord\Exception\CopyFileException
     * @throws \PhpOffice\PhpWord\Exception\CreateTemporaryFileException
     */
    public function print(Request $request)
    {
        try {
            $document = $this->service->printDocument($request);
            // throw new \Exception(json_encode($document), 1);
            $file_name = $document['file_export_name'] . '.pdf';
            $headers = [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'attachment; filename="' . $file_name . '"',
            ];

            $path_download = public_path('documents/' . $file_name);

            RemoveAttachment::dispatch([$path_download])->delay(now()->addMinutes(30));

            return \Response::make(Storage::disk('app_documents')
                ->get('/documents/' . $file_name), 200, $headers);
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 500, [
                'trace' => $e->getTraceAsString()
            ]);
        }

        // return response()->download($document['pdf_file_name'],
        // $document['file_export_name'] . '.pdf', $document['headers']);
    }
}
