<?php

namespace App\Http\Controllers;

use App\Models\Common\Application;
use App\Models\Common\Company;
use App\Models\Master\Permission;
use App\Models\Paper\Role;
use App\Models\User;
use App\Models\User\UserApp;
use App\Models\User\UserCompany;
use App\Models\User\UserDivision;
use App\Models\User\UserItmGrp;
use App\Models\User\UserWhs;
use App\Models\View\ViewEmployee;
use App\Services\DocumentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{

    /**
     * @param Request $request
     *
     * @return mixed
     */
    public function register(Request $request)
    {
        try {
            $attr = $request->validate([
                'name' => 'required|string|max:255',
                'username' => 'required|string|unique:users,username',
                'email' => 'required|string|email|unique:users,email',
                'password' => 'required|string|min:6|confirmed'
            ]);

            $user = User::create([
                'name' => $attr['name'],
                'username' => $attr['username'],
                'password' => bcrypt($attr['password']),
                'email' => $attr['email']
            ]);

            return $this->success([
                'token' => $user->createToken('API Token')->plainTextToken
            ], 'User Created!');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), 401);
        }
    }

    /**
     * checkAppSession
     *
     * @param  mixed $request
     */
    public function checkSession(Request $request)
    {
        try {
            $sessions_key = $request->sessions_key;
            $frontBaseUrl = $request->base_url;
            $frontToken = $request->token;
            $responseSession = Http::withHeaders([
                "Content-Type" => "application/json",
                "Accept" => "application/json"
            ])->get(config('app.eportalapi_url') . "/api/auth/session", [
                        'sessions_key' => $sessions_key
                    ]);
            $sessionArray = $responseSession->json();
            if (isset($sessionArray['data']['session'])) {
                //Get User Apps Session
                $currentUrl = request()->headers->get('origin');
                $currentHost = parse_url($currentUrl, PHP_URL_HOST);
                $currentScheme = parse_url($currentUrl, PHP_URL_SCHEME);
                if ($currentScheme == 'https') {
                    $schemeHost = 'https://' . $currentHost;
                } else {
                    $schemeHost = 'http://' . $currentHost;
                }

                $responseApp = Http::withHeaders([
                    "Content-Type" => "application/json",
                    "Accept" => "application/json",
                    "Authorization" => 'Bearer ' . $sessionArray['data']['session']['bearer_token']
                ])->get(config('app.eportalapi_url') . "/api/auth/appsession", [
                            'app_token' => $frontToken,
                            'app_host' => $currentUrl
                        ]);
                $appArray = $responseApp->json();
                if ($appArray['data'] == null) {
                    return response()->json([
                        'status' => true,
                        'app' => null,
                        'url' => config('app.eportalapi_url'),
                        'data' => $appArray,
                        'base_url' => $frontBaseUrl,
                        'currentUrl' => $currentUrl
                    ]);
                } else {
                    $responseApp = Http::withHeaders([
                        "Content-Type" => "application/json",
                        "Accept" => "application/json",
                        "Authorization" => 'Bearer ' . $sessionArray['data']['session']['bearer_token']
                    ])->get(config('app.eportalapi_url') . "/api/widget1");

                    $apps = collect($responseApp['data']['app']);

                    $map = $apps->map(function ($item) {
                        if ($item['app_name'] == 'E-PORTAL') {
                            $item['app_url'] = request()->headers->get('origin');
                        }
                        return $item;
                    })->all();
                    // $merge = $apps->merge([[
                    //     // 'app_description' => 'E-PORTAl',
                    //     // 'app_icon' => 'mdi-view-dashboard',
                    //     // 'app_name' => 'E-PORTAL',
                    //     'app_url' => 'https://eportal.imip.co.id',
                    //     "app_name" => "E-PORTAL",
                    //     "app_description" => "E-Portal IMIP Application",
                    //     "app_icon" => "mdi-apps",
                    //     // "app_url" => "http:\/\/**************"
                    // ]]);

                    return response()->json([
                        'currentUrl' => request()->headers->get('origin'),
                        'status' => true,
                        'app' => $appArray['data']['app'],
                        'list_app' => $map,
                        'map' => $map,
                        'apps' => $responseApp->json()
                    ]);
                }
            } else {
                return response()->json([
                    'status' => false,
                    'app' => null,
                    'url' => config('app.eportalapi_url'),
                    'response' => $sessionArray['data']
                ]);
            }
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), 401, [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param Request $request
     *
     * @return mixed
     */
    public function loginLocal(Request $request)
    {
        try {
            $attr = $request->validate([
                'username' => 'required|string',
                'password' => 'required|string'
            ]);

            $username = $request->username;
            $password = $request->password;
            $app_name = (isset($request->app_name)) ? $request->app_name : 'E-RESERVATION';

            $user = User::where('username', 'LIKE', '%' . $username . '%')->first();

            $apps = Application::where('app_name', $app_name)->first();
            if ($apps->app_name != 'E-FORM') {
                if (!$user) {
                    return $this->error('Username not registered in this Application!', 401);
                }

                if (
                    UserApp::where('user_id', $user->id)
                        ->where('app_id', $apps->id)
                        ->count() < 1
                ) {
                    return $this->error('Unauthorized to access this Application!', 401);
                }
            }

            // var_dump(openssl_get_cert_locations());

            // return $this->error('', 422, [$user]);
            if ($user->is_sap_user == 'Y') {
                $arr_user_sap_company = User::leftJoin('user_companies', 'user_companies.user_id', 'users.id')
                    ->leftJoin('companies', 'companies.id', 'user_companies.company_id')
                    ->where('users.id', $user->id)
                    ->distinct()
                    ->select('companies.db_code')->first();

                $params = [
                    "UserName" => $username,
                    "Password" => $request->password,
                    "CompanyDB" => $arr_user_sap_company->db_code,
                ];


                $curl = curl_init();
                curl_setopt($curl, CURLOPT_URL, config('app.service_layer') . "/b1s/v1/Login");
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($curl, CURLOPT_VERBOSE, 1);
                curl_setopt($curl, CURLOPT_POST, true);
                curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));

                $response = curl_exec($curl);
                // dd($response);

                $response_text = json_decode($response);

                if (property_exists($response_text, "error")) {
                    throw new \Exception(json_encode($response_text), 1);
                    // return $this->error($response_text, 422);
                } else {
                    $routeId = "";
                    curl_setopt($curl, CURLOPT_HEADERFUNCTION, function ($curl, $string) use (&$routeId) {
                        $len = strlen($string);
                        if (substr($string, 0, 10) == "Set-Cookie") {
                            preg_match("/ROUTEID=(.+);/", $string, $match);
                            if (count($match) == 2) {
                                $routeId = $match[1];
                            }
                        }
                        return $len;
                    });

                    curl_exec($curl);
                    // $array = [
                    //     'B1SESSION' => $response_text->SessionId,
                    //     'ROUTEID' => $routeId,,
                    // ];
                    // session($array);

                    $user->password = bcrypt($request->password);
                    $user->save();
                }
            } else {
                $response = '';

                if ($username != 'manager') {
                    $response = Http::withoutVerifying()->post(config('app.cherry_service_token'), [
                        'CommandName' => 'RequestToken',
                        'ModelCode' => 'AppUserAccount',
                        'UserName' => $username,
                        'Password' => $password,
                        'ParameterData' => [],
                    ]);

                    if (isset($response['Token'])) {
                        // Insert data user
                        $user = $this->insertUser($response, $password);
                    } else {
                        return $this->error($response['Message'], 401);
                    }
                }
            }

            $user = User::where('username', $username)->first();

            if (!Auth::attempt($attr)) {
                return $this->error('Credentials not match', 401);
            }

            if ($apps->app_name == 'E-METERAI') {
                $service = new DocumentService();
                // $service->peruriLogin();
                // session([
                //     'document_type' => $service->getDocumentType()
                // ]);
                // return $this->error('', 422, [session('document_type')]);
            }


            // $company = $this->assignUserCompany($user->id);

            $this->assignRolePermissionToUser($username, $apps->app_name, $response);

            // if ($app_name == 'E-RESERVATION') {
            //     $this->assignUserWareHouse($user->id, $company);

            //     $this->assignUserItemGroups($user->id, $company);
            // }

            return response()->json([
                'token' => $request->user()->createToken('api-token')->plainTextToken,
                'user' => auth()->user()
            ]);
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), 401, [
                'trace' => $exception->getTrace()
            ]);
        }
    }


    /**
     * @param Request $request
     *
     * @return mixed
     */
    public function login(Request $request)
    {
        try {
            if (auth()->check()) {
                return response()->json(['message' => 'already loggedin !']);
            }
            $attr = $request->validate([
                'username' => 'required|string',
                // 'password' => 'required|string'
            ]);

            $username = $request->username;
            $password = $request->password;
            $cherry_token = $request->cherry_token;
            $app_name = (isset($request->app_name)) ? $request->app_name : 'E-RESERVATION';

            // info('login info', [
            //     'username' => $username,
            //     'app_name' => $app_name,
            //     'cherry_token' => $cherry_token,
            // ]);

            $user = User::where('username', 'LIKE', '%' . $username . '%')->first();

            info("default DB connection " . DB::getDefaultConnection());

            $apps = Application::where('app_name', $app_name)->first();
            // if ($apps->app_name != 'E-FORM') {
            //     if (!$user) {
            //         return $this->error('Username not registered in this Application!', 401);
            //     }

            //     if (UserApp::where('user_id', $user->id)
            //             ->where('app_id', $apps->id)
            //             ->count() < 1) {
            //         return $this->error('Unauthorized to access this Application!', 401);
            //     }
            // }

            // var_dump(openssl_get_cert_locations());

            // return $this->error('', 422, [$user]);
            // if ($user->is_sap_user == 'Y') {
            //     $arr_user_sap_company = User::leftJoin('user_companies', 'user_companies.user_id', 'users.id')
            //         ->leftJoin('companies', 'companies.id', 'user_companies.company_id')
            //         ->where('users.id', $user->id)
            //         ->distinct()
            //         ->select('companies.db_code')->first();

            //     $params = [
            //         "UserName" => $username,
            //         "Password" => $request->password,
            //         "CompanyDB" => $arr_user_sap_company->db_code,
            //     ];


            //     $curl = curl_init();
            //     curl_setopt($curl, CURLOPT_URL, config('app.service_layer') . "/b1s/v1/Login");
            //     curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            //     curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            //     curl_setopt($curl, CURLOPT_VERBOSE, 1);
            //     curl_setopt($curl, CURLOPT_POST, true);
            //     curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));

            //     $response = curl_exec($curl);
            //     // dd($response);

            //     $response_text = json_decode($response);

            //     if (property_exists($response_text, "error")) {
            //         return $this->error($response_text, 422);
            //     } else {
            //         $routeId = "";
            //         curl_setopt($curl, CURLOPT_HEADERFUNCTION, function ($curl, $string) use (&$routeId) {
            //             $len = strlen($string);
            //             if (substr($string, 0, 10) == "Set-Cookie") {
            //                 preg_match("/ROUTEID=(.+);/", $string, $match);
            //                 if (count($match) == 2) {
            //                     $routeId = $match[1];
            //                 }
            //             }
            //             return $len;
            //         });

            //         curl_exec($curl);
            //         // $array = [
            //         //     'B1SESSION' => $response_text->SessionId,
            //         //     'ROUTEID' => $routeId,,
            //         // ];
            //         // session($array);

            //         $user->password = bcrypt($request->password);
            //         $user->save();
            //     }
            // } else {
            //     $response = '';

            //     if ($username != 'manager') {
            //         $response = Http::withoutVerifying()->post(config('app.cherry_service_token'), [
            //             'CommandName' => 'RequestToken',
            //             'ModelCode' => 'AppUserAccount',
            //             'UserName' => $username,
            //             'Password' => $password,
            //             'ParameterData' => [],
            //         ]);

            //         if (isset($response['Token'])) {
            //             // Insert data user
            //             $user = $this->insertUser($response, $password);
            //         }
            //         // else {
            //         //     return $this->error($response['Message'], 401);
            //         // }
            //     }
            // }

            $employee = ViewEmployee::where("Nik", $username)->first();

            $user = User::where('username', $username)->first();
            $user->cherry_token = $cherry_token;
            $user->email = (!empty($employee->OfficeEmailAddress)) ? $employee->OfficeEmailAddress : (
                (!empty($employee->PrivateEmailAddress)) ? $employee->PrivateEmailAddress : $user->email
            );
            $user->save();
            // $this->insertUser($response, $password);

            // if (!Auth::attempt($attr)) {
            //     return $this->error('Credentials not match', 401);
            // }

            /* Script before 2 */
            Auth::login($user);

            if ($apps->app_name == 'E-METERAI') {
                $service = new DocumentService();
                // $service->peruriLogin();
                // session([
                //     'document_type' => $service->getDocumentType()
                // ]);
                // return $this->error('', 422, [session('document_type')]);
            }


            // $company = $this->assignUserCompany($user->id);

            // $this->assignRolePermissionToUser($username, $apps->app_name, $response);

            // if ($app_name == 'E-RESERVATION') {
            //     $this->assignUserWareHouse($user->id, $company);

            //     $this->assignUserItemGroups($user->id, $company);
            // }

            return response()->json([
                'token' => $request->user()->createToken('api-token')->plainTextToken,
                'user' => auth()->user()
            ]);
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), 401, [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    public function checkUser($username)
    {
        $user = User::where('username', $username)->firstOrFail();
        return $this->success([
            'data' => $user
        ]);
    }

    /**
     * @param $response
     * @param $password
     *
     * @return mixed
     */
    protected function insertUser($response, $password)
    {
        $data = [
            'name' => $response['Data']['Name'],
            'cherry_token' => $response['Token'],
            'username' => $response['UserName'],
            'password' => bcrypt($password),
            'email' => !empty($response['Data']['Email']) ? $response['Data']['Email']
                : strtotime(date('Y-m-d H:i:s')) . '@imip.co.id',
            'department' => $response['Data']['Organization'],
            'company' => $response['Data']['Company'],
            'position' => $response['Data']['Position'],
            'location' => $response['Data']['Location'],
            'company_code' => $response['Data']['CompanyCode'],
            'employee_code' => $response['Data']['EmployeeCode'],
        ];

        if (User::where('username', '=', $response['UserName'])->first()) {
            $user = User::where('username', '=', $response['UserName'])
                ->update($data);
        } else {
            $user = User::create($data);
        }
        return $user;
    }


    /**
     * @param $username
     *
     * @return mixed
     */
    protected function assignUserCompany($username)
    {
        if (env('APP_ENV') == 'local') {
            $company = Company::where('db_code', '=', 'IMIP_TEST')->first();
        } else {
            $company = Company::where('db_code', '=', 'IMIP_LIVE')->first();
        }
        if (
            UserCompany::where('user_id', '=', $username)
                ->where('company_id', '=', $company->id)
                ->count() < 1
        ) {
            UserCompany::create([
                'user_id' => $username,
                'company_id' => $company->id
            ]);
        }

        return $company->db_code;
    }

    /**
     * @param $username
     * @param $app_name
     * @param $response
     */
    protected function assignRolePermissionToUser($username, $app_name, $response)
    {
        if ($username == 'manager') {
            $role = Role::where('name', 'Superuser')->first();
            $permissions = Permission::all();
            $user = User::where('username', $username)->first();
            $user->assignRole($role);
            foreach ($permissions as $permission) {
                $user->givePermissionTo($permission->name);
            }
        }

        if ($app_name == 'E-FORM') {
            $user = User::where('username', $username)->first();
            $check_role = DB::table('model_has_roles')
                ->where('model_id', '=', $user->id)
                ->where('model_type', '=', 'App\Models\User')
                ->count();
            if ($check_role < 1) {
                $role = Role::where('name', 'Personal')->first();
                $user->assignRole($role);

                $data = $response['Data']['Organization'];
                if (UserDivision::where('user_id', $user->id)->count() > 0) {
                    UserDivision::where('user_id', $user->id)->delete();
                }

                if ($data) {
                    UserDivision::updateOrCreate([
                        'user_id' => $user->id,
                        'division_name' => $response['Data']['Organization']
                    ]);
                }

                $apps = $app_name;
                if (UserApp::where('user_id', $user->id)->count() > 0) {
                    UserApp::where('user_id', $user->id)->delete();
                }

                if ($apps) {
                    $id = Application::where('app_name', '=', $app_name)->first();
                    UserApp::updateOrCreate([
                        'user_id' => $user->id,
                        'app_id' => $id->id
                    ]);
                }
            }
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        if (!$request->user()->hasAnyRole(['Admin E-Meterai', 'Admin Helpdesk'])) {
            $request->user()->tokens()->delete();
            auth()->user()->tokens()->delete();
        }


        return $this->success([
            'message' => 'Tokens Revoked'
        ]);
    }

    /**
     * JWT refresh token
     *
     * @return mixed
     */
    public function refresh()
    {
        return $this->respondWithToken(auth()->refresh());
    }

    /**
     * Get the token array structure.
     *
     * @param string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithToken($token)
    {
        return response()->json([
            'token' => $token,
            'user' => auth()->user(),
            'token_type' => 'Bearer',
            'expires_in' => auth()->factory()->getTTL() * 60
        ]);
    }

    /**
     * Laravel Sanctum refresh token
     *
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refreshToken(Request $request)
    {
        $token = $request->token;
        $personal_token = PersonalAccessToken::where('token', $token)->first();

        return response()->json(['token' => $request->user()->createToken('refresh-token')->plainTextToken], 200);
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function user(Request $request)
    {
        // throw new \Exception($request->user()->id, 1);
        $user = User::where('id', '=', $request->user()->id)->with(['roles', 'permissions'])->first();
        return response()->json([
            'user' => $user
        ]);
    }

    /**
     * Display a listing of permissions from current logged user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function permissions(Request $request): \Illuminate\Http\JsonResponse
    {
        return response()->json($request->user()->getAllPermissions()->pluck('name'));
    }

    /**
     * Display a listing of roles from current logged user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function roles(): \Illuminate\Http\JsonResponse
    {
        return response()->json(auth()->user()->getRoleNames());
    }

    /**
     * @param $username
     * @param $company
     */
    protected function assignUserWareHouse($username, $company)
    {
        if (UserWhs::where('user_id', '=', $username)->count() < 1) {
            $list_whs = [
                [
                    'whs_code' => 'MW-GE',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'whs_code' => 'MW-GA',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'whs_code' => 'IG03',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'whs_code' => 'MW-HSE',
                    'db_code' => $company,
                    'user_id' => $username
                ],
            ];

            UserWhs::insert($list_whs);
        }
    }

    /**
     * @param $username
     * @param $company
     */
    protected function assignUserItemGroups($username, $company)
    {
        if (UserItmGrp::where('user_id', '=', $username)->count() < 1) {
            $data = [
                [
                    'item_group' => '100',
                    'item_group_name' => '100',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'item_group' => '102',
                    'item_group_name' => '102',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'item_group' => '107',
                    'item_group_name' => '107',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'item_group' => '111',
                    'item_group_name' => '111',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'item_group' => '112',
                    'item_group_name' => '112',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'item_group' => '147',
                    'item_group_name' => '147',
                    'db_code' => $company,
                    'user_id' => $username
                ],
                [
                    'item_group' => 'ZITS',
                    'item_group_name' => 'ZITS',
                    'db_code' => $company,
                    'user_id' => $username
                ],
            ];
            UserItmGrp::insert($data);
        }
    }
}
