<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Common\Application;
use App\Models\Common\Company;
use App\Models\Enviro\EnviroSubRole;
use App\Models\Enviro\EnviroUserSubRole;
use App\Models\Paper\Role;
use App\Models\User;
use App\Models\User\UserApp;
use App\Models\User\UserCompany;
use App\Models\User\UserDivision;
use App\Models\User\UserDocType;
use App\Models\User\UserWorkLocation;
use App\Models\View\ViewCompanyOrganization;
use App\Models\View\ViewEmployee;
use App\Traits\ConnectHana;
use App\Traits\MasterData;
use App\Traits\RolePermission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Collection;

class MasterUserController extends Controller
{
    use ConnectHana;
    use MasterData, RolePermission;

    /**
     * MasterUserController constructor.
     */
    public function __construct()
    {
        $this->middleware(['direct_permission:Users-index'])->only(['index', 'show']);
        $this->middleware(['direct_permission:Users-store'])->only('store');
        $this->middleware(['direct_permission:Users-edits'])->only('update');
        $this->middleware(['direct_permission:Users-erase'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $options = json_decode($request->options);
        $year_local = date('Y');
        $pages = isset($request->page) ? (int) $request->page : 1;
        $filter = isset($request->filter) ? (string) $request->filter : $year_local;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 5;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "users.name";
        $order = isset($request->sortBy[0]) ? $request->sortBy[0]['value'] : "ASC";
        $search = isset($request->search) ? (string) $request->search : "";
        $select_data = isset($request->searchItem) ? (string) $request->searchItem : "name";
        $select_role = isset($request->searchRole) ? (string) $request->searchRole : null;
        $select_company = isset($request->searchCompany) ? (string) $request->searchCompany : null;

        $offset = $pages;

        $result = array();
        $query = User::select('users.*')
            // ->leftJoin('model_has_roles', 'model_has_roles.model_id', 'users.id')
            // ->leftJoin('roles', 'roles.id', 'model_has_roles.role_id')
            // ->where('roles.name', '<>', 'Personal')
            // ->distinct()
            ->orderBy($sorts, $order);
        // return response()->json($select_role, 422);
        $all_data = $query->when(
            $select_data,
            function ($query) use ($select_data, $search, $select_role, $select_company) {
                $data_query = $query;
                switch ($select_data) {
                    case 'Username':
                        $data_query->where('username', 'LIKE', '%' . $search . '%');
                        break;
                    case 'Name':
                        $data_query->where('name', 'LIKE', '%' . $search . '%');
                        break;
                    case 'Department':
                        $data_query->where('department', 'LIKE', '%' . $search . '%');
                        break;
                }

                if (isset($select_role)) {
                    $data_query->role($select_role);
                }

                if (isset($select_company)) {
                    $data_query->where('company', 'LIKE', '%' . $select_company . '%');
                }

                return $data_query;
            }
        );

        $result["total"] = $query->count();

        $all_data = $query->paginate($row_data)
            ->items();

        $array_user = [];
        foreach ($all_data as $item) {
            $user_roles = User::leftJoin('model_has_roles', 'model_has_roles.model_id', 'users.id')
                ->leftJoin('roles', 'roles.id', 'model_has_roles.role_id')
                ->where('users.id', $item->id)
                ->select('model_has_roles.role_id', 'roles.name')
                ->get();
            $arr_role_name = [];
            $arr_user_role = [];
            foreach ($user_roles as $user_role) {
                $arr_user_role[] = (int) $user_role->role_id;
                $arr_role_name[] = $user_role->name;
            }

            $app_access = User::leftJoin('user_apps', 'user_apps.user_id', 'users.id')
                ->leftJoin('applications', 'user_apps.app_id', 'applications.id')
                ->where('users.id', $item->id)
                ->select('user_apps.app_id')
                ->get();

            $arr_user_app = [];
            foreach ($app_access as $user_app) {
                $arr_user_app[] = (int) $user_app->app_id;
            }

            $arr_user_division = User::leftJoin('user_divisions', 'user_divisions.user_id', 'users.id')
                ->where('users.id', $item->id)
                ->select('user_divisions.division_name')
                ->pluck('user_divisions.division_name');

            $arr_user_whs = User::leftJoin('user_whs', 'user_whs.user_id', 'users.id')
                ->where('users.id', $item->id)
                ->select('user_whs.whs_code')
                ->pluck('user_whs.whs_code');

            $arr_user_sap_company = User::leftJoin('user_companies', 'user_companies.user_id', 'users.id')
                ->leftJoin('companies', 'companies.id', 'user_companies.company_id')
                ->where('users.id', $item->id)
                ->distinct()
                ->select('companies.db_code')
                ->pluck('companies.db_code');

            $arr_item_group = User::leftJoin('user_itm_grps', 'user_itm_grps.user_id', 'users.id')
                ->distinct()
                ->where('users.id', $item->id)
                ->select('user_itm_grps.item_group')
                ->pluck('user_itm_grps.item_group');

            $item_sub_role = EnviroUserSubRole::where('user_id', $item->id)
                ->distinct()
                ->leftJoin('enviro_sub_roles', 'enviro_user_sub_roles.enviro_sub_role_id', 'enviro_sub_roles.id')
                ->pluck('enviro_sub_roles.name');

            $item_work_location = UserWorkLocation::where('user_id', $item->id)
                ->distinct()
                ->pluck('work_location');

            $item_document_type = UserDocType::where('user_id', $item->id)
                ->distinct()
                ->pluck('document_type');

            $array_user[] = [
                'Action' => $item->Action,
                'active' => $item->active,
                'specimen_sign' => $item->specimen_sign,
                'company' => $item->company,
                'company_code' => $item->company_code,
                'department' => $item->department,
                'email' => $item->email,
                'id' => $item->id,
                'is_admin_subwh' => $item->is_admin_subwh,
                'is_superuser' => $item->is_superuser,
                'is_sap_user' => $item->is_sap_user,
                'is_sales_user' => $item->is_sales_user,
                'location' => $item->location,
                'name' => $item->name,
                'position' => $item->position,
                'role_name' => implode(', ', $arr_role_name),
                'username' => $item->username,
                'role' => $arr_user_role,
                'apps' => $arr_user_app,
                'division' => $arr_user_division,
                'item_group' => $arr_item_group,
                'whs' => $arr_user_whs,
                'work_location' => $item_work_location,
                'document_type' => $item_document_type,
                'sub_role' => $item_sub_role,
                'user_sap_company' => $arr_user_sap_company
            ];
        }

        // return response()->json($request->user()->company);
        //return response()->json($array_user);




        $work_location = ViewEmployee::distinct()->pluck('WorkLocation');

        $sub_role = EnviroSubRole::pluck('name');

        $result = array_merge($result, [
            "rows" => $array_user,
            "filter" => ['Username', 'Name', 'Department'],
            'sub_role' => $sub_role,
            'work_location' => $work_location
        ]);
        return response()->json($result);
    }

    public function getUserDivision(Request $request)
    {
        $item_work_location = UserWorkLocation::where('user_id', $request->user()->id)
            ->pluck('work_location');

        // Explode each work_location value and take the first part
        $exploded_locations = collect($item_work_location)->map(function ($location) {
            $parts = explode(' ', $location);
            return 'PT ' . $parts[0]; // Get the first part
        })->toArray();

        $divisions = ViewCompanyOrganization::select('Department')
            ->whereIn('Company', $exploded_locations)
            ->orderBy('Department')
            ->distinct()
            ->get();

        // $item_work_location = UserWorkLocation::where('user_id', $request->user()->id)->pluck('work_location');
        // $divisions = ViewEmployee::select('Department')
        //     ->whereIn('WorkLocation', $item_work_location)
        //     ->orderBy('Department')
        //     ->distinct()
        //     ->get();
        $arr_division = [];
        foreach ($divisions as $division) {
            $arr_division[] = [
                'name' => $division->Department
            ];
        }
        return response()->json([
            'division' => $arr_division,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        $form = $request->form;
        //        $roles = $request->form['role'];
        //        foreach ($roles as $role) {
        //            return response()->json($role['id']);
        //        }
        //return response()->json($form);

        DB::beginTransaction();
        try {
            $data = [
                'username' => $form['username'],
                'name' => $form['username'],
                'is_admin_subwh' => $form['is_admin_subwh'],
                'is_superuser' => $form['is_superuser'],
                'email' => strtotime(date('Y-m-d H:i:s')) . '@imip.co.id',
                'active' => $form['active'],
                'is_sap_user' => (isset($form['is_sap_user'])) ? $form['is_sap_user'] : 'N',
                'is_sales_user' => (isset($form['is_sales_user'])) ? $form['is_sales_user'] : 'N',
            ];

            $user = User::create($data);

            $this->storeUserDetails($request, $user);

            DB::commit();

            return $this->success([
                "errors" => false
            ], 'Data inserted!');
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $messages = [
            'form.username' => 'Username Field is required!',
            'form.apps' => 'Apps Access Field is required!',
            'form.role' => 'Role Field is required!',
            'form.active' => 'Status is required!',
            'form.is_sap_user' => 'Check SAP User is required!',
        ];
        if (array_key_exists('id', $request->form)) {
            $user_id = $request->form['id'];
            $validator = Validator::make($request->all(), [
                'form.username' => 'required|unique:users,username,' . $user_id,
                'form.apps' => 'required',
                'form.role' => 'required',
                'form.active' => 'required',
                'form.is_sap_user' => 'required',
            ], $messages);
        } else {
            $validator = Validator::make($request->all(), [
                'form.username' => 'required',
                'form.apps' => 'required',
                'form.role' => 'required',
                'form.active' => 'required',
                'form.is_sap_user' => 'required',
            ], $messages);
        }


        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): \Illuminate\Http\JsonResponse
    {
        if (intval($id)) {
            $user = User::where("user_id", "=", $id)->first();
            return response()->json([
                "sub_id" => [
                    "U_UserName" => $user['U_UserName'],
                    "user_id" => $user['user_id'],
                ]
            ]);
        } else {
            return response()->json([
                "sub_id" => [
                    "U_UserName" => null,
                    "user_id" => null,
                ]
            ]);
        }
    }

    public function showUser($company)
    {
        $user = User::select('id', 'name')
            ->get();
        return $this->success($user);
    }

    /**
     * @param $request
     * @param $user
     */
    protected function storeUserDetails($request, $user)
    {
        $this->storeUserRole($request, $user);

        $this->storeUserApps($request, $user);
        $this->storeUseDivision($request, $user);

        $this->storeUseWhs($request, $user);

        $this->storeUserItemGroups($request, $user);

        $this->storeUserWorkLocation($request, $user);

        $this->storeUserDocumentType($request, $user);

        // $this->storeUserEnviroSubRole($request, $user);

        if (!empty($request->form['user_sap_company'])) {
            UserCompany::where('user_id', '=', $user->id)->delete();
            foreach ($request->form['user_sap_company'] as $key => $value) {
                $company = Company::where('db_code', '=', $value)->first();
                if ($company) {
                    if (
                        UserCompany::where('user_id', '=', $user->id)
                            ->where('company_id', '=', $company->id)
                            ->count() < 1
                    ) {
                        UserCompany::create([
                            'user_id' => $user->id,
                            'company_id' => $company->id
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id): \Illuminate\Http\JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        $form = $request->form;

        try {
            $data = [
                'username' => $form['username'],
                // 'name' => (empty($form['name'])) ? $form['username'] : $form['name'],
                'is_admin_subwh' => $form['is_admin_subwh'],
                'is_superuser' => $form['is_superuser'],
                'is_sap_user' => $form['is_sap_user'],
                'is_sales_user' => (isset($form['is_sales_user'])) ? $form['is_sales_user'] : 'N',
                'active' => $form['active'],
            ];

            User::where("id", "=", $id)->update($data);

            $user = User::find($id);

            $this->storeUserDetails($request, $user);

            return $this->success([
                "errors" => false
            ], 'Data updated (' . $form['name'] . ' - ' . $form['username'] . ')');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $details = User::where("id", "=", $id)->first();
        if ($details) {
            User::where("id", "=", $id)->delete();
            return $this->success([
                "errors" => false
            ], 'Row deleted!');
        }

        return $this->error('Row not found', 422, [
            "errors" => true
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     *
     */
    public function syncData(Request $request)
    {
        // DB::beginTransaction();
        try {
            $company = $request->company;
            $check_user = User::where('company', '=', $company)
                ->where('username', '<>', 'manager')
                ->whereNotNull('name')
                ->whereNotIn('department', ['SECURITY', 'MSS'])
                ->whereHas('roles', function ($query) {
                    $query->whereIn('name', ['Admin E-RESERVATION', 'Personal']);
                })
                ->pluck('username');

            Log::info('check_user ' . count($check_user), [
                'company' => $company,
                'check_user' => $check_user,
            ]);

            if (count($check_user) > 0) {
                $collection = collect($check_user);

                $chunks = $collection->chunk(1000);


                foreach ($chunks->all() as $item) {
                    $employees = ViewEmployee::whereIn('Company', [$company])
                        ->whereNotIn('Nik', $item)
                        ->get();
                    // Log::info('employees ', [
                    //     $employees
                    // ]);
                    // return response()->json($check_user, 422);

                    foreach ($employees as $employee) {
                        $password = '1234';
                        $email = (!empty($employee->OfficeEmailAddress)) ? $employee->OfficeEmailAddress : Str::random(20) . '@imip.co.id';
                        if (User::where('email', '=', $employee->OfficeEmailAddress)->count() > 0) {
                            $email = Str::random(20) . '@imip.co.id';
                        }
                        $data = [
                            'name' => $employee->Name,
                            'username' => $employee->Nik,
                            'password' => bcrypt($password),
                            'email' => $email,
                            'department' => $employee->Department,
                            'company' => $employee->Company,
                            'position' => $employee->JobPosition,
                            'location' => $employee->WorkLocation,
                            'employee_code' => $employee->EmployeeCode,
                            'company_code' => $employee->CompanyCode,
                            'active' => ($employee->IsActive == 'False') ? 'N' : 'Y'
                        ];

                        if (User::where('username', '=', $employee->Nik)->first()) {
                            $user = User::where('username', '=', $employee->Nik)
                                ->update($data);
                        } else {
                            $user = User::create($data);
                        }

                        $user = User::where('username', $employee->Nik)->first();

                        UserWorkLocation::updateOrCreate([
                            'user_id' => $user->id,
                            'work_location' => $employee->WorkLocation,
                            'created_by' => auth()->user()->id
                        ]);

                        $check_role = DB::table('model_has_roles')
                            ->where('model_id', '=', $user->id)
                            ->where('model_type', '=', 'App\Models\User')
                            ->count();

                        if ($check_role < 1) {
                            $role = Role::where('name', 'Personal')->first();
                            $user->assignRole($role);

                            $data = $employee->Department;
                            if (UserDivision::where('user_id', $user->id)->count() > 0) {
                                UserDivision::where('user_id', $user->id)->delete();
                            }

                            if ($data) {
                                UserDivision::updateOrCreate([
                                    'user_id' => $user->id,
                                    'division_name' => $employee->Department
                                ]);
                            }

                            if (UserApp::where('user_id', $user->id)->count() > 0) {
                                UserApp::where('user_id', $user->id)->delete();
                            }

                            $id = Application::where('app_name', '=', 'E-FORM')->first();
                            UserApp::updateOrCreate([
                                'user_id' => $user->id,
                                'app_id' => $id->id
                            ]);
                        }
                    }
                }
            } else {
                $employees = ViewEmployee::whereIn('Company', [$company])
                    ->select(
                        "Company",
                        "CompanyCode",
                        "WorkLocation",
                        "Department",
                        "OldNik",
                        "Nik",
                        "EmployeeCode",
                        "Name",
                        "JoinDate",
                        "JobTitle",
                        "JobPosition",
                        "EmploymentStatus",
                        "Gender",
                        "BirthDate",
                        "BirthPlace",
                        "IdType",
                        "IdNumber",
                        "AlamatKTP",
                        "AlamatDomisili",
                        "MobilePhone",
                        "OfficeEmailAddress",
                        "PrivateEmailAddress",
                        "DeviceToken",
                        "LivesInDormitory",
                        "DormitoryNr",
                        "DirectSuperiorNIK",
                        "DirectSuperiorName",
                        "DirectSuperiorPosition",
                        "IsActive",
                        "EffectiveDate",
                        "WorkShift",
                        "WorkShiftToday",
                        "CalendarName",
                        "FieldBreakSchemaName",
                        "NickName",
                        "Religion",
                        "OrganizationRole",
                    )
                    ->get();

                Log::info('check_user ' . count($employees));
                // return response()->json($check_user, 422);

                foreach ($employees as $employee) {
                    $password = '1234';
                    $email = (!empty($employee->OfficeEmailAddress)) ? $employee->OfficeEmailAddress : Str::random(20) . '@imip.co.id';
                    if (User::where('email', '=', $employee->OfficeEmailAddress)->count() > 0) {
                        $email = Str::random(20) . '@imip.co.id';
                    }
                    $data = [
                        'name' => $employee->Name,
                        'username' => $employee->Nik,
                        'password' => bcrypt($password),
                        'email' => $email,
                        'department' => $employee->Department,
                        'company' => $employee->Company,
                        'position' => $employee->JobPosition,
                        'location' => $employee->WorkLocation,
                        'employee_code' => $employee->EmployeeCode,
                        'company_code' => $employee->CompanyCode,
                        'active' => ($employee->IsActive == 'False') ? 'N' : 'Y'
                    ];

                    if (User::where('username', '=', $employee->Nik)->first()) {
                        $user = User::where('username', '=', $employee->Nik)
                            ->update($data);
                    } else {
                        $user = User::create($data);
                    }

                    $user = User::where('username', $employee->Nik)->first();

                    UserWorkLocation::updateOrCreate([
                        'user_id' => $user->id,
                        'work_location' => $employee->WorkLocation,
                        'created_by' => auth()->user()->id
                    ]);

                    $check_role = DB::table('model_has_roles')
                        ->where('model_id', '=', $user->id)
                        ->where('model_type', '=', 'App\Models\User')
                        ->count();

                    if ($check_role < 1) {
                        $role = Role::where('name', 'Personal')->first();
                        $user->assignRole($role);

                        $data = $employee->Department;
                        if (UserDivision::where('user_id', $user->id)->count() > 0) {
                            UserDivision::where('user_id', $user->id)->delete();
                        }

                        if ($data) {
                            UserDivision::updateOrCreate([
                                'user_id' => $user->id,
                                'division_name' => $employee->Department
                            ]);
                        }

                        if (UserApp::where('user_id', $user->id)->count() > 0) {
                            UserApp::where('user_id', $user->id)->delete();
                        }

                        $id = Application::where('app_name', '=', 'E-FORM')->first();
                        UserApp::updateOrCreate([
                            'user_id' => $user->id,
                            'app_id' => $id->id
                        ]);
                    }
                }
            }


            // DB::commit();
            return $this->success([], 'Data Sync!');
        } catch (\Exception $exception) {
            // DB::rollBack();
            return $this->error($exception->getMessage(), 422, [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    public function syncDataUser(Request $request)
    {
        // DB::beginTransaction();
        $company = $request->company;
        $department = ViewEmployee::where('Company', $company)
            ->select("Department")
            ->distinct()
            ->get()
            ->pluck('Department');
        try {
            $employees = User::where('company', '=', $company)
                ->whereHas('roles', function ($query) {
                    $query->where('name', 'Admin E-RESERVATION');
                })
                ->where('username', '<>', 'manager')
                ->whereNotNull('name')
                ->get();

            // throw new \Exception(count($employees));
            // return response()->json($check_user, 422);


            foreach ($employees as $employee) {
                UserWorkLocation::updateOrCreate([
                    'user_id' => $employee->id,
                    'work_location' => $employee->location,
                    'created_by' => auth()->user()->id
                ]);

                $userDivisions = UserDivision::where('user_id', $employee->id)->get();

                foreach ($userDivisions as $key => $userDivision) {
                    // throw new \Exception((Str::of($userDivision->division_name)->contains($department)));
                    if (!Str::contains($userDivision->division_name, $department)) {
                        UserDivision::where('user_id', $employee->id)
                            ->where('division_name', $userDivision->division_name)
                            ->delete();
                    }
                }

                // $userDivisions = UserDivision::where('user_id', $employee->id)->get();

                if (!$userDivisions) {
                    UserDivision::updateOrCreate([
                        'user_id' => $employee->id,
                        'division_name' => $employee->department
                    ]);
                } else {
                    UserDivision::updateOrCreate([
                        'user_id' => $employee->id,
                        'division_name' => $employee->department
                    ]);
                }

                $view = ViewEmployee::where('Nik', $employee->username)->first();
                if ($view) {
                    User::where('id', $employee->id)
                        ->update([
                            'active' => ($view->IsActive == 'False') ? 'N' : 'Y'
                        ]);
                }
            }
            // DB::commit();
            return $this->success([], 'Data Sync!');
        } catch (\Exception $exception) {
            // DB::rollBack();
            return $this->error($exception->getMessage(), 422, [
                'department' => $department,
                'trace' => $exception->getTrace(),
            ]);
        }
    }

    public function generateToken(Request $request)
    {
        $user = User::find($request->id);

        $token = $user->createToken('authToken')->plainTextToken;

        return $this->success([], $token);
    }
}
