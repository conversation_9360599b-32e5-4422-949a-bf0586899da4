GO
/****** Object:  StoredProcedure [dbo].[sp_role_permissions]    Script Date: 7/31/2021 9:38:51 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE
    [dbo].[sp_user_permissions](@User INTEGER)
AS
BEGIN
    -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
    SET
        NOCOUNT ON;

    WITH Summary AS (
        select a.menu_name               as permission,
               ISNULL(idx.valuex, 'N')   as 'index',
               ISNULL(store.valuex, 'N') as store,
               ISNULL(edits.valuex, 'N') as edits,
               ISNULL(erase.valuex, 'N') as erase,
               a.order_line
        from permissions as a
                 left join model_has_permissions as b on a.id = b.permission_id
                 left join (
            select menu_name, 'Y' as valuex
            from permissions as a1
                     left join model_has_permissions as b on a1.id = b.permission_id
            where b.model_id = @User
              and RIGHT(a1.name
                      , 5) = 'index'
            group by menu_name
        ) as idx on a.menu_name = idx.menu_name

                 left join (
            select menu_name, 'Y' as valuex
            from permissions as a1
                     left join model_has_permissions as b on a1.id = b.permission_id
            where b.model_id = @User
              and RIGHT(a1.name
                      , 5) = 'store'
            group by menu_name
        ) as store on a.menu_name = store.menu_name

                 left join (
            select menu_name, 'Y' as valuex
            from permissions as a1
                     left join model_has_permissions as b on a1.id = b.permission_id
            where b.model_id = @User
              and RIGHT(a1.name
                      , 5) = 'edits'
            group by menu_name
        ) as edits on a.menu_name = edits.menu_name

                 left join (
            select menu_name, 'Y' as valuex
            from permissions as a1
                     left join model_has_permissions as b on a1.id = b.permission_id
            where b.model_id = @User
              and RIGHT(a1.name
                      , 5) = 'erase'
            group by menu_name
        ) as erase on a.menu_name = erase.menu_name

        where b.model_id = @User
        group by a.menu_name, idx.valuex, store.valuex,
                 edits.valuex, erase.valuex, a.order_line
    )

    SELECT *
    FROM Summary
    order by order_line
END


select IMIP_ERESV_TEST."RESV_H".*,
       (SELECT STRING_AGG(X."PR_NO", ', ')
        FROM (SELECT DISTINCT Q0."DocNum" AS "PR_NO"
              FROM IMIP_TEST_1217."OPRQ" Q0
                       LEFT JOIN IMIP_TEST_1217."PRQ1" Q1 ON Q0."DocEntry" = Q1."DocEntry"
              WHERE Q1."U_DGN_IReqId" = RESV_H."SAP_GIRNo"
                AND Q0."CANCELED" = 'N') AS X)                                AS "SAP_PRNo",
       (SELECT "U_DocNum"
        FROM IMIP_TEST_1217."@DGN_EI_OIGR"
        where IMIP_TEST_1217."@DGN_EI_OIGR"."DocNum" = RESV_H."SAP_GIRNo")    AS "SAP_GIRNo",
       (SELECT STRING_AGG(X."DocNum", ', ') as "GI_No"
        FROM (SELECT DISTINCT T0."DocNum"
              FROM IMIP_TEST_1217."@DGN_EI_OIGR" G0
                       LEFT JOIN IMIP_TEST_1217."@DGN_EI_IGR1" G1 ON G0."DocEntry" = G1."DocEntry"
                       LEFT JOIN IMIP_TEST_1217.IGE1 T1
                                 ON T1."U_DGN_IReqId" = G1."DocEntry" AND T1."U_DGN_IReqLineId" = G1."LineId"
                       LEFT JOIN IMIP_TEST_1217.OIGE T0 ON T1."DocEntry" = T0."DocEntry"
              WHERE G0."DocEntry" = RESV_H."SAP_GIRNo") AS X)                 AS "SAP_GINo",
       IMIP_ERESV_TEST."RESV_H"."Company"                                     as "U_DbCode",
       (SELECT STRING_AGG(X."PONum", ', ') AS "PONum"
        FROM (SELECT DISTINCT T1."DocNum" AS "PONum"
              FROM IMIP_TEST_1217."POR1" AS T0
                       LEFT JOIN IMIP_TEST_1217."OPOR" AS T1 ON T0."DocEntry" = T1."DocEntry"
              WHERE T0."U_DGN_IReqId" = RESV_H."SAP_GIRNo"
                AND T1."CANCELED" = 'N') AS X)                                AS "PONum",
       (SELECT STRING_AGG(X."GRPO_NO", ', ') AS "GRPO_NO"
        FROM (SELECT DISTINCT T1."DocNum" AS "GRPO_NO"
              FROM IMIP_TEST_1217."PDN1" AS T0
                       LEFT JOIN IMIP_TEST_1217."OPDN" AS T1 ON T0."DocEntry" = T1."DocEntry"
              WHERE T0."U_DGN_IReqId" = RESV_H."SAP_GIRNo"
                AND T1."CANCELED" = 'N') AS X)                                AS "GRPONum",
       (SELECT STRING_AGG(X."TrfNo", ', ') AS "SAP_TrfNo"
        FROM (SELECT DISTINCT T2."DocNum" AS "TrfNo"
              FROM IMIP_ERESV_TEST."RESV_H" T0
                       LEFT JOIN IMIP_TEST_1217."@DGN_EI_IGR1" G1 ON T0."SAP_GIRNo" = G1."DocEntry"
                       LEFT JOIN IMIP_TEST_1217."@DGN_EI_OIGR" G0 ON G0."DocEntry" = G1."DocEntry"
                       LEFT JOIN IMIP_TEST_1217."WTR1" T1
                                 ON G1."DocEntry" = T1."U_DGN_IReqId" AND T1."U_DGN_IReqLineId" = G1."LineId"
                       LEFT JOIN IMIP_TEST_1217."OWTR" T2 ON T1."DocEntry" = T2."DocEntry"
              WHERE T0."U_DocEntry" = IMIP_ERESV_TEST.RESV_H."U_DocEntry") X) AS "SAP_TrfNo",
       RESV_H."RequesterName"                                                 AS "RequestName",
       CASE
           WHEN RESV_H."DocStatus" = 'D' THEN 'Draft'
           ELSE (SELECT CASE
                            when G0."Status" = 'O' THEN 'Open'
                            when G0."Status" = 'C' AND G0."Canceled" = 'Y' THEN 'Cancel'
                            ELSE 'Closed' END AS "GIR_status"
                 FROM IMIP_TEST_1217."@DGN_EI_OIGR" G0
                 WHERE G0."DocEntry" = RESV_H."SAP_GIRNo") END                AS "DocumentStatus",
       CASE
           WHEN RESV_H."ApprovalStatus" = 'W' THEN 'Waiting'
           WHEN RESV_H."ApprovalStatus" = 'P' THEN 'Pending'
           WHEN RESV_H."ApprovalStatus" = 'N' THEN 'Rejected'
           WHEN RESV_H."ApprovalStatus" = 'Y' THEN 'Approved'
           WHEN RESV_H."ApprovalStatus" = '-' THEN '-' END                    AS "AppStatus",
       'action'                                                               AS "Action"
from IMIP_ERESV_TEST."RESV_H"
where "RESV_H"."ApprovalStatus" LIKE '%%'
order by IMIP_ERESV_TEST."RESV_H"."DocNum" desc limit 15
offset 0
