<?php

namespace App\Models\Task;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Task\TaskSection
 *
 * @property int $id
 * @property string $title
 * @property string|null $slug
 * @property int $order_line
 * @property int $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $department
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection whereOrderLine($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSection whereUserId($value)
 * @mixin \Eloquent
 */
class TaskSection extends Model
{
    use HasFactory;

    protected $table = 'task_section';

    protected $guarded = [];
}
