<?php

namespace App\Http\Controllers\Tasks;

use App\Http\Controllers\Controller;
use App\Models\Task\Task;
use App\Models\Task\TaskTagPeople;
use App\Models\User;
use App\Services\Taskservice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class TaskController extends Controller
{
    public $service;

    public function __construct(Taskservice $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $result['form'] = $this->taskForm();
        $query = DB::table('tasks as B')
            ->select('a.*');

        $userDivision = DB::table('user_divisions')
            ->where('user_id', '=', $request->user()->id)
            ->pluck('division_name');

        $tasks = clone $query;
        $assignees = clone $query;
        $team = clone $query;

        $tasks = $tasks->where('B.user_id', '=', $request->user()->id)
            ->count();

        $team = $team->whereIn('B.department', $userDivision)->count();

        $assignees = $assignees->leftJoin('task_tag_people as C', 'C.task_id', 'B.id')
            ->where('C.user_id', '=', $request->user()->id)
            ->count();

        $result['countAssignTicket'] = $assignees;
        $result['countMyTicket'] = $tasks;
        $result['countTeamTicket'] = $team;
        $result = array_merge($result, $this->service->index($request));
        return $this->success($result);
    }

    /**
     * @return array
     */
    public function taskForm()
    {
        $form = $this->form('tasks');
        $extra_form = [];
        $extra_form['task_temp_id'] = mt_rand(100000, 99999999);
        $extra_form['priority_name'] = null;
        $extra_form['sub_category']['title'] = null;
        return array_merge($form, $extra_form);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listWorkSpace(Request $request)
    {
        $form = $this->taskForm();

        $userDivision = DB::table('user_divisions')
            ->where('user_id', '=', $request->user()->id)
            ->pluck('division_name');

        $display_style = $request->displayStyle;
        $query = DB::table('tasks as B')
            ->select('a.*');

        $tasks = clone $query;
        $assignees = clone $query;
        $team = clone $query;

        $tasks = $tasks->where('B.user_id', '=', $request->user()->id)
            ->count();

        $team = $team->whereIn('B.department', $userDivision)->count();

        $assignees = $assignees->leftJoin('task_tag_people as C', 'C.task_id', 'B.id')
            ->where('C.user_id', '=', $request->user()->id)
            ->count();

        return $this->success([
            'rows' => $tasks,
            'assignees' => $assignees,
            'form' => $form,
            'countAssignTicket' => $assignees,
            'countMyTicket' => $tasks,
            'countTeamTicket' => $team,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            if ($this->validation($request)) {
                return $this->error($this->validation($request), 422, [
                    "errors" => true
                ]);
            }

            // return $this->error('', '422', $this->service->generateDocNum(date('Y-m-d H:i:s')));
            $form = $request->form;

            $data = $this->service->formData($form, $request, 'store');

            $task = Task::create($data);

            $this->service->sendNewTaskNotification($request, $task);

            DB::commit();
            return $this->success([
                'rows' => []
            ], 'Data Saved!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }


    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $messages = [
            'form.title' => 'Title is required!',
            'form.description' => 'Description is required!',
            'form.department' => 'Department is required!',
            'form.sub_category' => 'Sub Category is required!',
        ];

        $validator = Validator::make($request->all(), [
            'form.title' => 'required',
            'form.department' => 'required',
            'form.sub_category' => 'required',
            'form.description' => 'required',
        ], $messages);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            if ($this->validation($request)) {
                return $this->error($this->validation($request), 422, [
                    "errors" => true
                ]);
            }

            // return $this->error('', '422', $this->service->generateDocNum(date('Y-m-d H:i:s')));
            $form = $request->form;

            $data = $this->service->formData($form, $request, 'update');

            $task = Task::where('id', $id)->update($data);

            if ($form['assignees']) {
                foreach ($form['assignees'] as $assigner) {
                    $user = User::where('username', $assigner)->first();
                    if (!$user) {
                        $user = User::where('username', $assigner['nik'])->first();
                    }
                    TaskTagPeople::updateOrCreate([
                        'user_id' => $user->username,
                        'task_id' => $id
                    ]);
                    $this->service->sendAssignNotification($request, $id, $user);
                }
            }

            DB::commit();
            return $this->success([
                'rows' => []
            ], 'Data Saved!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
