<?php

namespace App\Models\Task;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Task\TaskSubCategory
 *
 * @property int $sub_category_id
 * @property int $task_id
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSubCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSubCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSubCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSubCategory whereSubCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TaskSubCategory whereTaskId($value)
 * @mixin \Eloquent
 */
class TaskSubCategory extends Model
{
    use HasFactory;

    protected $table = 'task_sub_category';

    protected $guarded = [];
}
