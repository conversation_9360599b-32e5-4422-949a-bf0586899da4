<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Task\TaskSection;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class TaskSectionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $options = json_decode($request->options);
        $pages = isset($request->page) ? (int) $request->page : 1;
        $row_data = isset($request->itemsPerPage) ? (int) $request->itemsPerPage : 20;
        $sorts = isset($request->sortBy[0]) ? $request->sortBy[0]['key'] : "order_line";
        $department = isset($request->department) ? (string) $request->department : "";
        $order = (!empty($options->sortDesc)) ? (($options->sortDesc[0]) ? "desc" : "asc") : 'asc';
        $offset = $pages;

        $result = array();
        $query = TaskSection::selectRaw("*, 'actions' as ACTIONS");

        $result["total"] = $query->count();

        $all_data = $query->offset($offset)
            ->orderBy('department', $order)
            ->orderBy($sorts, $order)
            ->limit($row_data)
            ->get();

        $all_rows = TaskSection::where('department', 'LIKE', '%' . $department . '%')->get();
        $arr_rows = [];
        foreach ($all_rows as $item) {
            $arr_rows[] = [
                "title" => $item->title,
                "id" => $item->id,
            ];
        }

        $result = array_merge($result, [
            "rows" => $all_data,
            "simple" => $arr_rows
        ]);
        return $this->success($result);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): \Illuminate\Http\JsonResponse
    {
        $details = collect($request->details);
        DB::beginTransaction();
        try {

            foreach ($details as $detail) {
                if (empty($detail['title'])) {
                    return $this->error('Title cannot empty', '422');
                }
                $id = array_key_exists('id', $detail) ? $detail['id'] : null;
                $data = TaskSection::where('id', '=', $id)->first();
                if ($data) {
                    $data->title = Str::ucfirst($detail['title']);
                    $data->order_line = $detail['order_line'];
                    $data->department = $detail['department'];
                    $data->updated_at = Carbon::now();
                } else {
                    $data = new TaskSection();
                    $data->title = Str::ucfirst($detail['title']);
                    $data->order_line = $detail['order_line'];
                    $data->department = $detail['department'];
                    $data->user_id = $request->user()->id;
                    $data->created_at = Carbon::now();
                }
                $data->save();
            }

            DB::commit();

            return $this->success([
                "errors" => false
            ], 'Data inserted!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $messages = [
            'form.title' => 'Title is required!',
        ];

        $validator = Validator::make($request->all(), [
            'form.title' => 'required',
        ], $messages);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): \Illuminate\Http\JsonResponse
    {
        $data = TaskSection::where("id", "=", $id)->get();

        return $this->success([
            'rows' => $data
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        $form = $request->form;
        try {
            $data = [
                'name' => $form['name'],
            ];

            TaskSection::where("id", "=", $id)->update($data);

            return $this->success([
                "errors" => false
            ], 'Data updated!');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): \Illuminate\Http\JsonResponse
    {
        $details = TaskSection::where("id", "=", $id)->first();
        if ($details) {
            TaskSection::where("id", "=", $id)->delete();
            return $this->success([
                "errors" => false
            ], 'Row deleted!');
        }

        return $this->error('Row not found', 422, [
            "errors" => true
        ]);
    }
}
