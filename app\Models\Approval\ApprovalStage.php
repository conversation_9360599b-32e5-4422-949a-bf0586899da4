<?php

namespace App\Models\Approval;

use App\Models\Document\Document;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * App\Models\Approval\ApprovalStage
 *
 * @property int $id
 * @property int $approval_id
 * @property int $user_id
 * @property string $status
 * @property string|null $notes
 * @property string|null $response_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $document_id
 * @property int|null $cancel_by
 * @property string|null $cancel_date
 * @property-read \App\Models\Approval\Approval|null $approval
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read Document|null $document
 * @property-read User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereApprovalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereCancelBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereCancelDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereResponseDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApprovalStage whereUserId($value)
 * @mixin \Eloquent
 */
class ApprovalStage extends Model implements Auditable
{
    use HasFactory;

    use \OwenIt\Auditing\Auditable;

    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approval()
    {
        return $this->belongsTo(Approval::class);
    }

    public function document()
    {
        return $this->belongsTo(Document::class);
    }
}
