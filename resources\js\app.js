var app = new Vue({
    el: '#app',
    data: {
        message: 'Hello Vue!',
        kanban: false,
        kanbancontrols: false,
        kanbanhover: [],
        isloading: false,
        boardnames: ["one", "two", "three", "four"],
        boarddesc: ["new", "progress", "finalizing", "complete"],
        colours: {
            card: "",
            cardButtons: "black--text transparent",
        },
        search: '',
        selected: [],
        headers: [
            {
                text: 'Task Name',
                align: 'left',
                sortable: true,
                value: 'title',
            },
            {text: 'Short Description', value: 'short_desc'},
            {text: 'Priority', value: 'priority'},
        ],
        alt: [{text: 'Stage', value: 'stage'}, {text: 'Long Description', value: 'long_desc'},
            {text: 'Created by', value: 'user'},
            {text: 'Assigned to', value: 'assigned'},
            {text: 'Time and Data Created on', value: 'created'}
        ],
        tasks: [
            {
                "title": "a title",
                "short_desc": "this is a short desc, see how i fly",
                "long_desc": "the project involves writing a long sente",
                "priority": 3,
                "stage": 0,
                "user": "admin",
                "created": 1279679227,
                "assigned": ["fred", "simon", "nancy"]
            }, {
                "title": "another thing wrong",
                "short_desc": "fix the thing",
                "long_desc": "not much happening in this description",
                "priority": 9,
                "stage": 1,
                "user": "admin",
                "created": 1579279227,
                "assigned": ["admin"]
            },
            {
                "title": "one more",
                "short_desc": "this is a short desc, see how i fly",
                "long_desc": "if i was good at writing i would have become a writer or at least something something",
                "priority": 1,
                "stage": 3,
                "user": "james",
                "created": 1579679227,
                "assigned": ["joyce", "carla", "damien"]
            },
        ],
        emptytask: {
            "title": "",
            "short_desc": "",
            "long_desc": "",
            "priority": 1,
            "stage": 1,
            "user": "",
            "created": 0,
            "assigned": [],
            "notes": [],
        },
        emptynote: {"subject": "", "message": ""},


        taskform: [
            {"name": "title", "type": "text", "value": ""},
            {"name": "short_desc", "type": "text", "value": ""},
            {"name": "long_desc", "type": "field", "value": ""},
            {"name": "priority", "type": "number", "value": 1, "max_value": 10},
            {"name": "stage", "type": "number", "value": 1, "max_value": 3},
            {"name": "user", "type": "user", "value": ""},
            {"name": "assigned", "type": "list", "value": []},
            {"name": "created", "type": "date", "value": 0},
        ],


    },
    methods: {
        getColor(e, range) {
            if (range == "priority") {
                if (e > 8) return 'red'
                else if (e > 5) return 'orange'
                else if (e > 2) return 'blue'
                else return 'green'
            }
            if (range == "stage") {
                if (e >= 3) return 'green lighten-3'
                else if (e >= 2) return 'red lighten-3'
                else if (e >= 1) return 'orange lighten-3'
                else return 'blue lighten-3'
            }
        },
        time(UNIX_timestamp) {
            var a = new Date(UNIX_timestamp * 1000);
            var year = a.getFullYear();
            var month = a.getMonth() + 1;
            var date = a.getDate();
            var hour = a.getHours();
            var min = a.getMinutes();
            var sec = a.getSeconds();
            if (month < 10) {
                month = "0" + month
            }
            if (hour < 10) {
                hour = "0" + hour
            }
            var time = year + '-' + month + '-' + date + ' ' + hour + ':' + min;
            return time;
        },
        deleteselected(e) {
//this.$delete(e, this.tasks)
            var index = this.tasks.indexOf(this.selected[0]);
            if (index > -1) {
                this.$delete(this.tasks, index)
            }
            this.selected = []
        },
        adjuststage(e, k) {

            if (k == null) {
                var index = this.tasks.indexOf(this.selected[0]);
            } else {
                var index = k
            }
            if (index > -1) {
                var newstage = this.tasks[index]["stage"]
                if (e == 1) {
                    newstage += e
                    if (newstage >= 3) {
                        newstage = 3
                    }

                }
                if (e == -1) {
                    newstage += e
                    if (newstage <= 0) {
                        newstage = 0
                    }

                }

                this.tasks[index]["stage"] = newstage

            }
        },
    },
    vuetify: new Vuetify(),
})
