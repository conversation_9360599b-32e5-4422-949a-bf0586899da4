<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\User\UserDivision;
use App\Models\User\UserWorkLocation;
use App\Models\View\ViewEmployee;
use App\Models\View\ViewEmployeeLeave;
use App\Traits\RolePermission;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MasterEmployeeController extends Controller
{
    use RolePermission;

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $company = (isset($request->company)) ? $request->company : '';
        $username = (isset($request->username)) ? $request->username : '';
        $plucked = '';

        $employee = ViewEmployee::orderBy("Nik");
        if ($request->alias == 'stkpd') {
            $employee = $employee->where('WorkLocation', 'LIKE', '%JAKARTA%')
                ->get();
        } elseif ($request->alias == 'sim' || $request->alias == 'sik' || $request->alias == 'srm' || $request->alias == 'srk') {
            $user_department = UserDivision::where('user_id', '=', $request->user()->id)
                ->pluck('division_name');

            if ($request->user()->is_superuser == 'Yes') {
                $employee = $employee->where('Company', 'LIKE', '%' . $request->user()->company . '%')
                    ->where('WorkLocation', 'LIKE', $request->user()->location)
                    ->where("Nik", "LIKE", "%" . $username . "%");
                // ->where('Department', 'LIKE', '%' . $request->user()->department . '%');
            } else {
                $check_work_location = UserWorkLocation::where("user_id", "=", $request->user()->id)
                    ->pluck('work_location');

                if (count($check_work_location) > 0) {
                    $employee = $employee->where('Company', 'LIKE', '%' . $request->user()->company . '%')
                        ->whereIn('WorkLocation', $check_work_location)
                        ->whereIn('Department', $user_department);
                } else {
                    $employee = $employee->where('Company', 'LIKE', '%' . $request->user()->company . '%')
                        ->where('WorkLocation', 'LIKE', $request->user()->location)
                        ->whereIn('Department', $user_department);
                    // ->where('Department', 'LIKE', '%' . $request->user()->department . '%');
                }
            }

            if ($request->user()->hasAnyRole(['Personal'])) {
                $employee = $employee->where('Name', '=', $request->user()->name);
            }
        } elseif ($request->alias == 'fsr') {
            $employee = $employee->where('Company', 'LIKE', '%' . $request->user()->company . '%')
                ->where('WorkLocation', 'LIKE', '%JAKARTA%');
        } elseif ($request->alias == 'gsv' || $request->alias == 'abr') {
            $employee = $employee
                ->where('Company', 'LIKE', '%' . $request->user()->company . '%')
                ->where('WorkLocation', 'LIKE', '%JAKARTA%');
        } else {
            if (!$request->user()->role('Superuser')) {
                $employee = $employee->limit(10);
            } else {
                $employee = $employee->limit(100);
            }
        }

        if ($request->alias != 'stkpd') {
            $employee = $employee->where('Company', 'LIKE', '%' . $company . '%')
                ->get();

            $plucked = $employee->pluck('Name');
        }

        return response()->json([
            'rows' => $employee,
            'plucked' => $plucked
        ]);
    }

    public function relatedEmployee(Request $request)
    {
        $department = (isset($request->department)) ? $request->department : $request->user()->department;

        $employee = ViewEmployee::where('Department', 'LIKE', '%' . substr($department, 0, 4) . '%')
            ->select('Nik as nik', 'Name as name')
            ->get();
        return response()->json([
            'rows' => $employee,
        ]);
    }

    /**
     * @param Request $request
     * @param $nik
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function leave(Request $request, $nik)
    {
        if ($request->user()->department != 'IT - SAP') {
            $date = Carbon::now()->subDays(100)->format('Ymd');
            $employee_leave = ViewEmployeeLeave::select(
                "DocumentReferenceNumber",
                DB::raw("concat(FORMAT(DateFrom, 'dd MMMM yyyy'), ' - ', FORMAT(DateTo, 'dd MMMM yyyy')) as date_from_to"),
                'Jenis Cuti AS jenisCuti'
            )
                ->where('Nik', '=', $nik)
                ->where(DB::raw('CAST(DateFrom AS Date)'), '>=', $date)
                ->orderBy('DateFrom', 'desc')
                ->get();

            return $this->success($employee_leave);
        } else {
            $date = Carbon::now()->subDays(100)->format('Ymd');
            $employee_leave = ViewEmployeeLeave::select(
                "DocumentReferenceNumber",
                DB::raw("concat(FORMAT(DateFrom, 'dd MMMM yyyy'), ' - ', FORMAT(DateTo, 'dd MMMM yyyy')) as date_from_to"),
                'Jenis Cuti AS jenisCuti'
            )
                ->where('Nik', '=', $nik)
                // ->where(DB::raw('CAST(DateFrom AS Date)'), '>=', $date)
                ->orderBy('DateFrom', 'desc')
                ->get();

            return $this->success($employee_leave);
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function employeeByName(Request $request)
    {
        $name = $request->name;
        $employee = ViewEmployee::where('Name', '=', $name)->first();
        if ($employee) {
            return response()->json($employee);
        } else {
            return response()->json('');
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function departments(Request $request)
    {
        $departments = ViewEmployee::where('Company', '=', $request->user()->company)
            ->distinct()
            ->select('Department')
            ->where('Department', 'LIKE', '%IT - %')
            ->orderBy('Department')
            ->pluck('Department');

        return response()->json($departments);
    }
}
