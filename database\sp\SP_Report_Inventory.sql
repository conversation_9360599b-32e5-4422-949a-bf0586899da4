USE [BC_DEV_SAP]
GO
    /****** Object:  StoredProcedure [dbo].[SP_Report_Inventory]    Script Date: 4/9/2024 2:03:23 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO -- Author:		<Author,,Name>
    -- Create date: <Create Date,,>
    -- Description:	<Description,,>
    -- =============================================
    ALTER PROCEDURE [dbo].[SP_Report_Inventory] (
        @FromDate DATE,
        @EndDate DATE,
        @Whs NVARCHAR(200),
        @DocType NVARCHAR(200)
    ) AS -- BEGIN
    BEGIN -- SET NOCOUNT ON added to prevent extra result sets from
    -- interfering with SELECT statements.
SET NOCOUNT ON;
select CONCAT(
        A.prefix,
        FORMAT(A.doc_number, '00000'),
        A.suffix
    ) as doc_num,
    A.post_date,
    B.item_code,
    B.item_name,
    <PERSON>.qty,
    B.uom,
    B.notes,
    B.resv_number,
    D.SAP_GIRNo,
    C.Employee<PERSON>d,
    C.EmployeeName,
    D.Department,
    D.WhsCode,
    D.Replacement,
    D.DocDate,
    D.DocNum,
    D.WorkLocation,
    D.ItemType
from inventories as A
    left join inventory_details As B on A.id = B.header_id
    left join resv_details as C on B.resv_detail_id = C.LineEntry
    left join resv_headers as D on C.U_DocEntry = D.U_DocEntry
WHERE CONVERT(varchar, A.post_date, 23) BETWEEN @FromDate AND @EndDate
    AND A.doc_type = @DocType
    AND A.status in ('O', 'C')
    AND B.whs_code in (
        SELECT Split.a.value('.', 'NVARCHAR(MAX)') DATA
        FROM (
                SELECT CAST(
                        '<X>' + REPLACE(@Whs, ',', '</X><X>') + '</X>' AS XML
                    ) AS String
            ) AS A
            CROSS APPLY String.nodes('/X') AS Split(a)
    )
END