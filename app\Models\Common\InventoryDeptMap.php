<?php

namespace App\Models\Common;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Common\InventoryDeptMap
 *
 * @property int $id
 * @property string|null $department
 * @property string|null $prefix
 * @property int|null $created_by
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap query()
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap wherePrefix($value)
 * @method static \Illuminate\Database\Eloquent\Builder|InventoryDeptMap whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class InventoryDeptMap extends Model
{
    use HasFactory;
    protected $connection = 'sqlsrv';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
