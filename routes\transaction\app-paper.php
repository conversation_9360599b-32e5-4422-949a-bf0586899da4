<?php

use App\Http\Controllers\Paper\PaperController;
use Illuminate\Support\Facades\Route;

Route::get('paper/print', [PaperController::class, 'print']);
Route::get('reference-no/{username}', [PaperController::class, 'referenceNo']);

Route::apiResource('paper', PaperController::class)->names([
    'index' => 'data-app-paper.index',
    'store' => 'data-app-paper.store',
    'show' => 'data-app-paper.show',
    'update' => 'data-app-paper.update',
    'destroy' => 'data-app-paper.destroy'
]);