<?php

namespace App\Http\Controllers\SapS4;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;


class MappingErrorS4Controller extends Controller
{
    public function mappingErrorItemCode(Request $request)
    {
        $datas = DB::connection('sqlsrv')
            ->table('api_responses')
            ->select('reservation_number')
            ->distinct()
            ->where('message', 'Success')
            ->get()
            ->pluck('reservation_number');

        $data = DB::connection('sqlsrv')
            ->table('api_responses')
            ->select('reservation_number')
            ->distinct()
            ->where('message', 'Error')
            ->whereNotIn('reservation_number', $datas)
            ->get()
            ->pluck('reservation_number');

        $itemCode = DB::connection('sqlsrv')
            ->table('resv_details')
            ->leftJoin("resv_headers", "resv_headers.U_DocEntry", "resv_details.U_DocEntry")
            ->select("resv_details.ItemCode", "resv_details.WhsCode", "resv_headers.WorkLocation", "resv_headers.Division")
            // ->whereBetween("ReqDate", ['2023-01-01', '2023-12-31'])
            ->whereIn("resv_headers.DocNum", $data)
            ->distinct()
            ->get();

        $items = [];

        foreach ($itemCode as $key => $value) {
            $mapping = DB::connection('sqlsrv')
                ->table('mapping_whs')
                ->where('whs_code_b1', $value['WhsCode'])
                ->first();


            if ($mapping) {
                $items[] = [
                    'WorkLocation' => $value['WorkLocation'],
                    'Division' => $value['Division'],
                    'ItemCode' => $value['ItemCode'],
                    'Whs Code' => $mapping->whs_code_s4,
                    // 'Plan' => $mapping->plan,
                ];
            } else {
                $items[] = [
                    'WorkLocation' => $value['WorkLocation'],
                    'Division' => $value['Division'],
                    'ItemCode' => $value['ItemCode'],
                    'Whs Code' => $value['WhsCode'],
                    // 'Plan' => '',
                ];
            }

        }

        return response()->json($items);
    }
}
