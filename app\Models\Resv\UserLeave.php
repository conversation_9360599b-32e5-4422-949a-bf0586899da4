<?php

namespace App\Models\Resv;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Resv\UserLeave
 *
 * @method static \Illuminate\Database\Eloquent\Builder|UserLeave newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserLeave newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserLeave query()
 * @mixin \Eloquent
 */
class UserLeave extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv2';
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;
    protected $table = 'OUSR_LEAVE';
    protected $primaryKey = 'U_DocEntry';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
}
