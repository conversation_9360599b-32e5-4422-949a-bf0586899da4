<?php

namespace App\Http\Controllers\Approval;

use App\Http\Controllers\Controller;
use App\Models\Approval\Approval;
use App\Models\Approval\ApprovalApprover;
use App\Models\Approval\ApprovalRule;
use App\Models\User;
use App\Services\ApprovalMasterDataService;
use App\Services\ApprovalService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ApprovalRuleController extends Controller
{
    public $service;

    /**
     * MasterUserController constructor.
     */
    public function __construct(ApprovalService $service)
    {
        $this->service = $service;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $type = isset($request->type) ? (string)$request->type : 'index';
        $result = [];

        $extra_list['rules'] = [
            [
                'name' => null,
                'operator' => 'IN',
                'value' => null,
                'id' => null,
            ],
            [
                'name' => null,
                'operator' => 'IN',
                'value' => null,
                'id' => null,
            ]
        ];

        $extra_list['approver'] = [
            [
                'user_id' => null,
                'sequence' => 1,
                'types' => 'Any',
                'is_specimen' => 'N',
                'id' => null,
            ]
        ];
        $extra_list['show_speciment'] = 'Y';

        $result['form'] = array_merge($this->form('approvals'), $extra_list);
        $result['itemSigner'] = ['HAMID MINA', 'ADWIN SUDARNI', 'HASRAT', 'MIKHAEL SE', 'WILLIAM THEO'];

        // $documentService->peruriLogin();
        $approvalData = new ApprovalMasterDataService();

        // $result['employee'] = ViewEmployee::select('Nik', 'Name')
        //     ->where('IsActive', 'True')
        //     ->where('Company', 'PT IMIP')
        //     ->get();

        $result = array_merge($result, $this->service->index($request, $type), $approvalData->index());

        return $this->success($result);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        DB::beginTransaction();
        try {
            $approver = $request->approver;
            $rules = $request->rules;
            // return $this->error('', 422, $request->all());
            $data = Approval::create($this->service->formData($request));

            $this->processDetails($approver, $rules, $data->id);

            DB::commit();
            return $this->success([
                "errors" => false
            ], 'Data inserted!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $request
     * @return false|string
     */
    protected function validation($request)
    {
        $messages = [
            'name' => 'Name is required!',
        ];

        $validator = Validator::make($request->all(), [
            'name' => 'required',
        ], $messages);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->getMessageBag()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * @param $approver
     * @param $rules
     * @param $id
     * @return void
     */
    protected function processDetails($approver, $rules, $id)
    {
        if ($rules) {
            $this->service->storeApprovalRules($rules, $id);
        }

        if ($approver) {
            $this->service->storeApprover($approver, $id);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id): JsonResponse
    {
        $data = Approval::where("id", "=", $id)->first();
        $extra_list['emails'] = [
            [
                'email' => null
            ]
        ];
        $extra_list['password'] = null;
        $extra_list['banks'] = [
            [
                'name' => null,
                'branch' => null,
                'contact_account_name' => null,
                'contact_account_number' => null,
            ]
        ];
        $form = array_merge($this->form('contacts'), $extra_list);


        return $this->success([
            "rows" => $data,
            "form" => $form,
            "count" => ($data) ? 1 : 0
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), 422, [
                "errors" => true
            ]);
        }

        try {
            $approver = $request->approver;
            $rules = $request->rules;

            $formData = $this->service->formData($request);
            $approval = Approval::find($id);
            foreach ($formData as $index => $data) {
                $approval->$index = $data;
            }
            $approval->save();

            $this->processDetails($approver, $rules, $id);

            DB::commit();

            return $this->success([
                "errors" => false
            ], 'Data updated!');
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), 422, [
                "errors" => true,
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $details = Approval::where("id", "=", $id)->first();
        if ($details) {
            Approval::where("id", "=", $id)->delete();
            return $this->success([
                "errors" => false
            ], 'Row deleted!');
        }

        return $this->error('Row not found', 422, [
            "errors" => true
        ]);
    }

    /**
     * @param $id
     * @return void
     */
    public function deleteApprover($id)
    {
        $data = ApprovalApprover::find((int)$id);
        if ($data) {
            $data->delete();
        }
    }

    /**
     * @param $id
     * @return void
     */
    public function deleteRules($id)
    {
        $data = ApprovalRule::find((int)$id);
        if ($data) {
            $data->delete();
        }
    }

    public function storeSpecimen(Request $request)
    {
        try {
            //code...
            $data_file = $request->file('image');
            $image = $request->image;
            $id_user = $request->userId;

            $file_name = $id_user . '.png';
            $contents = file_get_contents($image);
            // $file_name = $doc_id . '_DOWNLOAD.pdf';
            // $file_path = public_path('images/approval/');
            // $path_download = $file_path . $file_name;

            // if (!file_exists($file_path)) {
            //     if (!mkdir($file_path, 0777, true) && !is_dir($file_path)) {
            //         throw new \RuntimeException(
            //             sprintf(
            //                 'Directory "%s" was not created',
            //                 $file_path
            //             )
            //         );
            //     }
            // }
            // file_put_contents($path_download, $contents);

            $destination_path = custom_disk_path("/images/approval/$file_name", "sftp");
            custom_disk_put($destination_path, $contents);
            // $data_file->storeAs($destination_path, $file_name, 'sftp');


            $approval = User::find($id_user);
            $approval->specimen_sign = $image;
            $approval->save();

            return $this->success([], 'success updated');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
