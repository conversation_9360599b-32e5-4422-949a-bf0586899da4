<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Models\Settings\RegisterPrivy;
use App\Services\ApprovalPrivyService;
use App\Services\RegisterPrivyService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class PrivyRegisterStatusController extends Controller
{
    public function show($id)
    {
        $row = RegisterPrivy::find($id);
        $service = new ApprovalPrivyService();
        $service->login();

        $register = new RegisterPrivyService();

        $url = $this->getConfigByName('PrivyCheckStatusRegister', 'PRIVY');
        $token = $this->getConfigByName('PrivyAuthToken', 'PRIVY');
        $timestamp = Carbon::now()->format('Y-m-d\TH:i:sP');

        $response = Http::withoutVerifying()
            ->timeout(90)
            ->retry(3, 100)
            ->withOptions(["verify" => false])
            ->withToken($token)
            ->withHeaders([
                'Timestamp' => $timestamp,
                'Signature' => $this->signature($row, $timestamp, 'POST'),
                'Request-ID' => strtotime($timestamp),
            ])
            ->post($url, $register->statusParams($row))
            ->throw(function ($response, $e) {
                throw new \Exception('E-DIGITAL SIGN: ' . json_decode($response->collect()), 1);
            });

        $row->privy_id = (array_key_exists('privy_id', $response->collect()['data'])) ? $response->collect()['data']['privy_id'] : null;
        $row->status = $response->collect()['data']['status'];
        $row->save();

        return $this->success([
            'data' => $response->collect(),
        ]);
    }

    protected function signature($row, $timestamp, $httpVerb)
    {
        $register = new RegisterPrivyService();
        $api_key = $this->getConfigByName('PrivyApiKey', 'PRIVY');
        $api_secret = $this->getConfigByName('PrivySecretKey', 'PRIVY');

        $body = $register->statusParams($row);

        Arr::forget($body, 'identity');
        Arr::forget($body, 'selfie');

        $strBody = json_encode($body);
        $strBody = str_replace(" ", "", $strBody);
        $method = $httpVerb;
        $body_md5 = base64_encode(md5($strBody, true));
        $hmac_signature = $timestamp . ":" . $api_key . ":" . $method . ":" . $body_md5;
        $byte_key = utf8_encode($api_secret);
        $new_hmac = utf8_encode($hmac_signature);
        $hmac = hash_hmac('sha256', $new_hmac, $byte_key, true);
        $base64_message = base64_encode($hmac);
        $auth_string = $api_key . ":" . $base64_message;
        $signature = base64_encode($auth_string);
        return $signature;
    }

    public function store()
    {
    }
}
