<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnToResvHeadersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('resv_headers');
        Schema::dropIfExists('resv_details');
        Schema::dropIfExists('resv_req_items');

        Schema::create('resv_headers', function (Blueprint $table) {
            $table->id('U_DocEntry');
            $table->unsignedInteger('DocNum');
            $table->date('DocDate')->nullable();
            $table->date('RequiredDate')->nullable();
            $table->unsignedInteger('Requester')->nullable();
            $table->string('Division', 200)->nullable();
            $table->string('Department', 200)->nullable();
            $table->string('Company', 200)->nullable();
            $table->text('Memo')->nullable();
            $table->string('Canceled', 20)->nullable();
            $table->string('DocStatus', 20)->nullable();
            $table->string('ApprovalStatus', 20)->nullable();
            $table->unsignedInteger('ApprovalKey')->nullable();
            $table->string('isConfirmed', 20)->nullable();
            $table->date('ConfirmDate')->nullable();
            $table->unsignedInteger('ConfirmBy')->nullable();
            $table->unsignedInteger('SAP_GIRNo')->nullable();
            $table->unsignedInteger('SAP_TrfNo')->nullable();
            $table->unsignedInteger('SAP_PRNo')->nullable();
            $table->date('CreateDate')->nullable();
            $table->time('CreateTime')->nullable();
            $table->unsignedBigInteger('CreatedBy')->nullable();
            $table->date('UpdateDate')->nullable();
            $table->time('UpdateTime')->nullable();
            $table->unsignedBigInteger('UpdatedBy')->nullable();
            $table->string('RequestType', 50)->nullable();
            $table->string('U_NIK', 30)->nullable();
            $table->string('WhsCode', 20)->nullable();
            $table->string('WhTo', 20)->nullable();
            $table->string('Token', 200)->nullable();
            $table->string('CreatedName', 200)->nullable();
            $table->string('RequesterName', 200)->nullable();
            $table->string('UrgentReason', 200)->nullable();
            $table->string('ItemType', 200)->nullable();
            $table->string('Is_Urgent', 20)->nullable();
            $table->string('U_ATTACH', 200)->nullable();
            $table->string('CategoryType', 100)->nullable();
            $table->string('UsageFor', 100)->nullable();
            $table->string('VehicleNo', 100)->nullable();
            $table->string('Mileage', 100)->nullable();
            $table->string('Customer', 200)->nullable();
            $table->unsignedInteger('SAP_SONo')->nullable();
            $table->string('Replacement', 100)->nullable();
            $table->string('EmployeeType', 100)->nullable();
            $table->string('WorkLocation', 200)->nullable();
            $table->string('DocumentType', 200)->nullable();
            $table->string('CostType', 50)->nullable();
            $table->string('Usage', 50)->nullable();
            $table->unsignedInteger('SAP_GIRNoOld')->nullable();
            $table->unsignedInteger('SAP_PRNoOld')->nullable();
            $table->timestamps();

            $table->index('DocNum');
            $table->index('DocDate');
            $table->index('RequiredDate');
            $table->index('Requester');
            $table->index('Division');
            $table->index('Department');
            $table->index('DocStatus');
            $table->index('ApprovalStatus');
            $table->index('WhsCode');
        });

        Schema::create('resv_details', function (Blueprint $table) {
            $table->id('LineEntry');
            $table->unsignedBigInteger('U_DocEntry');
            $table->unsignedInteger('LineNum')->nullable();
            $table->string('ItemCode', 20)->nullable();
            $table->text('ItemName')->nullable();
            $table->string('WhsCode', 20)->nullable();
            $table->string('UoMCode', 20)->nullable();
            $table->string('UoMName', 20)->nullable();
            $table->double('ReqQty')->nullable();
            $table->date('ReqDate')->nullable();
            $table->text('ReqNotes')->nullable();
            $table->unsignedInteger('OtherResvNo')->nullable();
            $table->string('RequestType', 20)->nullable();
            $table->double('QtyReadyIssue')->nullable();
            $table->string('LineStatus', 20)->nullable();
            $table->unsignedInteger('SAP_GIRNo')->nullable();
            $table->unsignedInteger('SAP_TrfNo')->nullable();
            $table->unsignedInteger('SAP_PRNo')->nullable();
            $table->string('ItemCategory', 30)->nullable();
            $table->unsignedInteger('OIGRDocNum')->nullable();
            $table->string('InvntItem', 20)->default('Y')->nullable();
            $table->string('U_ATTACH', 200)->nullable();
            $table->string('AssetCode', 50)->nullable();
            $table->string('AssetName', 200)->nullable();
            $table->string('ItemGroup', 50)->nullable();
            $table->string('SubGroup', 50)->nullable();
            $table->double('AvailableQty')->nullable();
            $table->timestamp('AvailableQtyDate')->nullable();
            $table->string('U_Department', 100)->nullable();
            $table->string('U_Period', 100)->nullable();
            $table->string('U_Category', 100)->nullable();
            $table->string('U_AppResBy', 100)->nullable();
            $table->unsignedInteger('SAP_SONo')->nullable();
            $table->string('VehicleNo', 100)->nullable();
            $table->string('Mileage', 100)->nullable();
            $table->string('EmployeeName', 200)->nullable();
            $table->string('EmployeeId', 15)->nullable();
            $table->string('OrderId', 50)->nullable();
            $table->double('OnHand')->nullable();
            $table->string('U_ItemType', 50)->nullable();
            $table->timestamps();

            $table->index('U_DocEntry');
            $table->index('ItemCode');
            $table->index('ReqDate');
            $table->index('AssetCode');
            $table->index('AssetName');
            $table->index('LineNum');
        });

        Schema::create('resv_req_items', function (Blueprint $table) {
            $table->id('U_DocEntry');
            $table->string('U_Description', 200)->nullable();
            $table->string('U_UoM', 20)->nullable();
            $table->string('U_Status', 20)->nullable();
            $table->text('U_Remarks')->nullable();
            $table->text('U_Supporting')->nullable();
            $table->string('U_CreatedBy', 200)->nullable();
            $table->string('U_Comments', 200)->nullable();
            $table->string('U_ItemType', 200)->nullable();
            $table->timestamp('U_CreatedAt')->nullable();
            $table->timestamps();
        });

        Schema::create('safety_data', function (Blueprint $table) {
            $table->id('U_DocEntry');
            $table->date('date_out')->nullable();
            $table->string('item_code', 20)->nullable();
            $table->string('uom', 20)->nullable();
            $table->string('item_name')->nullable();
            $table->string('id_card', 20)->nullable();
            $table->string('employee_name')->nullable();
            $table->string('company')->nullable();
            $table->string('department')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->string('notes')->nullable();
            $table->double('qty')->nullable();
            $table->timestamps();
            $table->unsignedBigInteger('header_id')->nullable();
            $table->unsignedBigInteger('detail_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('resv_headers');
        Schema::dropIfExists('resv_details');
        Schema::dropIfExists('resv_req_items');
        Schema::dropIfExists('safety_data');
    }
}
