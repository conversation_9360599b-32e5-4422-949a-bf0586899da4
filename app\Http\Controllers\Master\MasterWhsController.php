<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Services\SapS4Service;
use App\Traits\ConnectHana;
use Illuminate\Http\Request;

class MasterWhsController extends Controller
{
    use ConnectHana;
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $service = new SapS4Service();
        $company = $request->user()->company;
        $user = $request->user();
        $result = $service->getSloc(1, 100, null, $company, $user);
        $arr = [];
        if ($result) {
            foreach ($result['DATA'] as $item) {
                $arr[] = [
                    "name" => $item['LGORT'],
                    "whs_name" => $item['LGORT'] . ' - ' . $item['LGOBE'],
                ];
            }
        }

        return $this->success([
            "simple" => $arr,
            "result" => $result,
            "sloc" => ['IM01', 'IM02', 'IM03']
        ]);
    }
}
