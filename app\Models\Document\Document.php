<?php

namespace App\Models\Document;

use App\Models\Common\Attachment;
use App\Models\User;
use App\Models\Approval\ApprovalStage;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * App\Models\Document\Document
 *
 * @property int $id
 * @property string $document_number
 * @property string $filename
 * @property string|null $filepath
 * @property string|null $extension
 * @property string|null $type
 * @property int $value
 * @property string|null $document_date
 * @property string|null $meta
 * @property string|null $location
 * @property string|null $password
 * @property string|null $reason
 * @property string|null $specimen_path
 * @property string|null $profile_name
 * @property string|null $vis_llx
 * @property string|null $vis_lly
 * @property string|null $vis_urx
 * @property string|null $vis_ury
 * @property string|null $identity_type
 * @property string|null $identity_number
 * @property string|null $identity_name
 * @property int|null $signature_page
 * @property string|null $jwt_token
 * @property string|null $ref_token
 * @property int|null $temp_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $type_no
 * @property string $status
 * @property string|null $vis_digisign_llx
 * @property string|null $vis_digisign_lly
 * @property string|null $vis_digisign_urx
 * @property string|null $vis_digisign_ury
 * @property int|null $materai_page
 * @property int|null $sign_page
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $company
 * @property int|null $customer_id
 * @property int|null $document_sub_type_id
 * @property string|null $remark
 * @property string|null $customer_name
 * @property string|null $external_document_number
 * @property string|null $digisign_coordinate
 * @property string|null $digisign_top
 * @property string|null $digisign_left
 * @property string|null $digisign_width
 * @property string|null $digisign_height
 * @property string|null $meterai_coordinate
 * @property string|null $meterai_top
 * @property string|null $meterai_left
 * @property string|null $meterai_width
 * @property string|null $meterai_height
 * @property string|null $coordinate_document_path
 * @property string|null $digisign_id
 * @property string|null $digisign_color
 * @property string|null $digisign_stamp_type
 * @property string|null $digisign_page_index
 * @property string|null $meterai_id
 * @property string|null $meterai_color
 * @property string|null $meterai_stamp_type
 * @property string|null $meterai_page_index
 * @property string|null $batch_id
 * @property int|null $invoice_id
 * @property string|null $total
 * @property string $document_type
 * @property \Illuminate\Database\Eloquent\Collection<int, ApprovalStage> $approver
 * @property string $sign_payment
 * @property int|null $approval_id
 * @property string $internal_document
 * @property string|null $str_url
 * @property string|null $document_token
 * @property string|null $privy_status
 * @property string|null $reference_number
 * @property-read int|null $approver_count
 * @property-read Attachment|null $attachment
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \OwenIt\Auditing\Models\Audit> $audits
 * @property-read int|null $audits_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Document\DocumentCoordinate> $coordinate
 * @property-read int|null $coordinate_count
 * @property-read mixed $sign_payments
 * @property-read User|null $userCreate
 * @method static \Illuminate\Database\Eloquent\Builder|Document newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Document newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Document query()
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereApprovalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereApprover($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCoordinateDocumentPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereCustomerName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignCoordinate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignLeft($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignPageIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignStampType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignTop($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDigisignWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDocumentDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDocumentNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDocumentSubTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDocumentToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereDocumentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereExtension($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereExternalDocumentNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereFilename($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereFilepath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereIdentityName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereIdentityNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereIdentityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereInternalDocument($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereJwtToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMateraiPage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeta($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiCoordinate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiHeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiLeft($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiPageIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiStampType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiTop($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereMeteraiWidth($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document wherePrivyStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereProfileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereRefToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereReferenceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereRemark($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereSignPage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereSignPayment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereSignaturePage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereSpecimenPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereStrUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereTempId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereTypeNo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVisDigisignLlx($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVisDigisignLly($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVisDigisignUrx($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVisDigisignUry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVisLlx($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVisLly($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVisUrx($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereVisUry($value)
 * @property string|null $signer
 * @method static \Illuminate\Database\Eloquent\Builder|Document whereSigner($value)
 * @mixin \Eloquent
 */
class Document extends Model implements Auditable
{
    use HasFactory;

    use \OwenIt\Auditing\Auditable;


    protected $guarded = [];

    protected $connection = 'sqlsrv';

    protected $casts = [
        'document_sub_type_id' => 'integer',
    ];

    protected $appends = [
        'sign_payments'
    ];

    public function getSignPaymentsAttribute()
    {
        return ($this->sign_payment) ? (($this->sign_payment == '2') ? 'Pay Per Sign' : 'Pay Per Document') : null;
    }

    public function coordinate()
    {
        return $this->hasMany(DocumentCoordinate::class, 'document_id', 'id');
    }

    public function attachment()
    {
        return $this->hasOne(Attachment::class, 'source_id', 'id')
            ->where('type', '=', 'peruri');
    }

    public function userCreate()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function approver()
    {
        return $this->hasMany(ApprovalStage::class, 'document_id', 'id');
    }
}
