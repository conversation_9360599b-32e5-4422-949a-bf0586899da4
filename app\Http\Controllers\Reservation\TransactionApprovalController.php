<?php

namespace App\Http\Controllers\Reservation;

use App\Http\Controllers\Controller;
use App\Models\Resv\ResvHeader;
use App\Models\Resv\SafetyData;
use App\Services\ApprovalEngineService;
use App\Services\CancelReservationService;
use App\Traits\Approval;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class TransactionApprovalController extends Controller
{
    use Approval;

    public function __construct()
    {
        $this->middleware(['direct_permission:Reservation Approval-index'])->only('index');
        $this->middleware(['direct_permission:Reservation Approval-store'])->only('store');
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $cherry_token = $request->user()->cherry_token;
        $employee_code = $request->user()->employee_code;
        $status_approval = $request->status_approval;

        $documents = Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'GetList',
            'ModelCode' => 'ApprovalRequests',
            'UserName' => $request->user()->username,
            'Token' => $cherry_token,
            'OrderBy' => 'InsertStamp',
            'OrderDirection ' => 'desc',
            'ParameterData' => [
                [
                    'ParamKey' => 'ApproverCode',
                    'ParamValue' => $employee_code,
                    'Operator' => 'eq'
                ],
                [
                    'ParamKey' => 'StatusId',
                    'ParamValue' => $status_approval,
                    'Operator' => 'eq'
                ]
            ]
        ]);

        $collect = $documents->collect();
        $array_result = [];

        //return response()->json($collect);

        $result = [];
        $result['ApprovalStatus'] = ['Pending', 'Approved', 'Rejected'];
        $result['filter'] = ['Document Reference ID', 'Request Date', 'Status'];

        if (!isset($collect['Data'])) {
            return $this->success(array_merge($result, []));
        }

        foreach ($collect['Data'] as $datum) {
            $documents = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'GADocuments',
                'UserName' => $request->user()->username,
                'Token' => $cherry_token,
                'ParameterData' => [
                    [
                        'ParamKey' => 'Code',
                        'ParamValue' => $datum['ModelEntityCode'],
                        'Operator' => 'eq'
                    ],
                ]
            ]);

            //return response()->json($documents->collect()['Data']);
            if ($documents->collect()['Data']) {
                $doc_entry = ResvHeader::where(
                    'DocNum',
                    '=',
                    intval($documents->collect()['Data'][0]['DocumentReferenceID'])
                )
                    ->first();

                $array_result[] = [
                    'Keys' => Str::random(20),
                    'TypeName' => $datum['TypeName'],
                    'ApproveUrl' => $datum['ApproveUrl'],
                    'ApproveToken' => $datum['ApproveToken'],
                    'RejectUrl' => $datum['RejectUrl'],
                    'RejectToken' => $datum['RejectToken'],
                    'Code' => $datum['Code'],
                    'DocDate' => $datum['InsertStamp'],
                    'Details' => $documents->collect()['Data'][0]['DocumentContent'],
                    'DocumentReferenceID' => $documents->collect()['Data'][0]['DocumentReferenceID'],
                    'DocNum' => $documents->collect()['Data'][0]['DocumentReferenceID'],
                    'RequesterName' => $datum['RequesterName'],
                    'StatusId' => $datum['StatusId'],
                    'U_DocEntry' => ($doc_entry) ? $doc_entry->U_DocEntry : 0,
                    'Date' => ($datum['Date']) ?
                        date('Y-m-d H:i:s', (int) substr($datum['Date'], 6, 10)) : '',
                ];
            }
        }

        return $this->success(array_merge($result, [
            'rows' => $array_result,
            'total' => count($collect['Data']),
        ]));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function action(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $code = [];
            $selected = collect($request->selected);
            foreach ($selected as $item) {
                $code[] = $item['Code'];
            }

            $action = $request->action;
            $documents = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'ApprovalRequests',
                'UserName' => $request->user()->username,
                'Token' => $request->user()->cherry_token,
                'ParameterData' => [
                    [
                        'ParamKey' => 'Code',
                        'ParamValue' => implode(',', $code),
                        'Operator' => 'in'
                    ]
                ]
            ]);

            $concat_array = [];
            foreach ($documents->collect()['Data'] as $index => $item) {
                $item = (object) $item;
                $item->StatusId = $action;
                $concat_array[] = $item;
            }

            //return response()->json($concat_array);

            $approval = Http::post(config('app.cherry_service_req'), [
                'CommandName' => 'SubmitList',
                'ModelCode' => 'ApprovalRequests',
                'UserName' => $request->user()->username,
                'Token' => $request->user()->cherry_token,
                'ModelData' => json_encode($concat_array),
                'ParameterData' => []
            ]);
            return $this->success($approval->collect(), $approval->collect()['Message']);
        } catch (\Exception $exception) {
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancelDocument(Request $request)
    {
        $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');
        $approvalEngineService = new ApprovalEngineService();

        DB::beginTransaction();
        try {
            $header = ResvHeader::where("U_DocEntry", "=", $request->form['U_DocEntry'])->first();
            // $header = ReservationHeader::where("U_DocEntry", "=", $request->form['U_DocEntry'])->first();

            if ($header->ApprovalStatus == 'W') {
                DB::connection('sqlsrv')
                    ->table('resv_headers')
                    ->where('U_DocEntry', '=', $request->form['U_DocEntry'])
                    ->update([
                        'ApprovalStatus' => 'N'
                    ]);

                if ($checkUseNewApproval == '1') {
                    $approvalEngineService->cancelApproval($header);
                } else {
                    $cherry_token = $request->user()->cherry_token;

                    $headers = [
                        'Content-Type' => 'application/json',
                        'Accept' => 'application/json'
                    ];

                    $documents = Http::withHeaders($headers)
                        ->post(config('app.cherry_service_req'), [
                            'CommandName' => 'GetList',
                            'ModelCode' => 'GADocuments',
                            'UserName' => $request->user()->username,
                            'Token' => $cherry_token,
                            'ParameterData' => [
                                [
                                    'ParamKey' => 'DocumentReferenceID',
                                    'ParamValue' => $request->form['DocNum'],
                                    'Operator' => 'eq'
                                ]
                            ]
                        ]);

                    $collect = $documents->collect();

                    Http::post(config('app.cherry_service_req'), [
                        'CommandName' => 'Remove',
                        'ModelCode' => 'GADocuments',
                        'UserName' => $request->user()->username,
                        'Token' => $cherry_token,
                        'ModelData' => $collect['Data'][0]
                    ]);
                }

            } else {
                if (!$request->user()->hasAnyRole(['Superuser'])) {
                    if ($header->CreatedBy != $request->user()->username) {
                        throw new \Exception('You are not allowed to cancel this document', 1);
                    }
                }
                $service = new CancelReservationService();
                $service->update($header, $request);
            }


            DB::commit();

            return $this->success([], 'Document Canceled!');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->error($exception->getMessage(), '422', [
                'trace' => $exception->getTrace(),
            ]);
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvalStages(Request $request)
    {
        $checkUseNewApproval = $this->getConfigByName('ApprovalUseNew', 'ApprovalService');
        $form = json_decode($request->form);
        if ($checkUseNewApproval == '1') {
            $approvalEngineService = new ApprovalEngineService();
            $header = ResvHeader::where('U_DocEntry', '=', $form->U_DocEntry)->first();
            $approvalList = $approvalEngineService->approvalList($header);
            if (empty($approvalList)) {
                $cherry_token = $request->user()->cherry_token;

                $headers = [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ];

                $documents = Http::withHeaders($headers)
                ->post(config('app.cherry_service_req'), [
                    'CommandName' => 'GetList',
                    'ModelCode' => 'GADocuments',
                    'UserName' => $request->user()->username,
                    'Token' => $cherry_token,
                    'OrderBy' => 'InsertStamp',
                    'OrderDirection' => 'Asc',
                    'ParameterData' => [
                        [
                            'ParamKey' => 'DocumentReferenceID',
                            'ParamValue' => (isset($form->DocNum)) ? $form->DocNum : $form->paper_no,
                            'Operator' => 'eq'
                        ]
                    ]
                ]);

                // return response()->json($documents->collect(), 422);

                $collect = $documents->collect();

                if ($collect['MessageType'] == 'error') {
                    throw new \Exception($collect['Message'], 1);
                }
                if ($collect['Data']) {
                    // return response()->json($collect, 422);
                    $list_code = Http::post(config('app.cherry_service_req'), [
                        'CommandName' => 'GetList',
                        'ModelCode' => 'ApprovalRequests',
                        'UserName' => $request->user()->username,
                        'Token' => $cherry_token,
                        'ParameterData' => [
                            [
                                'ParamKey' => 'ModelEntityCode',
                                'ParamValue' => $collect['Data'][0]['Code'],
                                'Operator' => 'eq'
                            ]
                        ]
                    ]);

                    //return response()->json($collect);

                    $arr_result = [];
                    foreach ($list_code->collect()['Data'] as $datum) {
                        $arr_result[] = [
                            'Keys' => $datum['Code'],
                            'ApproverEmployeeName' => $datum['ApproverEmployeeName'],
                            'StatusId' => $datum['StatusId'],
                            'ResponseDate' => $datum['ResponseDate'],
                            'ResponseDates' => ($datum['ResponseDate']) ?
                                date('Y-m-d H:i:s', (int) substr($datum['ResponseDate'], 6, 10)) : '',
                            'Notes' => $datum['ApprovalNotes'],
                            'ApprovalSchemaName' => $datum['ApprovalSchemaName'],
                        ];
                    }

                    return response()->json([
                        'rows' => $arr_result,
                        'total' => count($list_code->collect()['Data'])
                    ]);
                }
            }
            $listCodeResponse = $approvalEngineService->approvalDetail($header);

            $arr_result = [];
            foreach ($listCodeResponse['Data']["Approvals"] as $datum) {
                $arr_result[] = [
                    'Keys' => $datum['ApproverId'],
                    'ApproverEmployeeName' => $datum['ResponseByName'] ??  $datum['ApproverName'],
                    'StatusId' => $datum['Status'],
                    'ResponseDate' => $datum['ResponseAt'],
                    'ResponseDates' => $datum['ResponseAt'],
                    'Notes' => $datum['ResponseNote'],
                    'ApprovalSchemaName' => $listCodeResponse['Data']["SchemaName"],
                ];
            }

            return response()->json([
                'rows' => $arr_result,
                'datum' => $listCodeResponse,
                'total' => count($listCodeResponse['Data'])
            ]);
        } else {
            $cherry_token = $request->user()->cherry_token;

            $headers = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ];

            $documents = Http::withHeaders($headers)
                ->post(config('app.cherry_service_req'), [
                    'CommandName' => 'GetList',
                    'ModelCode' => 'GADocuments',
                    'UserName' => $request->user()->username,
                    'Token' => $cherry_token,
                    'OrderBy' => 'InsertStamp',
                    'OrderDirection' => 'Asc',
                    'ParameterData' => [
                        [
                            'ParamKey' => 'DocumentReferenceID',
                            'ParamValue' => (isset($form->DocNum)) ? $form->DocNum : $form->paper_no,
                            'Operator' => 'eq'
                        ]
                    ]
                ]);

            // return response()->json($documents->collect(), 422);

            $collect = $documents->collect();

            if ($collect['MessageType'] == 'error') {
                throw new \Exception($collect['Message'], 1);
            }
            if ($collect['Data']) {
                // return response()->json($collect, 422);
                $list_code = Http::post(config('app.cherry_service_req'), [
                    'CommandName' => 'GetList',
                    'ModelCode' => 'ApprovalRequests',
                    'UserName' => $request->user()->username,
                    'Token' => $cherry_token,
                    'ParameterData' => [
                        [
                            'ParamKey' => 'ModelEntityCode',
                            'ParamValue' => $collect['Data'][0]['Code'],
                            'Operator' => 'eq'
                        ]
                    ]
                ]);

                //return response()->json($collect);

                $arr_result = [];
                foreach ($list_code->collect()['Data'] as $datum) {
                    $arr_result[] = [
                        'Keys' => $datum['Code'],
                        'ApproverEmployeeName' => $datum['ApproverEmployeeName'],
                        'StatusId' => $datum['StatusId'],
                        'ResponseDate' => $datum['ResponseDate'],
                        'ResponseDates' => ($datum['ResponseDate']) ?
                            date('Y-m-d H:i:s', (int) substr($datum['ResponseDate'], 6, 10)) : '',
                        'Notes' => $datum['ApprovalNotes'],
                        'ApprovalSchemaName' => $datum['ApprovalSchemaName'],
                    ];
                }

                return response()->json([
                    'rows' => $arr_result,
                    'total' => count($list_code->collect()['Data'])
                ]);
            } else {
                return response()->json([
                    'rows' => [],
                    'total' => 0
                ]);
            }
        }
    }
}
