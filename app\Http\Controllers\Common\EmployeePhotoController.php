<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class EmployeePhotoController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $getPhotoCherry = $this->cherryGetPhoto($user);
        $photoBase64 = 'data:image/png;base64,' . $getPhotoCherry;

        return $this->success($photoBase64);
    }

    private function cherryGetPhoto(User $employee)
    {
        $randomInt = (int) rand(10000000000000, 99999999999999);
        $paramsQuery = http_build_query([
            'ticks' => $randomInt
        ]);
        $paramsBody = [
            "CommandName" => "GetEmployeePhoto",
            "ModelCode" => $employee->employee_code,
            "ParameterData" => [],
            "UserName" => $employee->username,
            "Token" => $employee->cherry_token
        ];
        $url = $this->getConfigByName('CHERRY_GET_EMPLOYEE_PHOTO', 'CHERRY') . "?" . $paramsQuery;
        $response = Http::withoutVerifying()
            ->post($url, $paramsBody);

        return $response->body();
    }
}
