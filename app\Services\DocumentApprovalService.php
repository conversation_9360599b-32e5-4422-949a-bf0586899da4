<?php

namespace App\Services;

use App\Models\Approval\Approval;
use App\Models\Approval\ApprovalApprover;
use App\Models\Approval\ApprovalRule;
use App\Models\Approval\ApprovalStage;
use App\Models\Document\Document;
use App\Notifications\ApprovalMeteraiRequest;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Notification;

class DocumentApprovalService
{
    /**
     * Sends a notification to approvers based on the approval rules set in the database.
     *
     * @param int $count_document The number of invoices waiting for approval.
     * @param array $list_doc The list of invoices waiting for approval.
     * @param array $cc_email The list of email addresses that will receive a copy of the notification.
     * @throws \Exception If there is an error retrieving the approval rules or sending the notification.
     */
    public function sendNotificationToApprover($count_document, $list_doc, $cc_email)
    {
        $rules = ApprovalRule::all();
        foreach ($rules as $key => $rule) {
            if ($rule->name == 'Document Type') {
                $array_value = $rule->value;
                $approval = Approval::find($rule->approval_id);
                if ($approval) {
                    $approvers = ApprovalApprover::leftJoin('users', 'users.id', 'approval_approver.user_id')
                        ->where('approval_id', $approval->id)
                        ->orderBy('approval_approver.sequence')
                        ->get();

                    foreach ($approvers as $index => $approver) {
                        // $requester = ViewEmployee::where('Nik', $document->userCreate->username)->first();
                        $project = [
                            'greeting' => 'Dear ' . $approver->name . ',',
                            'body' => $count_document . ': Invoices waiting for approval',
                            'subject' => $count_document . ': Invoices waiting for approval',
                            'inv' => $list_doc,
                            'status' => 'Pending',
                            'to' => '<EMAIL>',
                            'cc' => array_unique($cc_email)
                        ];

                        if ($approver->username == 'ADWIN') {
                            Notification::route('mail', '<EMAIL>')
                                ->notify(new ApprovalMeteraiRequest($project));
                        } else {
                            Notification::route('mail', $approver->email)
                                ->notify(new ApprovalMeteraiRequest($project));
                        }
                        // Notification::route('mail', '<EMAIL>')
                    }
                }
            }
        }
    }
    /**
     * Processes the approval submission for a given document and submits it for approval.
     *
     * @param mixed $requestData The request data containing the document ID.
     * @param mixed $request The request object containing the arrayMaterai and arrayEsign.
     * @param mixed $type The type of document being submitted.
     * @throws \Exception When the attachment is empty or the coordinate is not provided.
     * @return array The error status of the submission.
     */
    public function processSubmitApproval($requestData, $request, $type): array
    {
        $service = new ApprovalService();
        $id = $requestData->id;
        $document = Document::find($id);
        // $arrayMaterai = $request->arrayMaterai;
        // $arrayEsign = $request->arrayEsign;

        if (!$document->attachment()->exists()) {
            return [
                'error' => true,
                'message' => 'Document number ' . $document->document_number . ': Attachment cannot empty!'
            ];
        }

        if ($document->digisign_coordinate == 'Y' && empty($document->vis_digisign_llx)) {
            return [
                'error' => true,
                'message' => 'Document number ' . $document->document_number . ': Sign Coordinate cannot empty!'
            ];
        }

        if ($document->meterai_coordinate == 'Y' && empty($document->vis_llx)) {
            return [
                'error' => true,
                'message' => 'Document number ' . $document->document_number . ': Meterai Coordinate cannot empty!'
            ];
        }

        if ($document->document_type == 'internal') {
            if (!$document->digisign_id) {
                return [
                    'error' => true,
                    'message' => 'Document number ' . $document->document_number . ': Internal Coordinate cannot empty!'
                ];
            }
        }

        // throw new \Exception(json_encode($document));

        $service->submitApproval('Document Type', $document, $type, $request);

        $document->status = 'pending';
        $document->save();

        return [
            'error' => false,
            'message' => 'Approval send!'
        ];
    }

    /**
     * Processes the cancellation of a document and updates the approval stage and document status.
     *
     * @param mixed $requestData The data for the request to cancel a document.
     * @param mixed $request The request object containing the data to cancel the document.
     * @throws \Exception When an error occurs while processing the cancellation.
     * @return string A message indicating that the document has been canceled.
     */
    public function processCancel($requestData, $request)
    {
        DB::beginTransaction();
        try {
            // $rows = json_decode(json_encode($request->rows), FALSE);
            // $formApproval = json_decode(json_encode($request->formApproval), FALSE);
            $doc_id = $requestData->id;
            $document = Document::where('id', $doc_id)->first();

            // throw new \Exception($doc_id);

            // if ($request->user()->hasAnyRole(['E-Sign Cherry Approval'])) {
            if ($document->userCreate->hasAnyRole(['E-Sign Cherry Approval'])) {
                $this->cancelApprovalCherry($request, $document);
            } else {
                ApprovalStage::where('document_id', $doc_id)
                    ->update([
                        'status' => 'canceled',
                        'cancel_by' => $request->user()->id,
                        'cancel_date' => Carbon::now()
                    ]);
            }

            Document::where('id', $doc_id)
                ->update([
                    'status' => 'canceled'
                ]);
            DB::commit();
            return 'document canceled';
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    public function processDelete($requestData, $request)
    {
        DB::beginTransaction();
        try {
            // $rows = json_decode(json_encode($request->rows), FALSE);
            // $formApproval = json_decode(json_encode($request->formApproval), FALSE);
            $doc_id = $requestData->id;
            $document = Document::where('id', $doc_id)->delete();

            DB::commit();
            return 'document canceled';
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    protected function cancelApprovalCherry($request, $document)
    {
        $cherry_token = $request->user()->cherry_token;

        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json'
        ];

        $documents = Http::withHeaders($headers)
            ->post(config('app.cherry_service_req'), [
                'CommandName' => 'GetList',
                'ModelCode' => 'GADocuments',
                'UserName' => $request->user()->username,
                'Token' => $cherry_token,
                'ParameterData' => [
                    [
                        'ParamKey' => 'DocumentReferenceID',
                        'ParamValue' => $document->document_number,
                        'Operator' => 'eq'
                    ]
                ]
            ]);

        $collect = $documents->collect();

        Http::post(config('app.cherry_service_req'), [
            'CommandName' => 'Remove',
            'ModelCode' => 'GADocuments',
            'UserName' => $request->user()->username,
            'Token' => $cherry_token,
            'ModelData' => $collect['Data'][0]
        ]);
    }
}
